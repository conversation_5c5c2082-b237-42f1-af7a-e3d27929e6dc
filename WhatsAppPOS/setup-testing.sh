#!/bin/bash

# WhatsApp POS Tanzania - Complete Testing Setup Script
echo "🇹🇿 Setting up WhatsApp POS Tanzania for Testing..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
echo ""
echo "🔍 Checking Prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ required. Current version: $(node --version)"
    exit 1
fi
print_status "Node.js version: $(node --version)"

# Check npm
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi
print_status "npm version: $(npm --version)"

# Check if MongoDB is running
if ! command -v mongosh &> /dev/null && ! command -v mongo &> /dev/null; then
    print_warning "MongoDB CLI not found. Please ensure MongoDB is installed and running."
else
    if mongosh --eval "db.runCommand('ping')" &> /dev/null || mongo --eval "db.runCommand('ping')" &> /dev/null; then
        print_status "MongoDB is running"
    else
        print_warning "MongoDB is not running. Please start MongoDB service."
    fi
fi

# Check if Redis is running
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        print_status "Redis is running"
    else
        print_warning "Redis is not running. Please start Redis service."
    fi
else
    print_warning "Redis CLI not found. Please ensure Redis is installed and running."
fi

# Install dependencies
echo ""
echo "📦 Installing Dependencies..."
npm install
if [ $? -eq 0 ]; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Setup environment file
echo ""
echo "📝 Setting up Environment Configuration..."
if [ ! -f .env ]; then
    cp .env.testing .env
    print_status "Created .env file from testing template"
else
    print_warning ".env file already exists. Backing up to .env.backup"
    cp .env .env.backup
    cp .env.testing .env
    print_status "Updated .env file with testing configuration"
fi

# Create necessary directories
echo ""
echo "📁 Creating Directories..."
mkdir -p logs
mkdir -p uploads
mkdir -p database/seeds
print_status "Created necessary directories"

# Setup ngrok for webhook testing
echo ""
echo "🌐 Setting up ngrok for Webhook Testing..."
if command -v ngrok &> /dev/null; then
    print_status "ngrok is already installed"
else
    print_warning "ngrok is not installed. Please install ngrok for webhook testing:"
    print_info "Visit: https://ngrok.com/download"
    print_info "Or install via: npm install -g ngrok"
fi

# Create test data seeder
echo ""
echo "🌱 Creating Test Data Seeder..."
cat > database/seeds/testData.js << 'EOF'
/**
 * Test Data Seeder for WhatsApp POS Tanzania
 */

const mongoose = require('mongoose');
const { Business, User, Product, Customer } = require('../../backend/models');

async function seedTestData() {
  console.log('🌱 Seeding test data...');
  
  try {
    // Clear existing test data
    await Business.deleteMany({ name: { $regex: /test/i } });
    await User.deleteMany({ name: { $regex: /test/i } });
    await Product.deleteMany({ name: { $regex: /test/i } });
    await Customer.deleteMany({ name: { $regex: /test/i } });
    
    // Create test business
    const testBusiness = new Business({
      name: 'Test Duka la Mama',
      phone: '+255700000001',
      owner_name: 'Test Owner',
      business_type: 'duka',
      location: {
        region: 'Dar es Salaam',
        district: 'Kinondoni',
        ward: 'Msasani',
        street: 'Test Street'
      },
      settings: {
        language: 'sw',
        currency_format: 'TSh',
        low_stock_threshold: 5
      }
    });
    
    await testBusiness.save();
    console.log('✅ Created test business');
    
    // Create test owner user
    const testOwner = new User({
      business_id: testBusiness._id,
      phone: '+255700000001',
      name: 'Test Owner',
      role: 'owner',
      preferences: {
        language: 'sw'
      },
      created_by: testBusiness._id
    });
    
    await testOwner.save();
    console.log('✅ Created test owner user');
    
    // Create test products
    const testProducts = [
      {
        business_id: testBusiness._id,
        name: 'Coca Cola',
        price: 1000,
        stock: 50,
        category: 'drinks',
        created_by: testOwner._id
      },
      {
        business_id: testBusiness._id,
        name: 'Unga wa Ngano',
        price: 2500,
        stock: 20,
        category: 'food',
        created_by: testOwner._id
      },
      {
        business_id: testBusiness._id,
        name: 'Sabuni',
        price: 500,
        stock: 100,
        category: 'household',
        created_by: testOwner._id
      }
    ];
    
    await Product.insertMany(testProducts);
    console.log('✅ Created test products');
    
    // Create test customer
    const testCustomer = new Customer({
      business_id: testBusiness._id,
      name: 'Test Customer',
      phone: '+255700000002',
      created_by: testOwner._id
    });
    
    await testCustomer.save();
    console.log('✅ Created test customer');
    
    console.log('🎉 Test data seeded successfully!');
    console.log('📱 Test business phone: +255700000001');
    console.log('👤 Test owner: Test Owner');
    console.log('🏪 Test business: Test Duka la Mama');
    
  } catch (error) {
    console.error('❌ Error seeding test data:', error);
  }
}

module.exports = { seedTestData };

// Run if called directly
if (require.main === module) {
  const mongoose = require('mongoose');
  
  mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/tanzanian_pos_test')
    .then(() => {
      console.log('📊 Connected to MongoDB');
      return seedTestData();
    })
    .then(() => {
      console.log('✅ Seeding complete');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}
EOF

print_status "Created test data seeder"

# Create testing checklist
echo ""
echo "📋 Creating Testing Checklist..."
cat > TESTING_CHECKLIST.md << 'EOF'
# WhatsApp POS Tanzania - Testing Checklist ✅

## Pre-Testing Setup
- [ ] Node.js 18+ installed
- [ ] MongoDB running locally
- [ ] Redis running locally
- [ ] Dependencies installed (`npm install`)
- [ ] Environment file configured (`.env`)
- [ ] ngrok installed for webhook testing
- [ ] Twilio account set up with WhatsApp sandbox

## Environment Configuration
- [ ] `.env` file created from `.env.testing`
- [ ] MongoDB URI configured for test database
- [ ] Redis URL configured
- [ ] JWT secrets set
- [ ] Twilio credentials added
- [ ] Webhook URL configured with ngrok

## Database Setup
- [ ] MongoDB test database created
- [ ] Test data seeded (`npm run seed:test`)
- [ ] Database connection verified

## Server Testing
- [ ] Server starts without errors (`npm run dev`)
- [ ] Health check endpoint responds (`GET /health`)
- [ ] Dashboard loads (`http://localhost:3000`)
- [ ] API endpoints accessible (`GET /api`)

## WhatsApp Bot Testing (Development)
- [ ] Test webhook endpoint works (`POST /webhook/test/whatsapp`)
- [ ] Help command responds (`Body: "msaada"`)
- [ ] English help command responds (`Body: "help"`)
- [ ] Business registration flow works
- [ ] Product management commands work
- [ ] Sales recording commands work

## WhatsApp Bot Testing (Production with Twilio)
- [ ] ngrok tunnel established
- [ ] Twilio webhook URL configured
- [ ] WhatsApp sandbox joined
- [ ] Send "msaada" to sandbox number
- [ ] Receive help message in Swahili
- [ ] Send "help" and receive English response
- [ ] Complete business registration via WhatsApp
- [ ] Add product via WhatsApp
- [ ] Record sale via WhatsApp
- [ ] Get daily report via WhatsApp

## API Testing
- [ ] Business registration (`POST /api/auth/register`)
- [ ] User login (`POST /api/auth/login`)
- [ ] Product CRUD operations
- [ ] Sales recording
- [ ] Customer management
- [ ] Reports generation

## Security Testing
- [ ] JWT authentication required for protected routes
- [ ] Business data isolation enforced
- [ ] Rate limiting active
- [ ] Input validation working
- [ ] Webhook signature verification (production)

## Performance Testing
- [ ] Response times under 500ms
- [ ] Memory usage stable
- [ ] Concurrent requests handled
- [ ] Database queries optimized

## End-to-End Testing
- [ ] Complete business onboarding flow
- [ ] Multi-user business setup
- [ ] Product inventory management
- [ ] Sales workflow with payment methods
- [ ] Customer loyalty tracking
- [ ] Report generation and export

## Production Readiness
- [ ] Environment variables secured
- [ ] Logging configured
- [ ] Error handling comprehensive
- [ ] Health checks implemented
- [ ] Monitoring setup ready
EOF

print_status "Created testing checklist"

# Display next steps
echo ""
echo "🎉 Setup Complete! Next Steps:"
echo "=============================="
echo ""
print_info "1. Configure Twilio Credentials:"
echo "   - Edit .env file and add your Twilio Account SID and Auth Token"
echo "   - Get these from: https://console.twilio.com/"
echo ""
print_info "2. Start ngrok for webhook testing:"
echo "   ngrok http 3000"
echo "   - Copy the HTTPS URL (e.g., https://abc123.ngrok.io)"
echo "   - Update WEBHOOK_URL in .env file"
echo ""
print_info "3. Configure Twilio WhatsApp Sandbox:"
echo "   - Go to: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn"
echo "   - Set webhook URL: https://your-ngrok-url.ngrok.io/webhook/whatsapp"
echo "   - Join sandbox by sending code to +1 ************"
echo ""
print_info "4. Seed test data:"
echo "   npm run seed:test"
echo ""
print_info "5. Start the server:"
echo "   npm run dev"
echo ""
print_info "6. Run system tests:"
echo "   npm run test:system"
echo ""
print_info "7. Test WhatsApp bot:"
echo "   Send 'msaada' to +1 ************"
echo ""
print_info "8. Access dashboard:"
echo "   http://localhost:3000"
echo ""
print_warning "Important: Make sure MongoDB and Redis are running before starting the server!"
echo ""
print_status "Setup script completed successfully! 🚀"
