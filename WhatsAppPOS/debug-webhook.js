/**
 * Debug webhook to test what's happening with numbered options
 */

require('dotenv').config();
const express = require('express');
const { processMessage } = require('./backend/services/whatsappService');

const app = express();
app.use(express.urlencoded({ extended: true }));

app.post('/test-webhook', async (req, res) => {
  try {
    console.log('\n🔍 DEBUGGING WEBHOOK REQUEST:');
    console.log('Body:', req.body);
    
    const { From, Body, MessageSid } = req.body;
    const phoneNumber = From ? From.replace('whatsapp:', '') : '+255672622640';
    const messageBody = Body || 'test';
    
    console.log('📱 Phone:', phoneNumber);
    console.log('💬 Message:', messageBody);
    
    // Create test session
    const session = {
      phone: phoneNumber,
      step: 'welcome',
      language: 'en',
      data: {}
    };
    
    console.log('🔄 Processing message...');
    const response = await processMessage(phoneNumber, messageBody, session);
    
    console.log('📤 Response:', response);
    console.log('✅ Processing completed');
    
    res.status(200).json({
      success: true,
      response: response,
      session: session
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`🧪 Debug webhook server running on port ${PORT}`);
  console.log(`📡 Test URL: http://localhost:${PORT}/test-webhook`);
  console.log('\n🔧 Test commands:');
  console.log('curl -X POST http://localhost:3001/test-webhook -d "Body=9"');
  console.log('curl -X POST http://localhost:3001/test-webhook -d "Body=help"');
  console.log('curl -X POST http://localhost:3001/test-webhook -d "Body=register"');
});
