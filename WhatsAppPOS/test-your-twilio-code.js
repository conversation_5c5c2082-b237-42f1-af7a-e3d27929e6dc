/**
 * Test Your Exact Twilio Code Pattern
 * This replicates your exact code to test the integration
 */

// Your exact code pattern:
const accountSid = '**********************************';
const authToken = 'ddfc85dc652a370ceb0fd90add0ce70d'; // Your actual auth token
const client = require('twilio')(accountSid, authToken);

console.log('🧪 Testing Your Exact Twilio Code Pattern...');
console.log(`📱 Account SID: ${accountSid}`);
console.log(`🔑 Auth Token: ${authToken.substring(0, 8)}...`);

// Test the exact message you provided
async function testYourExactCode() {
  console.log('\n📤 Testing your exact Twilio code...');
  
  try {
    const message = await client.messages.create({
      from: 'whatsapp:+***********',
      contentSid: 'HXb5b62575e6e4ff6129ad7c8efe1f983e',
      contentVariables: '{"1":"12/1","2":"3pm"}',
      to: 'whatsapp:+************'
    });
    
    console.log('✅ Message sent successfully!');
    console.log(`📨 Message SID: ${message.sid}`);
    console.log(`📱 Status: ${message.status}`);
    console.log(`🕐 Date Created: ${message.dateCreated}`);
    console.log(`💰 Price: ${message.price} ${message.priceUnit}`);
    
    return true;
  } catch (error) {
    console.error('❌ Failed to send message:', error.message);
    console.error('🔍 Error Code:', error.code);
    console.error('📋 Error Details:', error.moreInfo);
    
    if (error.message.includes('Authenticate')) {
      console.log('\n💡 Solution: Replace [AuthToken] with your actual Twilio auth token');
    }
    
    return false;
  }
}

// Test with WhatsApp POS Tanzania welcome message
async function testWhatsAppPOSMessage() {
  console.log('\n📤 Testing WhatsApp POS Tanzania welcome message...');
  
  try {
    const message = await client.messages.create({
      from: 'whatsapp:+***********',
      body: `🏪 *KARIBU WHATSAPP POS TANZANIA*

Mfumo wa kisasa wa mauzo kwa biashara za Tanzania!

📋 *AMRI ZA MSINGI:*
• *jiunge* - Kusajili biashara mpya
• *msaada* - Kupata msaada  
• *bidhaa* - Kuona bidhaa zako
• *mauzo* - Kurekodi mauzo
• *ripoti* - Kuona ripoti za mauzo

🎯 *FAIDA:*
✅ Usimamizi wa bidhaa
✅ Ufuatiliaji wa mauzo  
✅ Ripoti za kila siku
✅ Usimamizi wa wateja
✅ Kumbusho za stock

Andika *jiunge* kuanza!`,
      to: 'whatsapp:+************'
    });
    
    console.log('✅ WhatsApp POS message sent successfully!');
    console.log(`📨 Message SID: ${message.sid}`);
    console.log(`📱 Status: ${message.status}`);
    
    return true;
  } catch (error) {
    console.error('❌ Failed to send WhatsApp POS message:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Twilio Integration Tests...\n');
  
  // Check if auth token is placeholder
  if (authToken === '[AuthToken]') {
    console.log('⚠️  WARNING: Auth token is still placeholder');
    console.log('📝 Please replace [AuthToken] with your actual Twilio auth token');
    console.log('🔧 Update the authToken variable in this file or in .env');
    console.log('\n🧪 Running tests anyway to show expected behavior...\n');
  }
  
  const results = {
    exactCode: await testYourExactCode(),
    whatsappPOS: await testWhatsAppPOSMessage()
  };
  
  console.log('\n📊 TEST RESULTS:');
  console.log('================');
  console.log(`Your Exact Code: ${results.exactCode ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`WhatsApp POS Message: ${results.whatsappPOS ? '✅ PASS' : '❌ FAIL'}`);
  
  const passCount = Object.values(results).filter(Boolean).length;
  console.log(`\n🎯 Overall: ${passCount}/2 tests passed`);
  
  if (passCount === 2) {
    console.log('🎉 Perfect! Twilio integration is working flawlessly!');
    console.log('🚀 WhatsApp POS Tanzania is ready for production!');
  } else if (authToken === '[AuthToken]') {
    console.log('🔧 Replace [AuthToken] with your actual auth token to enable messaging');
    console.log('💡 Everything else is configured correctly!');
  } else {
    console.log('⚠️  Check your Twilio credentials and phone number verification');
  }
  
  console.log('\n📋 Next Steps:');
  console.log('1. Replace [AuthToken] with your actual Twilio auth token');
  console.log('2. Verify +************ is added to your Twilio WhatsApp sandbox');
  console.log('3. Set up webhook URL in Twilio console');
  console.log('4. Test complete WhatsApp POS workflow');
}

// Run the tests
runTests().catch(console.error);
