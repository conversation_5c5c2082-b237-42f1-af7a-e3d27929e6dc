# WhatsApp POS Tanzania - Complete Testing Guide 🇹🇿

## 🚀 Step-by-Step Testing Setup

### Phase 1: Environment Setup

#### 1.1 Prerequisites Installation
```bash
# Check Node.js version (requires 18+)
node --version

# Install MongoDB (if not installed)
# macOS: brew install mongodb-community
# Ubuntu: sudo apt install mongodb
# Windows: Download from mongodb.com

# Install Redis (if not installed)
# macOS: brew install redis
# Ubuntu: sudo apt install redis-server
# Windows: Download from redis.io

# Install ngrok for webhook testing
npm install -g ngrok
# Or download from: https://ngrok.com/download
```

#### 1.2 Project Setup
```bash
# Clone and setup project
git clone <repository>
cd WhatsAppPOS

# Run automated setup
chmod +x setup-testing.sh
./setup-testing.sh

# Or manual setup
npm install
cp .env.testing .env
```

#### 1.3 Database Setup
```bash
# Start MongoDB
mongod
# Or: brew services start mongodb-community (macOS)
# Or: sudo systemctl start mongod (Ubuntu)

# Start Redis
redis-server
# Or: brew services start redis (macOS)
# Or: sudo systemctl start redis (Ubuntu)

# Seed test data
npm run seed:test
```

### Phase 2: Twilio WhatsApp Sandbox Configuration

#### 2.1 Create Twilio Account
1. Go to [Twilio Console](https://console.twilio.com/)
2. Sign up for free account
3. Navigate to **Console Dashboard**
4. Note your **Account SID** and **Auth Token**

#### 2.2 Configure WhatsApp Sandbox
1. Go to **Develop → Messaging → Try it out → Send a WhatsApp message**
2. Note the sandbox number: **+1 **************
3. Join sandbox by sending the join code to +1 ************
4. You'll receive confirmation message

#### 2.3 Setup Webhook URL
```bash
# Start ngrok in new terminal
ngrok http 3000

# Copy the HTTPS URL (e.g., https://abc123.ngrok.io)
# Update .env file:
WEBHOOK_URL=https://abc123.ngrok.io/webhook/whatsapp
```

#### 2.4 Configure Twilio Webhook
1. In Twilio Console, go to **WhatsApp Sandbox Settings**
2. Set **Webhook URL**: `https://your-ngrok-url.ngrok.io/webhook/whatsapp`
3. Set **HTTP Method**: POST
4. Save configuration

#### 2.5 Update Environment Variables
```bash
# Edit .env file with your Twilio credentials
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
WEBHOOK_URL=https://your-ngrok-url.ngrok.io/webhook/whatsapp
```

### Phase 3: System Testing

#### 3.1 Start the System
```bash
# Terminal 1: Start ngrok
ngrok http 3000

# Terminal 2: Start the server
npm run dev

# Terminal 3: Run tests
npm run test:system
```

#### 3.2 Verify System Health
```bash
# Check server health
curl http://localhost:3000/health

# Check dashboard
open http://localhost:3000

# Check API
curl http://localhost:3000/api
```

### Phase 4: WhatsApp Bot Testing

#### 4.1 Development Testing (No Twilio Required)
```bash
# Test basic webhook functionality
npm run test:whatsapp

# Interactive testing
npm run test:whatsapp:interactive

# Manual webhook test
curl -X POST http://localhost:3000/webhook/test/whatsapp \
  -H "Content-Type: application/json" \
  -d '{
    "From": "whatsapp:+************",
    "Body": "msaada"
  }'
```

#### 4.2 Production Testing (With Twilio)
Send these messages to **+1 **************:

**Swahili Commands:**
```
msaada          → Get help in Swahili
sajili          → Register business
ongeza bidhaa   → Add product
bidhaa          → List products
uza             → Record sale
mauzo ya leo    → Today's report
ongeza mteja    → Add customer
wateja          → List customers
```

**English Commands:**
```
help            → Get help in English
register        → Register business
add product     → Add product
products        → List products
sell            → Record sale
today sales     → Today's report
add customer    → Add customer
customers       → List customers
```

### Phase 5: Complete Flow Testing

#### 5.1 Business Registration Flow
1. Send: `sajili` (Swahili) or `register` (English)
2. Enter business name: `Duka la Mama Juma`
3. Select business type: `1` (for Duka)
4. Enter owner name: `Mama Juma`
5. Verify registration success message

#### 5.2 Product Management Flow
1. Send: `ongeza bidhaa` or `add product`
2. Enter product name: `Coca Cola`
3. Enter price: `1000`
4. Enter stock: `50`
5. Select category: `2` (for drinks)
6. Verify product added successfully

#### 5.3 Sales Recording Flow
1. Send: `uza` or `sell`
2. Select product: `1` (first product)
3. Enter quantity: `2`
4. Select payment method: `1` (cash)
5. Verify sale recorded with receipt

#### 5.4 Reports Testing
1. Send: `mauzo ya leo` or `today sales`
2. Verify daily report with sales data
3. Send: `bidhaa` or `products`
4. Verify product list with updated stock

### Phase 6: API Testing

#### 6.1 Authentication Testing
```bash
# Register business via API
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "API Test Duka",
    "phone": "+255700000003",
    "owner_name": "API Test Owner",
    "business_type": "duka"
  }'

# Login and get token
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+255700000003"
  }'
```

#### 6.2 Product Management Testing
```bash
# Add product (use token from login)
curl -X POST http://localhost:3000/api/products \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Test Product",
    "price": 1500,
    "stock": 30,
    "category": "food"
  }'

# List products
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/products
```

#### 6.3 Sales Recording Testing
```bash
# Record sale
curl -X POST http://localhost:3000/api/sales \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "items": [
      {
        "product_id": "PRODUCT_ID_HERE",
        "quantity": 2
      }
    ],
    "payment_method": "mpesa"
  }'
```

### Phase 7: Performance & Security Testing

#### 7.1 Load Testing
```bash
# Install artillery for load testing
npm install -g artillery

# Test webhook endpoint
artillery quick --count 10 --num 5 \
  http://localhost:3000/webhook/test/whatsapp

# Test API endpoints
artillery quick --count 20 --num 10 \
  http://localhost:3000/health
```

#### 7.2 Security Testing
```bash
# Test rate limiting
for i in {1..20}; do
  curl http://localhost:3000/api/products
done

# Test authentication
curl http://localhost:3000/api/products
# Should return 401 Unauthorized

# Test business isolation
# Try accessing another business's data with valid token
```

## 🔧 Troubleshooting Guide

### Common Issues & Solutions

#### Server Won't Start
```bash
# Check if port is in use
lsof -i :3000
kill -9 <PID>

# Check environment variables
node -e "console.log(process.env.MONGODB_URI)"
```

#### MongoDB Connection Issues
```bash
# Check MongoDB status
mongosh --eval "db.runCommand('ping')"

# Restart MongoDB
brew services restart mongodb-community  # macOS
sudo systemctl restart mongod           # Ubuntu
```

#### Redis Connection Issues
```bash
# Check Redis status
redis-cli ping

# Restart Redis
brew services restart redis              # macOS
sudo systemctl restart redis            # Ubuntu
```

#### WhatsApp Webhook Issues
```bash
# Check ngrok status
curl https://your-ngrok-url.ngrok.io/health

# Check Twilio webhook logs
# Go to Twilio Console → Monitor → Logs → Errors

# Test webhook locally
curl -X POST http://localhost:3000/webhook/test/whatsapp \
  -H "Content-Type: application/json" \
  -d '{"From": "whatsapp:+************", "Body": "test"}'
```

## ✅ Testing Checklist

### Pre-Testing Setup
- [ ] Node.js 18+ installed
- [ ] MongoDB running
- [ ] Redis running
- [ ] Dependencies installed
- [ ] Environment configured
- [ ] ngrok installed and running
- [ ] Twilio account setup

### System Testing
- [ ] Server starts without errors
- [ ] Health check passes
- [ ] Dashboard loads
- [ ] API responds
- [ ] Database connections work

### WhatsApp Testing
- [ ] Webhook receives messages
- [ ] Help commands work (both languages)
- [ ] Business registration flow complete
- [ ] Product management works
- [ ] Sales recording works
- [ ] Reports generation works
- [ ] Language switching works

### Production Readiness
- [ ] All tests pass
- [ ] Performance acceptable
- [ ] Security measures active
- [ ] Error handling works
- [ ] Logging configured

## 🎉 Success Criteria

Your system is ready when:
1. ✅ All automated tests pass
2. ✅ WhatsApp bot responds in both languages
3. ✅ Complete business flows work end-to-end
4. ✅ API endpoints function correctly
5. ✅ Dashboard displays data properly
6. ✅ Performance meets requirements
7. ✅ Security measures are active

## 📞 Next Steps

Once testing is complete:
1. **Production Deployment**: Deploy to cloud platform
2. **Domain Setup**: Configure custom domain
3. **SSL Certificates**: Enable HTTPS
4. **Monitoring**: Set up logging and alerts
5. **Backup Strategy**: Configure data backups
6. **User Training**: Train business owners
7. **Go Live**: Launch for real businesses!

🇹🇿 **Hongera! Your WhatsApp POS Tanzania system is ready!** 🎉
