#!/bin/bash

# WhatsApp POS Tanzania - Production Deployment Script
# This script helps deploy the application to production with proper checks

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "This script should not be run as root for security reasons"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    print_info "Checking system requirements..."
    
    # Check Node.js version
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    REQUIRED_VERSION="18.0.0"
    
    if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
        print_error "Node.js version $NODE_VERSION is too old. Required: $REQUIRED_VERSION+"
        exit 1
    fi
    
    print_status "Node.js version $NODE_VERSION is compatible"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    # Check MongoDB
    if ! command -v mongod &> /dev/null; then
        print_warning "MongoDB is not installed locally. Make sure you have access to a MongoDB instance."
    fi
    
    # Check Redis
    if ! command -v redis-server &> /dev/null; then
        print_warning "Redis is not installed locally. Make sure you have access to a Redis instance."
    fi
    
    # Check OpenSSL for certificate generation
    if ! command -v openssl &> /dev/null; then
        print_error "OpenSSL is not installed. Required for SSL certificate management."
        exit 1
    fi
}

# Validate environment file
validate_environment() {
    print_info "Validating production environment configuration..."
    
    if [[ ! -f .env.production ]]; then
        print_error ".env.production file not found"
        print_info "Please copy .env.production template and configure it:"
        print_info "cp .env.production.template .env.production"
        exit 1
    fi
    
    # Check for placeholder values
    if grep -q "REPLACE-WITH-" .env.production; then
        print_error "Found placeholder values in .env.production"
        print_info "Please replace all REPLACE-WITH- values with actual configuration"
        grep "REPLACE-WITH-" .env.production
        exit 1
    fi
    
    if grep -q "your-.*-here" .env.production; then
        print_error "Found placeholder values in .env.production"
        print_info "Please replace all placeholder values with actual configuration"
        grep "your-.*-here" .env.production
        exit 1
    fi
    
    print_status "Environment configuration appears valid"
}

# Install dependencies
install_dependencies() {
    print_info "Installing production dependencies..."
    
    # Install backend dependencies
    npm ci --only=production
    
    # Install dashboard dependencies if React dashboard exists
    if [[ -d "dashboard-react" ]]; then
        print_info "Installing React dashboard dependencies..."
        cd dashboard-react
        npm ci --only=production
        npm run build
        cd ..
        print_status "React dashboard built successfully"
    fi
    
    print_status "Dependencies installed successfully"
}

# Setup SSL certificates
setup_ssl() {
    print_info "Setting up SSL certificates..."
    
    SSL_DIR="/etc/ssl/whatsapp-pos"
    
    if [[ ! -d "$SSL_DIR" ]]; then
        print_warning "SSL directory $SSL_DIR does not exist"
        print_info "Creating SSL directory..."
        sudo mkdir -p "$SSL_DIR"
        sudo chown $USER:$USER "$SSL_DIR"
    fi
    
    # Check if certificates exist
    if [[ -f "$SSL_DIR/whatsapp-pos.crt" && -f "$SSL_DIR/whatsapp-pos.key" ]]; then
        print_status "SSL certificates found"
    else
        print_warning "SSL certificates not found"
        print_info "You can:"
        print_info "1. Use Let's Encrypt: certbot --nginx -d yourdomain.com"
        print_info "2. Use existing certificates: copy them to $SSL_DIR/"
        print_info "3. Generate self-signed for testing: npm run ssl:generate"
    fi
}

# Setup systemd service
setup_systemd() {
    print_info "Setting up systemd service..."
    
    SERVICE_FILE="/etc/systemd/system/whatsapp-pos.service"
    APP_DIR=$(pwd)
    
    sudo tee "$SERVICE_FILE" > /dev/null <<EOF
[Unit]
Description=WhatsApp POS Tanzania
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_DIR
Environment=NODE_ENV=production
ExecStart=/usr/bin/node backend/server.js
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=whatsapp-pos

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable whatsapp-pos
    
    print_status "Systemd service configured"
}

# Setup nginx reverse proxy
setup_nginx() {
    print_info "Setting up Nginx reverse proxy..."
    
    if ! command -v nginx &> /dev/null; then
        print_warning "Nginx is not installed"
        print_info "Install nginx: sudo apt install nginx"
        return
    fi
    
    NGINX_CONFIG="/etc/nginx/sites-available/whatsapp-pos"
    
    sudo tee "$NGINX_CONFIG" > /dev/null <<EOF
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/ssl/whatsapp-pos/whatsapp-pos.crt;
    ssl_certificate_key /etc/ssl/whatsapp-pos/whatsapp-pos.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF
    
    sudo ln -sf "$NGINX_CONFIG" /etc/nginx/sites-enabled/
    sudo nginx -t
    
    print_status "Nginx configuration created"
    print_warning "Remember to update server_name with your actual domain"
}

# Run deployment
main() {
    print_info "🚀 Starting WhatsApp POS Tanzania Production Deployment"
    
    check_root
    check_requirements
    validate_environment
    install_dependencies
    setup_ssl
    setup_systemd
    setup_nginx
    
    print_status "🎉 Deployment completed successfully!"
    print_info ""
    print_info "Next steps:"
    print_info "1. Update Nginx configuration with your domain name"
    print_info "2. Obtain SSL certificates (Let's Encrypt recommended)"
    print_info "3. Start the service: sudo systemctl start whatsapp-pos"
    print_info "4. Check status: sudo systemctl status whatsapp-pos"
    print_info "5. View logs: sudo journalctl -u whatsapp-pos -f"
    print_info ""
    print_warning "Don't forget to configure your firewall to allow ports 80 and 443"
}

# Run main function
main "$@"
