/**
 * WhatsApp POS Tanzania - System Test Script
 * Quick test to verify all components are working
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testSystem() {
  console.log('🧪 Testing WhatsApp POS Tanzania System...\n');
  
  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Check:', healthResponse.data.status);
    
    // Test 2: Dashboard Access
    console.log('\n2. Testing Dashboard Access...');
    const dashboardResponse = await axios.get(BASE_URL);
    console.log('✅ Dashboard accessible:', dashboardResponse.status === 200);
    
    // Test 3: API Endpoints
    console.log('\n3. Testing API Endpoints...');
    
    // Test business registration
    const businessData = {
      name: 'Test Duka',
      phone: '+255700000001',
      owner_name: 'Test Owner',
      business_type: 'duka',
      language: 'sw'
    };
    
    try {
      const registerResponse = await axios.post(`${BASE_URL}/api/auth/register`, businessData);
      console.log('✅ Business Registration:', registerResponse.data.success);
      
      const token = registerResponse.data.data.tokens.access_token;
      const headers = { Authorization: `Bearer ${token}` };
      
      // Test product creation
      const productData = {
        name: 'Test Product',
        price: 1000,
        stock: 50,
        category: 'food'
      };
      
      const productResponse = await axios.post(`${BASE_URL}/api/products`, productData, { headers });
      console.log('✅ Product Creation:', productResponse.data.success);
      
      // Test product listing
      const productsResponse = await axios.get(`${BASE_URL}/api/products`, { headers });
      console.log('✅ Product Listing:', productsResponse.data.success);
      
    } catch (error) {
      if (error.response?.data?.error?.includes('already exists')) {
        console.log('✅ Business Registration: (Already exists - OK)');
      } else {
        console.log('❌ API Test Error:', error.response?.data?.error || error.message);
      }
    }
    
    // Test 4: WhatsApp Webhook (Development)
    console.log('\n4. Testing WhatsApp Webhook...');
    try {
      const webhookData = {
        From: 'whatsapp:+255700000001',
        Body: 'msaada',
        MessageSid: 'TEST_' + Date.now()
      };
      
      const webhookResponse = await axios.post(`${BASE_URL}/webhook/test/whatsapp`, webhookData);
      console.log('✅ WhatsApp Webhook:', webhookResponse.status === 200);
    } catch (error) {
      console.log('❌ Webhook Test Error:', error.response?.data?.error || error.message);
    }
    
    console.log('\n🎉 System Test Complete!');
    console.log('\n📱 Ready to test with WhatsApp:');
    console.log('   Send "msaada" to your Twilio WhatsApp number');
    console.log('   Or use the test endpoint: POST /webhook/test/whatsapp');
    
  } catch (error) {
    console.error('❌ System Test Failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the server is running: npm start');
    console.log('2. Check MongoDB is running');
    console.log('3. Check Redis is running');
    console.log('4. Verify .env file is configured');
  }
}

// Run the test
if (require.main === module) {
  testSystem();
}

module.exports = testSystem;
