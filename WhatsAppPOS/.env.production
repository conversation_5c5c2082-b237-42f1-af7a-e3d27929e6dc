# WhatsApp POS Tanzania - Production Environment Configuration
# IMPORTANT: Update all placeholder values before deployment

# Application Environment
NODE_ENV=production
PORT=3000
HTTPS_PORT=443

# Database Configuration (Production)
# Replace with your production MongoDB connection string
MONGODB_URI=mongodb://localhost:27017/whatsapp_pos_production
MONGODB_TEST_URI=mongodb://localhost:27017/whatsapp_pos_test

# Redis Configuration (Production)
# Replace with your production Redis connection details
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password-here

# JWT Configuration (CRITICAL: Generate secure secrets)
# Use: openssl rand -base64 64
JWT_SECRET=REPLACE-WITH-SECURE-64-CHAR-SECRET-KEY-FOR-PRODUCTION
JWT_REFRESH_SECRET=REPLACE-WITH-DIFFERENT-SECURE-64-CHAR-REFRESH-SECRET
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Twilio WhatsApp Configuration (Production)
# Replace with your production Twilio credentials
TWILIO_ACCOUNT_SID=your-production-twilio-account-sid
TWILIO_AUTH_TOKEN=your-production-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+your-production-whatsapp-number
WEBHOOK_URL=https://yourdomain.com/webhook/whatsapp

# SSL/TLS Configuration
SSL_CERT_PATH=/etc/ssl/certs/whatsapp-pos.crt
SSL_KEY_PATH=/etc/ssl/private/whatsapp-pos.key
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt

# Application URLs (Production)
FRONTEND_URL=https://yourdomain.com
DASHBOARD_URL=https://yourdomain.com

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Business Configuration
DEFAULT_CURRENCY=TZS
DEFAULT_LANGUAGE=sw
DEFAULT_TIMEZONE=Africa/Dar_es_Salaam

# Rate Limiting (Production - More restrictive)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Subscription Configuration
TRIAL_DURATION_DAYS=14
BASIC_PLAN_PRICE=10000
PRO_PLAN_PRICE=25000

# Logging Configuration (Production)
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Security Configuration (Production)
BCRYPT_ROUNDS=12
SESSION_SECRET=REPLACE-WITH-SECURE-SESSION-SECRET

# Monitoring & APM
SENTRY_DSN=https://<EMAIL>/project-id
ENABLE_APM=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=whatsapp-pos-backups
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# Cache Configuration
CACHE_ENABLED=true
CACHE_TTL=300
CACHE_MAX_KEYS=1000

# Production Flags
DEBUG=false
ENABLE_SWAGGER=false
SKIP_WEBHOOK_VERIFICATION=false
ENABLE_TEST_ENDPOINTS=false
AUTO_SEED_DATABASE=false

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# Performance Configuration
MAX_CONNECTIONS=100
CONNECTION_TIMEOUT=30000
REQUEST_TIMEOUT=30000

# Webhook Security
WEBHOOK_SECRET=REPLACE-WITH-SECURE-WEBHOOK-SECRET

# Database Connection Pool
DB_MIN_POOL_SIZE=5
DB_MAX_POOL_SIZE=20
DB_MAX_IDLE_TIME=60000

# External Services
PAYMENT_GATEWAY_URL=https://api.paymentgateway.com
PAYMENT_GATEWAY_KEY=your-payment-gateway-key
PAYMENT_GATEWAY_SECRET=your-payment-gateway-secret

# Analytics
ANALYTICS_ENABLED=true
ANALYTICS_PROVIDER=google
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Feature Flags
ENABLE_ADVANCED_REPORTS=true
ENABLE_BULK_OPERATIONS=true
ENABLE_EXPORT_FEATURES=true
ENABLE_CUSTOMER_LOYALTY=true

# Compliance
DATA_RETENTION_DAYS=2555
GDPR_COMPLIANCE=true
AUDIT_LOG_ENABLED=true
