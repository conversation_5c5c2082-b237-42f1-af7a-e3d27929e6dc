/**
 * WhatsApp POS Tanzania - Comprehensive WhatsApp Testing Script
 * Tests all WhatsApp bot functionality in both Swahili and English
 */

const axios = require('axios');
const readline = require('readline');

const BASE_URL = 'http://localhost:3000';
const WEBHOOK_URL = `${BASE_URL}/webhook/test/whatsapp`;

// Test phone numbers
const TEST_PHONES = [
  '+255700000001', // Business owner
  '+255700000002', // Customer
  '+255700000003'  // Employee
];

// Test scenarios in both languages
const TEST_SCENARIOS = {
  swahili: [
    { command: 'msaada', description: 'Get help in Swahili' },
    { command: 'sajili', description: 'Register business in Swahili' },
    { command: 'ongeza bidhaa', description: 'Add product in Swahili' },
    { command: 'bidhaa', description: 'List products in Swahili' },
    { command: 'uza', description: 'Record sale in Swahili' },
    { command: 'mauzo ya leo', description: 'Today\'s report in Swahili' },
    { command: 'ongeza mteja', description: 'Add customer in Swahili' },
    { command: 'wateja', description: 'List customers in Swahili' }
  ],
  english: [
    { command: 'help', description: 'Get help in English' },
    { command: 'register', description: 'Register business in English' },
    { command: 'add product', description: 'Add product in English' },
    { command: 'products', description: 'List products in English' },
    { command: 'sell', description: 'Record sale in English' },
    { command: 'today sales', description: 'Today\'s report in English' },
    { command: 'add customer', description: 'Add customer in English' },
    { command: 'customers', description: 'List customers in English' }
  ]
};

// Business registration flow test data
const BUSINESS_REGISTRATION_FLOW = {
  swahili: [
    { step: 1, input: 'sajili', expected: 'jina la biashara' },
    { step: 2, input: 'Duka la Mama Juma', expected: 'aina ya biashara' },
    { step: 3, input: '1', expected: 'jina la mmiliki' },
    { step: 4, input: 'Mama Juma', expected: 'Hongera' }
  ],
  english: [
    { step: 1, input: 'register', expected: 'business name' },
    { step: 2, input: 'Mama Juma Shop', expected: 'business type' },
    { step: 3, input: '1', expected: 'owner' },
    { step: 4, input: 'Mama Juma', expected: 'Congratulations' }
  ]
};

// Product management flow test data
const PRODUCT_FLOW = {
  swahili: [
    { step: 1, input: 'ongeza bidhaa', expected: 'jina la bidhaa' },
    { step: 2, input: 'Coca Cola', expected: 'bei ya bidhaa' },
    { step: 3, input: '1000', expected: 'idadi ya bidhaa' },
    { step: 4, input: '50', expected: 'aina ya bidhaa' },
    { step: 5, input: '2', expected: 'imeongezwa' }
  ],
  english: [
    { step: 1, input: 'add product', expected: 'product name' },
    { step: 2, input: 'Coca Cola', expected: 'product price' },
    { step: 3, input: '1000', expected: 'product quantity' },
    { step: 4, input: '50', expected: 'product category' },
    { step: 5, input: '2', expected: 'added' }
  ]
};

class WhatsAppTester {
  constructor() {
    this.sessionData = new Map();
  }

  async sendMessage(phone, message) {
    try {
      const response = await axios.post(WEBHOOK_URL, {
        From: `whatsapp:${phone}`,
        Body: message,
        MessageSid: `TEST_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      });
      
      return {
        success: true,
        status: response.status,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message
      };
    }
  }

  async testBasicCommands() {
    console.log('🧪 Testing Basic Commands...\n');
    
    for (const [language, commands] of Object.entries(TEST_SCENARIOS)) {
      console.log(`📱 Testing ${language.toUpperCase()} commands:`);
      
      for (const { command, description } of commands) {
        const phone = TEST_PHONES[0];
        const result = await this.sendMessage(phone, command);
        
        if (result.success) {
          console.log(`  ✅ ${description}: OK`);
        } else {
          console.log(`  ❌ ${description}: FAILED - ${result.error}`);
        }
        
        // Small delay between commands
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      console.log('');
    }
  }

  async testBusinessRegistrationFlow(language = 'swahili') {
    console.log(`🏪 Testing Business Registration Flow (${language})...\n`);
    
    const flow = BUSINESS_REGISTRATION_FLOW[language];
    const phone = TEST_PHONES[Math.floor(Math.random() * TEST_PHONES.length)];
    
    for (const { step, input, expected } of flow) {
      console.log(`  Step ${step}: Sending "${input}"`);
      
      const result = await this.sendMessage(phone, input);
      
      if (result.success) {
        console.log(`  ✅ Step ${step}: Response received`);
        // In a real test, you'd check if the response contains expected keywords
      } else {
        console.log(`  ❌ Step ${step}: FAILED - ${result.error}`);
        break;
      }
      
      // Delay between steps
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    console.log('');
  }

  async testProductManagementFlow(language = 'swahili') {
    console.log(`📦 Testing Product Management Flow (${language})...\n`);
    
    const flow = PRODUCT_FLOW[language];
    const phone = TEST_PHONES[0]; // Use registered business phone
    
    for (const { step, input, expected } of flow) {
      console.log(`  Step ${step}: Sending "${input}"`);
      
      const result = await this.sendMessage(phone, input);
      
      if (result.success) {
        console.log(`  ✅ Step ${step}: Response received`);
      } else {
        console.log(`  ❌ Step ${step}: FAILED - ${result.error}`);
        break;
      }
      
      // Delay between steps
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    console.log('');
  }

  async testSalesFlow(language = 'swahili') {
    console.log(`💰 Testing Sales Recording Flow (${language})...\n`);
    
    const phone = TEST_PHONES[0];
    const sellCommand = language === 'swahili' ? 'uza' : 'sell';
    
    const salesFlow = [
      { input: sellCommand, description: 'Start sales process' },
      { input: '1', description: 'Select first product' },
      { input: '2', description: 'Enter quantity' },
      { input: '1', description: 'Select cash payment' }
    ];
    
    for (const { input, description } of salesFlow) {
      console.log(`  ${description}: Sending "${input}"`);
      
      const result = await this.sendMessage(phone, input);
      
      if (result.success) {
        console.log(`  ✅ ${description}: OK`);
      } else {
        console.log(`  ❌ ${description}: FAILED - ${result.error}`);
        break;
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    console.log('');
  }

  async testReportsFlow(language = 'swahili') {
    console.log(`📊 Testing Reports Flow (${language})...\n`);
    
    const phone = TEST_PHONES[0];
    const reportCommands = language === 'swahili' 
      ? ['mauzo ya leo', 'mauzo ya wiki', 'hisa']
      : ['today sales', 'weekly sales', 'stock'];
    
    for (const command of reportCommands) {
      console.log(`  Testing: "${command}"`);
      
      const result = await this.sendMessage(phone, command);
      
      if (result.success) {
        console.log(`  ✅ ${command}: OK`);
      } else {
        console.log(`  ❌ ${command}: FAILED - ${result.error}`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    console.log('');
  }

  async runFullTest() {
    console.log('🇹🇿 WhatsApp POS Tanzania - Comprehensive Testing\n');
    console.log('================================================\n');
    
    try {
      // Test server health first
      console.log('🔍 Checking server health...');
      const healthCheck = await axios.get(`${BASE_URL}/health`);
      console.log('✅ Server is healthy\n');
      
      // Run all tests
      await this.testBasicCommands();
      await this.testBusinessRegistrationFlow('swahili');
      await this.testBusinessRegistrationFlow('english');
      await this.testProductManagementFlow('swahili');
      await this.testSalesFlow('swahili');
      await this.testReportsFlow('swahili');
      
      console.log('🎉 All tests completed!\n');
      console.log('📋 Test Summary:');
      console.log('- Basic commands tested in both languages');
      console.log('- Business registration flow tested');
      console.log('- Product management flow tested');
      console.log('- Sales recording flow tested');
      console.log('- Reports generation tested');
      console.log('\n✅ WhatsApp bot is ready for production testing!');
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      console.log('\n🔧 Troubleshooting:');
      console.log('1. Make sure the server is running: npm run dev');
      console.log('2. Check if MongoDB and Redis are running');
      console.log('3. Verify .env configuration');
    }
  }

  async interactiveTest() {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    console.log('🎮 Interactive WhatsApp Testing Mode');
    console.log('=====================================\n');
    console.log('Enter WhatsApp messages to test the bot.');
    console.log('Type "exit" to quit, "help" for commands.\n');

    const askQuestion = () => {
      rl.question('📱 Message: ', async (message) => {
        if (message.toLowerCase() === 'exit') {
          rl.close();
          return;
        }

        if (message.toLowerCase() === 'help') {
          console.log('\n📋 Available test commands:');
          console.log('Swahili: msaada, sajili, ongeza bidhaa, uza, mauzo ya leo');
          console.log('English: help, register, add product, sell, today sales');
          console.log('Type "exit" to quit\n');
          askQuestion();
          return;
        }

        const result = await this.sendMessage(TEST_PHONES[0], message);
        
        if (result.success) {
          console.log('✅ Message sent successfully');
        } else {
          console.log('❌ Failed to send message:', result.error);
        }
        
        console.log('');
        askQuestion();
      });
    };

    askQuestion();
  }
}

// Command line interface
async function main() {
  const tester = new WhatsAppTester();
  
  const args = process.argv.slice(2);
  
  if (args.includes('--interactive') || args.includes('-i')) {
    await tester.interactiveTest();
  } else {
    await tester.runFullTest();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = WhatsAppTester;
