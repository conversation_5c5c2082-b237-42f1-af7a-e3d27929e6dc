{"name": "whatsapp-pos-tanzania", "version": "1.0.0", "description": "Complete WhatsApp POS system for Tanzanian businesses", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "seed": "node database/seeds/seedDatabase.js", "seed:test": "node database/seeds/testData.js", "test:system": "node test-system.js", "test:whatsapp": "node test-whatsapp.js", "test:whatsapp:interactive": "node test-whatsapp.js --interactive", "verify": "node verify-setup.js", "quick-start": "chmod +x quick-start.sh && ./quick-start.sh", "build": "npm run build:dashboard", "build:dashboard": "cd dashboard && npm run build", "deploy": "npm run build && npm run start"}, "keywords": ["whatsapp", "pos", "tanzania", "business", "swahili", "ecommerce"], "author": "WhatsApp POS Tanzania", "license": "MIT", "dependencies": {"aws-sdk": "^2.1692.0", "bcryptjs": "^2.4.3", "bull": "^4.11.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.43", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "pdfkit": "^0.13.0", "qrcode": "^1.5.3", "redis": "^4.6.7", "sharp": "^0.32.5", "twilio": "^4.19.0", "winston": "^3.10.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/jest": "^29.5.4", "artillery": "^2.0.0", "axios": "^1.5.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}