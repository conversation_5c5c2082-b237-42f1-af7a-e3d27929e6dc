# WhatsApp POS Tanzania - Testing Guide 🧪

## 🚀 Quick Start Testing

### 1. Prerequisites Check
```bash
# Check Node.js (requires 18+)
node --version

# Check npm
npm --version

# Check if MongoDB is running
mongosh --eval "db.runCommand('ping')"

# Check if <PERSON><PERSON> is running
redis-cli ping
```

### 2. Setup and Install
```bash
# Clone and setup
git clone <repository>
cd WhatsAppPOS

# Run setup script (Linux/Mac)
./setup.sh

# Or manual setup
npm install
cp .env.example .env
# Edit .env with your configuration
```

### 3. Start the System
```bash
# Option 1: Development mode
npm run dev

# Option 2: Production mode
npm start

# Option 3: Docker (includes MongoDB & Redis)
docker-compose up -d
```

### 4. Run System Test
```bash
# Automated system test
npm run test:system

# Manual health check
curl http://localhost:3000/health
```

## 📱 WhatsApp Testing

### Development Testing (No Twilio Required)
```bash
# Test WhatsApp webhook locally
curl -X POST http://localhost:3000/webhook/test/whatsapp \
  -H "Content-Type: application/json" \
  -d '{
    "From": "whatsapp:+************",
    "Body": "msaada"
  }'
```

### Production Testing (Requires Twilio)
1. **Setup Twilio WhatsApp Sandbox**:
   - Go to Twilio Console → WhatsApp → Sandbox
   - Note your sandbox number
   - Set webhook URL: `https://yourdomain.com/webhook/whatsapp`

2. **Test Commands**:
   ```
   Send to Twilio WhatsApp number:
   
   Swahili:
   - "msaada" (help)
   - "sajili" (register business)
   - "ongeza bidhaa" (add product)
   - "uza" (record sale)
   - "mauzo ya leo" (today's sales)
   
   English:
   - "help"
   - "register"
   - "add product"
   - "sell"
   - "today sales"
   ```

## 🌐 Web Dashboard Testing

### Access Points
- **Dashboard**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api
- **Health Check**: http://localhost:3000/health

### Test Scenarios
1. **Business Registration**:
   ```bash
   curl -X POST http://localhost:3000/api/auth/register \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Test Duka",
       "phone": "+255700000001",
       "owner_name": "Test Owner",
       "business_type": "duka"
     }'
   ```

2. **Login and Get Token**:
   ```bash
   curl -X POST http://localhost:3000/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{
       "phone": "+255700000001"
     }'
   ```

3. **Add Product** (use token from login):
   ```bash
   curl -X POST http://localhost:3000/api/products \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "name": "Coca Cola",
       "price": 1000,
       "stock": 50,
       "category": "drinks"
     }'
   ```

## 🔧 Troubleshooting

### Common Issues

1. **Server Won't Start**:
   ```bash
   # Check if port 3000 is in use
   lsof -i :3000
   
   # Kill process if needed
   kill -9 <PID>
   ```

2. **MongoDB Connection Error**:
   ```bash
   # Start MongoDB
   mongod
   
   # Or check if running
   ps aux | grep mongod
   ```

3. **Redis Connection Error**:
   ```bash
   # Start Redis
   redis-server
   
   # Or check if running
   redis-cli ping
   ```

4. **WhatsApp Webhook Not Working**:
   - Check Twilio webhook URL configuration
   - Verify webhook signature (disable in development)
   - Check server logs: `tail -f logs/app.log`

### Environment Variables Check
```bash
# Verify required environment variables
node -e "
const required = [
  'MONGODB_URI', 'REDIS_URL', 'JWT_SECRET',
  'TWILIO_ACCOUNT_SID', 'TWILIO_AUTH_TOKEN'
];
required.forEach(key => {
  console.log(key + ':', process.env[key] ? '✅ Set' : '❌ Missing');
});
"
```

## 📊 Performance Testing

### Load Testing with Artillery
```bash
# Install artillery
npm install -g artillery

# Test API endpoints
artillery quick --count 10 --num 5 http://localhost:3000/health

# Test WhatsApp webhook
artillery quick --count 5 --num 2 \
  -p '{"From":"whatsapp:+************","Body":"msaada"}' \
  http://localhost:3000/webhook/test/whatsapp
```

### Memory and CPU Monitoring
```bash
# Monitor Node.js process
top -p $(pgrep -f "node.*server.js")

# Check memory usage
node -e "console.log(process.memoryUsage())"
```

## 🧪 Test Data

### Sample Business Data
```json
{
  "name": "Mama Juma's Duka",
  "phone": "+************",
  "owner_name": "Mama Juma",
  "business_type": "duka",
  "location": {
    "region": "Dar es Salaam",
    "district": "Kinondoni",
    "ward": "Msasani"
  }
}
```

### Sample Product Data
```json
{
  "name": "Unga wa Ngano",
  "price": 2500,
  "stock": 100,
  "category": "food",
  "description": "Unga wa ngano wa kilogram 1"
}
```

### Sample Sale Data
```json
{
  "items": [
    {
      "product_id": "PRODUCT_ID_HERE",
      "quantity": 2
    }
  ],
  "payment_method": "mpesa",
  "customer_info": {
    "name": "John Doe",
    "phone": "+255700987654"
  }
}
```

## ✅ Test Checklist

### Basic Functionality
- [ ] Server starts without errors
- [ ] Database connections established
- [ ] Health check returns 200
- [ ] Dashboard loads correctly
- [ ] API endpoints respond

### WhatsApp Bot
- [ ] Webhook receives messages
- [ ] Help command works (msaada/help)
- [ ] Business registration flow
- [ ] Product management commands
- [ ] Sales recording workflow
- [ ] Language switching works

### Business Logic
- [ ] User authentication
- [ ] Business data isolation
- [ ] Product CRUD operations
- [ ] Sales recording and calculation
- [ ] Customer management
- [ ] Reports generation

### Security
- [ ] JWT authentication works
- [ ] Rate limiting active
- [ ] Input validation working
- [ ] Business isolation enforced
- [ ] Webhook signature verification

### Performance
- [ ] Response times < 500ms
- [ ] Memory usage stable
- [ ] No memory leaks
- [ ] Concurrent users supported

## 🚀 Ready for Production

Once all tests pass:
1. Configure production environment variables
2. Set up SSL certificates
3. Configure reverse proxy (nginx)
4. Set up monitoring and logging
5. Configure backup strategies
6. Deploy to production server

## 📞 Support

If you encounter issues:
1. Check the logs: `tail -f logs/app.log`
2. Review this testing guide
3. Check the main README.md
4. Create an issue with detailed error information

Happy testing! 🎉
