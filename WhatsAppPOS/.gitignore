# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Upload directories
uploads/
public/uploads/

# Database files
*.db
*.sqlite
*.sqlite3

# Redis dump
dump.rdb

# PM2 logs
.pm2/

# Docker
.dockerignore

# Test files
test-results/
coverage/

# Build artifacts
build/
dist/

# Backup files
*.backup
*.bak

# Local development files
.local/
local/

# Certificate files
*.pem
*.key
*.crt
*.csr

# Sensitive data
secrets/
private/

# Temporary files
*.tmp
*.temp

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# IDE specific files
.vscode/settings.json
.idea/workspace.xml
.idea/tasks.xml
.idea/dictionaries/
.idea/vcs.xml
.idea/jsLibraryMappings.xml

# MacOS
.AppleDouble
.LSOverride

# Windows
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# JetBrains
.idea/
*.iml
*.ipr
*.iws

# VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
