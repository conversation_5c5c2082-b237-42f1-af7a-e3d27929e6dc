require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('./backend/config/logger');

console.log('Starting database test...');

// MongoDB connection options
const mongoOptions = {
  maxPoolSize: 10,
  minPoolSize: 2,
  maxIdleTimeMS: 30000,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 10000,
};

async function testConnection() {
  try {
    console.log('Attempting to connect to MongoDB...');
    const mongoURI = process.env.MONGODB_URI;
    console.log('MongoDB URI:', mongoURI);

    await mongoose.connect(mongoURI, mongoOptions);

    console.log('MongoDB connected successfully');
    logger.info('Database connection test successful');

    await mongoose.disconnect();
    console.log('Disconnected successfully');
    process.exit(0);

  } catch (error) {
    console.error('Database connection test failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

testConnection();
