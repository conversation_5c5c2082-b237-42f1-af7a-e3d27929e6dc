require('dotenv').config();

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const path = require('path');

console.log('Basic imports loaded...');

const logger = require('./backend/config/logger');
console.log('Logger loaded...');

const errorHandler = require('./backend/middleware/errorHandler');
console.log('Error handler loaded...');

// Test importing routes one by one
console.log('Importing auth routes...');
const authRoutes = require('./backend/routes/auth');
console.log('Auth routes loaded');

console.log('Importing business routes...');
const businessRoutes = require('./backend/routes/businesses');
console.log('Business routes loaded');

console.log('Importing product routes...');
const productRoutes = require('./backend/routes/products');
console.log('Product routes loaded');

console.log('Importing sales routes...');
const salesRoutes = require('./backend/routes/sales');
console.log('Sales routes loaded');

console.log('Importing customer routes...');
const customerRoutes = require('./backend/routes/customers');
console.log('Customer routes loaded');

console.log('Importing report routes...');
const reportRoutes = require('./backend/routes/reports');
console.log('Report routes loaded');

console.log('Importing user routes...');
const userRoutes = require('./backend/routes/users');
console.log('User routes loaded');

console.log('Importing webhook routes...');
const webhookRoutes = require('./backend/routes/webhook');
console.log('Webhook routes loaded');

console.log('All routes imported successfully');

const app = express();
console.log('Express app created');

module.exports = app;
