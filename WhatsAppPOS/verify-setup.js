/**
 * WhatsApp POS Tanzania - Setup Verification Script
 * Verifies all components are properly configured for testing
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const util = require('util');

const execAsync = util.promisify(exec);

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, symbol, message) {
  console.log(`${colors[color]}${symbol} ${message}${colors.reset}`);
}

function success(message) { log('green', '✅', message); }
function error(message) { log('red', '❌', message); }
function warning(message) { log('yellow', '⚠️ ', message); }
function info(message) { log('blue', 'ℹ️ ', message); }

class SetupVerifier {
  constructor() {
    this.errors = [];
    this.warnings = [];
  }

  async checkNodeVersion() {
    try {
      const { stdout } = await execAsync('node --version');
      const version = parseInt(stdout.trim().substring(1));
      
      if (version >= 18) {
        success(`Node.js version: ${stdout.trim()}`);
        return true;
      } else {
        error(`Node.js version ${stdout.trim()} is too old. Requires 18+`);
        this.errors.push('Node.js version too old');
        return false;
      }
    } catch (err) {
      error('Node.js not found');
      this.errors.push('Node.js not installed');
      return false;
    }
  }

  async checkNpm() {
    try {
      const { stdout } = await execAsync('npm --version');
      success(`npm version: ${stdout.trim()}`);
      return true;
    } catch (err) {
      error('npm not found');
      this.errors.push('npm not installed');
      return false;
    }
  }

  async checkMongoDB() {
    try {
      // Try mongosh first (newer versions)
      try {
        await execAsync('mongosh --eval "db.runCommand(\'ping\')" --quiet');
        success('MongoDB is running (mongosh)');
        return true;
      } catch (err) {
        // Fallback to mongo (older versions)
        await execAsync('mongo --eval "db.runCommand(\'ping\')" --quiet');
        success('MongoDB is running (mongo)');
        return true;
      }
    } catch (err) {
      error('MongoDB is not running or not accessible');
      this.errors.push('MongoDB not running');
      return false;
    }
  }

  async checkRedis() {
    try {
      await execAsync('redis-cli ping');
      success('Redis is running');
      return true;
    } catch (err) {
      error('Redis is not running or not accessible');
      this.errors.push('Redis not running');
      return false;
    }
  }

  checkEnvironmentFile() {
    const envPath = path.join(__dirname, '.env');
    
    if (!fs.existsSync(envPath)) {
      error('.env file not found');
      this.errors.push('.env file missing');
      return false;
    }
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    const requiredVars = [
      'MONGODB_URI',
      'REDIS_URL',
      'JWT_SECRET',
      'TWILIO_ACCOUNT_SID',
      'TWILIO_AUTH_TOKEN',
      'TWILIO_WHATSAPP_NUMBER',
      'WEBHOOK_URL'
    ];
    
    let allPresent = true;
    
    for (const varName of requiredVars) {
      if (envContent.includes(`${varName}=`) && !envContent.includes(`${varName}=your-`)) {
        success(`Environment variable ${varName} is configured`);
      } else {
        if (varName.startsWith('TWILIO_')) {
          warning(`Environment variable ${varName} needs Twilio configuration`);
          this.warnings.push(`${varName} needs configuration`);
        } else {
          error(`Environment variable ${varName} is missing or not configured`);
          this.errors.push(`${varName} missing`);
          allPresent = false;
        }
      }
    }
    
    return allPresent;
  }

  checkProjectStructure() {
    const requiredFiles = [
      'package.json',
      'backend/server.js',
      'backend/app.js',
      'backend/models/index.js',
      'backend/routes/webhook.js',
      'backend/services/whatsappService.js',
      'dashboard/public/index.html'
    ];
    
    let allPresent = true;
    
    for (const file of requiredFiles) {
      const filePath = path.join(__dirname, file);
      if (fs.existsSync(filePath)) {
        success(`File exists: ${file}`);
      } else {
        error(`Missing file: ${file}`);
        this.errors.push(`Missing ${file}`);
        allPresent = false;
      }
    }
    
    return allPresent;
  }

  async checkDependencies() {
    try {
      const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
      const nodeModulesPath = path.join(__dirname, 'node_modules');
      
      if (!fs.existsSync(nodeModulesPath)) {
        error('node_modules directory not found. Run: npm install');
        this.errors.push('Dependencies not installed');
        return false;
      }
      
      // Check key dependencies
      const keyDeps = ['express', 'mongoose', 'redis', 'twilio', 'jsonwebtoken'];
      let allInstalled = true;
      
      for (const dep of keyDeps) {
        const depPath = path.join(nodeModulesPath, dep);
        if (fs.existsSync(depPath)) {
          success(`Dependency installed: ${dep}`);
        } else {
          error(`Missing dependency: ${dep}`);
          this.errors.push(`Missing ${dep}`);
          allInstalled = false;
        }
      }
      
      return allInstalled;
    } catch (err) {
      error('Error checking dependencies');
      this.errors.push('Dependency check failed');
      return false;
    }
  }

  async checkNgrok() {
    try {
      const { stdout } = await execAsync('ngrok version');
      success(`ngrok installed: ${stdout.trim().split('\n')[0]}`);
      return true;
    } catch (err) {
      warning('ngrok not found. Install with: npm install -g ngrok');
      this.warnings.push('ngrok not installed');
      return false;
    }
  }

  async checkTwilioConfiguration() {
    const envPath = path.join(__dirname, '.env');
    
    if (!fs.existsSync(envPath)) {
      return false;
    }
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    // Check if Twilio credentials are configured (not placeholder values)
    const hasAccountSid = envContent.includes('TWILIO_ACCOUNT_SID=AC') && 
                         !envContent.includes('TWILIO_ACCOUNT_SID=ACxxxxxxxx');
    const hasAuthToken = envContent.includes('TWILIO_AUTH_TOKEN=') && 
                        !envContent.includes('TWILIO_AUTH_TOKEN=your_auth_token');
    const hasWebhookUrl = envContent.includes('WEBHOOK_URL=https://') && 
                         !envContent.includes('WEBHOOK_URL=https://your-ngrok-url');
    
    if (hasAccountSid && hasAuthToken) {
      success('Twilio credentials are configured');
      
      if (hasWebhookUrl) {
        success('Webhook URL is configured');
        return true;
      } else {
        warning('Webhook URL needs ngrok configuration');
        this.warnings.push('Webhook URL needs ngrok');
        return false;
      }
    } else {
      warning('Twilio credentials need configuration');
      this.warnings.push('Twilio credentials needed');
      return false;
    }
  }

  async runAllChecks() {
    console.log('🇹🇿 WhatsApp POS Tanzania - Setup Verification\n');
    console.log('==============================================\n');
    
    info('Checking system prerequisites...\n');
    
    // System checks
    await this.checkNodeVersion();
    await this.checkNpm();
    await this.checkMongoDB();
    await this.checkRedis();
    
    console.log('');
    info('Checking project configuration...\n');
    
    // Project checks
    this.checkEnvironmentFile();
    this.checkProjectStructure();
    await this.checkDependencies();
    
    console.log('');
    info('Checking testing tools...\n');
    
    // Testing tools
    await this.checkNgrok();
    await this.checkTwilioConfiguration();
    
    console.log('');
    this.printSummary();
  }

  printSummary() {
    console.log('📋 Verification Summary');
    console.log('=======================\n');
    
    if (this.errors.length === 0) {
      success('All critical checks passed! ✨');
      
      if (this.warnings.length === 0) {
        console.log('');
        success('🎉 Your system is fully configured and ready for testing!');
        console.log('');
        info('Next steps:');
        console.log('1. Start ngrok: ngrok http 3000');
        console.log('2. Update WEBHOOK_URL in .env with ngrok URL');
        console.log('3. Configure Twilio webhook URL');
        console.log('4. Start server: npm run dev');
        console.log('5. Run tests: npm run test:whatsapp');
      } else {
        console.log('');
        warning(`${this.warnings.length} warning(s) found:`);
        this.warnings.forEach(w => console.log(`  - ${w}`));
        console.log('');
        info('System can run but some features may be limited.');
      }
    } else {
      error(`${this.errors.length} critical error(s) found:`);
      this.errors.forEach(e => console.log(`  - ${e}`));
      
      if (this.warnings.length > 0) {
        console.log('');
        warning(`${this.warnings.length} warning(s) found:`);
        this.warnings.forEach(w => console.log(`  - ${w}`));
      }
      
      console.log('');
      error('Please fix the errors above before proceeding.');
      console.log('');
      info('Quick fixes:');
      console.log('- Install missing dependencies: npm install');
      console.log('- Start MongoDB: mongod or brew services start mongodb-community');
      console.log('- Start Redis: redis-server or brew services start redis');
      console.log('- Create .env file: cp .env.testing .env');
    }
  }
}

// Run verification
async function main() {
  const verifier = new SetupVerifier();
  await verifier.runAllChecks();
  
  // Exit with error code if there are critical errors
  if (verifier.errors.length > 0) {
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = SetupVerifier;
