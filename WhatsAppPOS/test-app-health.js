require('dotenv').config();

const express = require('express');
const logger = require('./backend/config/logger');

const app = express();

console.log('Testing health check endpoint...');

// Health check endpoint
app.get('/health', (req, res) => {
  console.log('Health check called');
  try {
    const version = require('./package.json').version;
    console.log('Package.json version:', version);
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      version: version,
      uptime: process.uptime()
    });
  } catch (error) {
    console.error('Error in health check:', error.message);
    res.status(500).json({ error: error.message });
  }
});

console.log('Health check endpoint created');

module.exports = app;
