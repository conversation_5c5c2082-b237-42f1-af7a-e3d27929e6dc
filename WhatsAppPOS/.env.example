# Environment Configuration for WhatsApp POS Tanzania
NODE_ENV=development
PORT=3000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/tanzanian_pos
MONGODB_TEST_URI=mongodb://localhost:27017/tanzanian_pos_test

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-change-this-in-production
JWT_REFRESH_SECRET=your-refresh-token-secret-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Twilio WhatsApp Configuration
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
WEBHOOK_URL=https://yourdomain.com/webhook/whatsapp

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Frontend Configuration
FRONTEND_URL=http://localhost:3001
DASHBOARD_URL=http://localhost:3001

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Business Configuration
DEFAULT_CURRENCY=TZS
DEFAULT_LANGUAGE=sw
DEFAULT_TIMEZONE=Africa/Dar_es_Salaam

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Subscription Configuration
TRIAL_DURATION_DAYS=14
BASIC_PLAN_PRICE=10000
PRO_PLAN_PRICE=25000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this

# External APIs
EXCHANGE_RATE_API_KEY=your-exchange-rate-api-key

# OpenRouter AI Configuration (Optional - for enhanced natural language understanding)
OPENROUTER_API_KEY=your-openrouter-api-key-here
AI_ENABLED=true
AI_PRIMARY_MODEL=anthropic/claude-3-haiku
AI_FALLBACK_MODEL=openai/gpt-3.5-turbo
AI_MAX_TOKENS=150
AI_TEMPERATURE=0.3

# Monitoring
SENTRY_DSN=your-sentry-dsn-for-error-tracking
