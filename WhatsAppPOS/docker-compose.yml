version: '3.8'

services:
  # WhatsApp POS Application
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=mongodb://mongo:27017/tanzanian_pos
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=dev-jwt-secret-change-in-production
      - JWT_REFRESH_SECRET=dev-refresh-secret-change-in-production
      - DEFAULT_LANGUAGE=sw
      - DEFAULT_TIMEZONE=Africa/Dar_es_Salaam
      - DEFAULT_CURRENCY=TZS
    depends_on:
      - mongo
      - redis
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped
    networks:
      - whatsapp-pos-network

  # MongoDB Database
  mongo:
    image: mongo:6.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=tanzanian_pos
    volumes:
      - mongo_data:/data/db
      - ./database/init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - whatsapp-pos-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - whatsapp-pos-network
    command: redis-server --appendonly yes

  # MongoDB Admin Interface (optional)
  mongo-express:
    image: mongo-express:latest
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongo
      - ME_CONFIG_MONGODB_PORT=27017
      - ME_CONFIG_MONGODB_ENABLE_ADMIN=true
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin123
    depends_on:
      - mongo
    restart: unless-stopped
    networks:
      - whatsapp-pos-network

  # Redis Admin Interface (optional)
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8082:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - whatsapp-pos-network

volumes:
  mongo_data:
    driver: local
  redis_data:
    driver: local

networks:
  whatsapp-pos-network:
    driver: bridge
