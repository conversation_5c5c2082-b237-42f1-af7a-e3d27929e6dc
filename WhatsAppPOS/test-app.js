/**
 * Minimal app for testing
 */

const express = require('express');
const logger = require('./backend/config/logger');

const app = express();

// Basic middleware
app.use(express.json());

// Health check route
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

// Test database connection
app.get('/test-db', async (req, res) => {
  try {
    const { connectDB } = require('./backend/config/database');
    await connectDB();
    res.json({ status: 'OK', message: 'Database connected' });
  } catch (error) {
    res.status(500).json({ status: 'ERROR', message: error.message });
  }
});

module.exports = app;
