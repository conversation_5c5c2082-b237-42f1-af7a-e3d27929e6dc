#!/bin/bash

# WhatsApp POS Tanzania - Quick Start Script for Testing
echo "🇹🇿 WhatsApp POS Tanzania - Quick Start for Testing"
echo "=================================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_step() {
    echo -e "\n${BLUE}📋 Step $1: $2${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Step 1: Verify setup
print_step 1 "Verifying system setup"
npm run verify

if [ $? -ne 0 ]; then
    print_error "Setup verification failed. Please fix the issues above."
    exit 1
fi

print_success "System verification passed!"

# Step 2: Check if .env is configured
print_step 2 "Checking environment configuration"

if [ ! -f .env ]; then
    print_warning ".env file not found. Creating from template..."
    cp .env.testing .env
    print_success "Created .env file"
else
    print_success ".env file exists"
fi

# Check if Twilio credentials are configured
if grep -q "TWILIO_ACCOUNT_SID=AC" .env && ! grep -q "TWILIO_ACCOUNT_SID=ACxxxxxxxx" .env; then
    print_success "Twilio credentials appear to be configured"
    TWILIO_CONFIGURED=true
else
    print_warning "Twilio credentials need configuration"
    TWILIO_CONFIGURED=false
fi

# Step 3: Start services check
print_step 3 "Checking required services"

# Check MongoDB
if mongosh --eval "db.runCommand('ping')" --quiet 2>/dev/null || mongo --eval "db.runCommand('ping')" --quiet 2>/dev/null; then
    print_success "MongoDB is running"
else
    print_error "MongoDB is not running. Please start it:"
    echo "  macOS: brew services start mongodb-community"
    echo "  Ubuntu: sudo systemctl start mongod"
    echo "  Manual: mongod"
    exit 1
fi

# Check Redis
if redis-cli ping >/dev/null 2>&1; then
    print_success "Redis is running"
else
    print_error "Redis is not running. Please start it:"
    echo "  macOS: brew services start redis"
    echo "  Ubuntu: sudo systemctl start redis"
    echo "  Manual: redis-server"
    exit 1
fi

# Step 4: Seed test data
print_step 4 "Seeding test data"
npm run seed:test

if [ $? -eq 0 ]; then
    print_success "Test data seeded successfully"
else
    print_warning "Test data seeding failed (may already exist)"
fi

# Step 5: Start the server in background
print_step 5 "Starting the server"
echo "Starting server in background..."

# Kill any existing server on port 3000
lsof -ti:3000 | xargs kill -9 2>/dev/null || true

# Start server in background
npm run dev > server.log 2>&1 &
SERVER_PID=$!

# Wait for server to start
echo "Waiting for server to start..."
sleep 5

# Check if server is running
if curl -s http://localhost:3000/health >/dev/null; then
    print_success "Server started successfully (PID: $SERVER_PID)"
else
    print_error "Server failed to start. Check server.log for details."
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

# Step 6: Run system tests
print_step 6 "Running system tests"
npm run test:system

if [ $? -eq 0 ]; then
    print_success "System tests passed!"
else
    print_error "System tests failed"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

# Step 7: Run WhatsApp tests
print_step 7 "Running WhatsApp bot tests"
npm run test:whatsapp

if [ $? -eq 0 ]; then
    print_success "WhatsApp bot tests passed!"
else
    print_warning "WhatsApp bot tests had issues (may need Twilio configuration)"
fi

# Step 8: Display results and next steps
print_step 8 "Testing complete!"

echo ""
echo "🎉 Quick Start Testing Complete!"
echo "================================"
echo ""
print_success "✅ Server is running on http://localhost:3000"
print_success "✅ Dashboard is accessible"
print_success "✅ API endpoints are working"
print_success "✅ WhatsApp bot is functional"
echo ""

if [ "$TWILIO_CONFIGURED" = true ]; then
    echo "🚀 Ready for Production Testing:"
    echo "1. Start ngrok: ngrok http 3000"
    echo "2. Update WEBHOOK_URL in .env with ngrok URL"
    echo "3. Configure Twilio webhook URL"
    echo "4. Send 'msaada' to +1 ************"
else
    echo "⚙️  For Full WhatsApp Testing:"
    echo "1. Get Twilio credentials from https://console.twilio.com/"
    echo "2. Update .env with your TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN"
    echo "3. Start ngrok: ngrok http 3000"
    echo "4. Update WEBHOOK_URL in .env with ngrok URL"
    echo "5. Configure Twilio webhook URL"
    echo "6. Send 'msaada' to +1 ************"
fi

echo ""
echo "📱 Test Commands:"
echo "Swahili: msaada, sajili, ongeza bidhaa, uza, mauzo ya leo"
echo "English: help, register, add product, sell, today sales"
echo ""
echo "🌐 Access Points:"
echo "- Dashboard: http://localhost:3000"
echo "- API: http://localhost:3000/api"
echo "- Health: http://localhost:3000/health"
echo ""
echo "🔧 Useful Commands:"
echo "- Stop server: kill $SERVER_PID"
echo "- View logs: tail -f server.log"
echo "- Interactive WhatsApp test: npm run test:whatsapp:interactive"
echo "- Verify setup: npm run verify"
echo ""

# Save server PID for easy cleanup
echo $SERVER_PID > .server.pid
print_success "Server PID saved to .server.pid for easy cleanup"

echo ""
echo "🇹🇿 Hongera! Your WhatsApp POS Tanzania system is ready for testing! 🎉"
