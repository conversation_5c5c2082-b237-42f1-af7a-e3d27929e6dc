# AI Integration Documentation
## WhatsApp POS Tanzania - OpenRouter AI Enhancement

### Overview

The WhatsApp POS Tanzania system integrates with OpenRouter API to provide advanced natural language understanding, making the system more accessible and user-friendly for Tanzanian businesses.

### Features

#### 1. Natural Language Understanding
- **Typo Correction**: Automatically corrects common spelling mistakes
- **Intent Recognition**: Understands user goals beyond exact command matches
- **Multi-language Support**: Handles Swahili-English code-switching
- **Contextual Understanding**: Interprets messages based on conversation state

#### 2. Smart Product Search
- **Fuzzy Matching**: Finds products even with misspelled names
- **Descriptive Search**: Understands product descriptions and categories
- **AI-Powered Suggestions**: Provides intelligent product recommendations
- **Confidence Scoring**: Shows how certain the AI is about matches

#### 3. Quantity and Price Extraction
- **Natural Language Parsing**: Extracts numbers from conversational text
- **Swahili Number Recognition**: Understands Swahili number words
- **Unit Recognition**: Handles various units (pieces, kilos, bottles, etc.)
- **Context-Aware Extraction**: Distinguishes between quantity and price

#### 4. Error Recovery and Clarification
- **Intelligent Suggestions**: Provides options when input is ambiguous
- **Clarifying Questions**: Asks specific questions to resolve confusion
- **Graceful Degradation**: Falls back to basic functionality if AI fails

### Configuration

#### Environment Variables

```bash
# OpenRouter AI Configuration
OPENROUTER_API_KEY=your-openrouter-api-key-here
AI_ENABLED=true
AI_PRIMARY_MODEL=anthropic/claude-3-haiku
AI_FALLBACK_MODEL=openai/gpt-3.5-turbo
AI_MAX_TOKENS=150
AI_TEMPERATURE=0.3
```

#### Model Selection

**Primary Model: Claude-3-Haiku**
- Fast response times (< 2 seconds)
- Good multilingual support
- Cost-effective for high volume
- Excellent instruction following

**Fallback Model: GPT-3.5-Turbo**
- Reliable backup option
- Good performance on edge cases
- Widely available
- Consistent quality

### Usage Examples

#### 1. Command Correction
```
User: "ad prodct"
AI Enhancement: "add product"
System Response: Product addition flow initiated
```

#### 2. Natural Language Sales
```
User: "I want to sell 5 bottles of Coca Cola"
AI Extraction:
- Intent: record_sale
- Product: "Coca Cola"
- Quantity: 5
System Response: Initiates sale with pre-filled data
```

#### 3. Swahili-English Code-Switching
```
User: "nataka ku-add bidhaa mpya"
AI Understanding: "want to add new product"
Intent: add_product
System Response: Product addition flow in Swahili
```

#### 4. Fuzzy Product Search
```
User: "coka cola" (misspelled)
AI Search: Finds "Coca Cola" with 85% confidence
System Response: Shows product with confidence indicator
```

### API Integration

#### OpenRouter Request Format
```javascript
{
  "model": "anthropic/claude-3-haiku",
  "messages": [
    {
      "role": "user",
      "content": "Enhanced prompt with context"
    }
  ],
  "max_tokens": 150,
  "temperature": 0.3
}
```

#### Response Processing
```javascript
{
  "enhanced": true,
  "intent": "record_sale",
  "confidence": 0.85,
  "correctedMessage": "I want to sell something",
  "extractedData": {
    "quantity": 5,
    "productName": "Coca Cola"
  },
  "suggestions": ["record_sale", "quick_sale"],
  "needsClarification": false
}
```

### Performance Optimization

#### 1. Caching Strategy
- **Intent Cache**: Stores frequent query results for 5 minutes
- **Cache Key**: Combines message, context, and language
- **Cache Size**: Limited to 1000 entries with LRU eviction

#### 2. Request Optimization
- **Token Limits**: Maximum 150 tokens to control costs
- **Temperature**: Low (0.3) for consistent, focused responses
- **Timeout**: 5-second timeout to maintain responsiveness

#### 3. Fallback Mechanisms
- **Model Fallback**: Switches to backup model if primary fails
- **Function Fallback**: Uses regex/heuristics if AI unavailable
- **Graceful Degradation**: System works fully without AI

### Cost Management

#### Estimated Costs (per 1000 messages)
- **Claude-3-Haiku**: ~$0.50-1.00
- **GPT-3.5-Turbo**: ~$1.00-2.00
- **Cache Hit Rate**: 30-40% (reduces costs significantly)

#### Cost Optimization Strategies
1. **Smart Caching**: Reduces API calls by 30-40%
2. **Token Optimization**: Minimal prompts for faster, cheaper responses
3. **Selective Enhancement**: Only enhance complex/ambiguous messages
4. **Batch Processing**: Group similar requests when possible

### Error Handling

#### 1. API Failures
```javascript
// Graceful fallback to basic functionality
if (!aiResult.enhanced) {
  return basicCommandDetection(message);
}
```

#### 2. Malformed Responses
```javascript
// Parse with error handling
try {
  const parsed = JSON.parse(aiResponse);
  return processAIResult(parsed);
} catch (error) {
  return extractIntentFromText(aiResponse);
}
```

#### 3. Network Issues
```javascript
// Retry with exponential backoff
const result = await retryWithBackoff(
  () => callOpenRouter(prompt),
  { maxRetries: 2, baseDelay: 200 }
);
```

### Monitoring and Analytics

#### 1. Performance Metrics
- **Response Time**: Average AI processing time
- **Success Rate**: Percentage of successful AI enhancements
- **Cache Hit Rate**: Efficiency of caching system
- **Cost Tracking**: API usage and associated costs

#### 2. Quality Metrics
- **Intent Accuracy**: How often AI correctly identifies user intent
- **User Satisfaction**: Reduced error rates and clarification requests
- **Conversion Rate**: Successful task completion after AI enhancement

#### 3. Logging
```javascript
logger.info('AI Enhancement', {
  original: messageBody,
  enhanced: result.correctedMessage,
  intent: result.intent,
  confidence: result.confidence,
  processingTime: duration
});
```

### Testing

#### 1. Unit Tests
- Intent recognition accuracy
- Product search functionality
- Quantity/price extraction
- Error handling scenarios

#### 2. Integration Tests
- End-to-end message processing
- Multi-language support
- Context preservation
- Performance benchmarks

#### 3. Load Testing
- High-volume message processing
- Cache performance under load
- API rate limiting handling
- Fallback system reliability

### Security Considerations

#### 1. Data Privacy
- **No PII Storage**: User data not stored in AI service
- **Minimal Context**: Only necessary business context sent to AI
- **Encryption**: All API communications use HTTPS
- **Audit Logging**: Track all AI interactions for compliance

#### 2. API Security
- **Key Rotation**: Regular API key updates
- **Rate Limiting**: Prevent abuse and control costs
- **Request Validation**: Sanitize all inputs before AI processing
- **Error Sanitization**: Don't expose internal errors to users

### Deployment

#### 1. Environment Setup
```bash
# Install dependencies
npm install axios

# Set environment variables
export OPENROUTER_API_KEY="your-key-here"
export AI_ENABLED=true

# Test AI service
npm test -- --grep "AI Service"
```

#### 2. Production Considerations
- **API Key Management**: Use secure key storage
- **Monitoring**: Set up alerts for API failures
- **Cost Alerts**: Monitor usage to prevent overruns
- **Performance Monitoring**: Track response times and success rates

### Troubleshooting

#### Common Issues

1. **AI Not Working**
   - Check OPENROUTER_API_KEY is set
   - Verify API key has sufficient credits
   - Check network connectivity

2. **Slow Responses**
   - Monitor API response times
   - Check cache hit rates
   - Consider model switching

3. **High Costs**
   - Review token usage patterns
   - Optimize prompt lengths
   - Increase cache hit rates

4. **Poor Intent Recognition**
   - Review and update prompts
   - Add more context to requests
   - Consider model fine-tuning

### Future Enhancements

1. **Custom Model Training**: Train models on Tanzanian business data
2. **Voice Integration**: Add speech-to-text for voice messages
3. **Predictive Analytics**: Predict user needs based on patterns
4. **Advanced Personalization**: Customize responses per business type
5. **Multi-modal Support**: Handle images and documents

### Support

For technical support or questions about AI integration:
- Email: <EMAIL>
- Documentation: https://docs.whatsapp-pos-tanzania.com
- GitHub Issues: https://github.com/whatsapp-pos-tanzania/issues
