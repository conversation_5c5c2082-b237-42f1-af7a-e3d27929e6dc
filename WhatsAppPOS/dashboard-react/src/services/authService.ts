import { apiClient } from './api';
import { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  User, 
  Business,
  ApiResponse 
} from '@types/index';

export class AuthService {
  // Login user
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse['data']>('/auth/login', credentials);
      
      if (response.success && response.data) {
        // Store tokens and user data
        const { tokens, user, business } = response.data;
        
        localStorage.setItem('access_token', tokens.access_token);
        localStorage.setItem('refresh_token', tokens.refresh_token);
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('business', JSON.stringify(business));
        
        return {
          success: true,
          data: response.data
        };
      }
      
      throw new Error(response.error || 'Login failed');
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Login failed');
    }
  }

  // Register new business
  async register(data: RegisterRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse['data']>('/auth/register', data);
      
      if (response.success && response.data) {
        // Store tokens and user data
        const { tokens, user, business } = response.data;
        
        localStorage.setItem('access_token', tokens.access_token);
        localStorage.setItem('refresh_token', tokens.refresh_token);
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('business', JSON.stringify(business));
        
        return {
          success: true,
          data: response.data
        };
      }
      
      throw new Error(response.error || 'Registration failed');
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Registration failed');
    }
  }

  // Refresh access token
  async refreshToken(): Promise<string> {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiClient.post<{ tokens: { access_token: string; refresh_token: string } }>('/auth/refresh', {
        refresh_token: refreshToken
      });

      if (response.success && response.data) {
        const { access_token, refresh_token: newRefreshToken } = response.data.tokens;
        
        localStorage.setItem('access_token', access_token);
        localStorage.setItem('refresh_token', newRefreshToken);
        
        return access_token;
      }
      
      throw new Error('Token refresh failed');
    } catch (error: any) {
      this.logout();
      throw new Error(error.response?.data?.error || error.message || 'Token refresh failed');
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      // Call logout endpoint to invalidate tokens on server
      await apiClient.post('/auth/logout');
    } catch (error) {
      // Continue with logout even if server call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Clear local storage
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
      localStorage.removeItem('business');
      
      // Redirect to login
      window.location.href = '/login';
    }
  }

  // Get current user
  getCurrentUser(): User | null {
    try {
      const userStr = localStorage.getItem('user');
      return userStr ? JSON.parse(userStr) : null;
    } catch {
      return null;
    }
  }

  // Get current business
  getCurrentBusiness(): Business | null {
    try {
      const businessStr = localStorage.getItem('business');
      return businessStr ? JSON.parse(businessStr) : null;
    } catch {
      return null;
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    const token = localStorage.getItem('access_token');
    const user = this.getCurrentUser();
    return !!(token && user);
  }

  // Check if user has specific permission
  hasPermission(permission: keyof User['permissions']): boolean {
    const user = this.getCurrentUser();
    return user?.permissions[permission] || false;
  }

  // Check if user has specific role
  hasRole(role: User['role'] | User['role'][]): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;
    
    const roles = Array.isArray(role) ? role : [role];
    return roles.includes(user.role);
  }

  // Update user profile
  async updateProfile(data: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.put<User>('/auth/profile', data);
      
      if (response.success && response.data) {
        // Update stored user data
        localStorage.setItem('user', JSON.stringify(response.data));
        return response.data;
      }
      
      throw new Error(response.error || 'Profile update failed');
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Profile update failed');
    }
  }

  // Change password
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      const response = await apiClient.post('/auth/change-password', {
        current_password: currentPassword,
        new_password: newPassword
      });
      
      if (!response.success) {
        throw new Error(response.error || 'Password change failed');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Password change failed');
    }
  }

  // Request password reset
  async requestPasswordReset(phone: string): Promise<void> {
    try {
      const response = await apiClient.post('/auth/forgot-password', { phone });
      
      if (!response.success) {
        throw new Error(response.error || 'Password reset request failed');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Password reset request failed');
    }
  }

  // Reset password with token
  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      const response = await apiClient.post('/auth/reset-password', {
        token,
        new_password: newPassword
      });
      
      if (!response.success) {
        throw new Error(response.error || 'Password reset failed');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Password reset failed');
    }
  }

  // Verify phone number
  async verifyPhone(phone: string, code: string): Promise<void> {
    try {
      const response = await apiClient.post('/auth/verify-phone', {
        phone,
        verification_code: code
      });
      
      if (!response.success) {
        throw new Error(response.error || 'Phone verification failed');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Phone verification failed');
    }
  }

  // Send verification code
  async sendVerificationCode(phone: string): Promise<void> {
    try {
      const response = await apiClient.post('/auth/send-verification', { phone });
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to send verification code');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Failed to send verification code');
    }
  }
}

// Create singleton instance
export const authService = new AuthService();

// Export for use in components
export default authService;
