/**
 * Customers Management Page
 * Complete CRUD operations for customers with bilingual support
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  InputAdornment,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Divider,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  People as PeopleIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';
import { api } from '../../services/api';

interface Customer {
  _id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: {
    street?: string;
    ward?: string;
    district?: string;
    region?: string;
  };
  demographics?: {
    date_of_birth?: string;
    gender?: string;
    occupation?: string;
  };
  total_purchases: number;
  last_purchase_date?: string;
  created_at: string;
}

interface CustomerFormData {
  name: string;
  phone: string;
  email: string;
  street: string;
  ward: string;
  district: string;
  region: string;
  occupation: string;
  gender: string;
}

export const CustomersPage: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [language] = useState<'en' | 'sw'>('sw');
  
  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);
  
  // Form state
  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    phone: '',
    email: '',
    street: '',
    ward: '',
    district: '',
    region: '',
    occupation: '',
    gender: ''
  });
  const [formErrors, setFormErrors] = useState<Partial<CustomerFormData>>({});
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchCustomers();
  }, []);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.get('/customers');
      setCustomers(response.data.data || []);
    } catch (err: any) {
      console.error('Failed to fetch customers:', err);
      setError(err.response?.data?.message || 'Failed to load customers');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (customer?: Customer) => {
    if (customer) {
      setEditingCustomer(customer);
      setFormData({
        name: customer.name,
        phone: customer.phone || '',
        email: customer.email || '',
        street: customer.address?.street || '',
        ward: customer.address?.ward || '',
        district: customer.address?.district || '',
        region: customer.address?.region || '',
        occupation: customer.demographics?.occupation || '',
        gender: customer.demographics?.gender || ''
      });
    } else {
      setEditingCustomer(null);
      setFormData({
        name: '',
        phone: '',
        email: '',
        street: '',
        ward: '',
        district: '',
        region: '',
        occupation: '',
        gender: ''
      });
    }
    setFormErrors({});
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingCustomer(null);
    setFormData({
      name: '',
      phone: '',
      email: '',
      street: '',
      ward: '',
      district: '',
      region: '',
      occupation: '',
      gender: ''
    });
    setFormErrors({});
  };

  const validateForm = (): boolean => {
    const errors: Partial<CustomerFormData> = {};

    if (!formData.name.trim()) {
      errors.name = language === 'sw' ? 'Jina linahitajika' : 'Name is required';
    }

    if (formData.phone && !/^\+255[67]\d{8}$/.test(formData.phone)) {
      errors.phone = language === 'sw' ? 'Nambari ya simu si sahihi' : 'Invalid phone number';
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = language === 'sw' ? 'Barua pepe si sahihi' : 'Invalid email address';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setSubmitting(true);
      
      const customerData = {
        name: formData.name.trim(),
        phone: formData.phone.trim() || undefined,
        email: formData.email.trim() || undefined,
        address: {
          street: formData.street.trim() || undefined,
          ward: formData.ward.trim() || undefined,
          district: formData.district.trim() || undefined,
          region: formData.region.trim() || undefined
        },
        demographics: {
          occupation: formData.occupation.trim() || undefined,
          gender: formData.gender || undefined
        }
      };

      if (editingCustomer) {
        await api.put(`/customers/${editingCustomer._id}`, customerData);
      } else {
        await api.post('/customers', customerData);
      }

      await fetchCustomers();
      handleCloseDialog();
      
    } catch (err: any) {
      console.error('Failed to save customer:', err);
      setError(err.response?.data?.message || 'Failed to save customer');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!customerToDelete) return;

    try {
      setSubmitting(true);
      await api.delete(`/customers/${customerToDelete._id}`);
      await fetchCustomers();
      setDeleteDialogOpen(false);
      setCustomerToDelete(null);
    } catch (err: any) {
      console.error('Failed to delete customer:', err);
      setError(err.response?.data?.message || 'Failed to delete customer');
    } finally {
      setSubmitting(false);
    }
  };

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (customer.phone && customer.phone.includes(searchTerm)) ||
    (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sw-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getGenderLabel = (gender: string) => {
    const genders: Record<string, { sw: string; en: string }> = {
      male: { sw: 'Mwanaume', en: 'Male' },
      female: { sw: 'Mwanamke', en: 'Female' },
      other: { sw: 'Nyingine', en: 'Other' }
    };
    return genders[gender] ? genders[gender][language] : gender;
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          {language === 'sw' ? 'Wateja' : 'Customers'}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          {language === 'sw' ? 'Ongeza Mteja' : 'Add Customer'}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Search */}
      <TextField
        fullWidth
        variant="outlined"
        placeholder={language === 'sw' ? 'Tafuta wateja...' : 'Search customers...'}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      {/* Customers Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{language === 'sw' ? 'Jina' : 'Name'}</TableCell>
              <TableCell>{language === 'sw' ? 'Mawasiliano' : 'Contact'}</TableCell>
              <TableCell>{language === 'sw' ? 'Mahali' : 'Location'}</TableCell>
              <TableCell>{language === 'sw' ? 'Ununuzi' : 'Purchases'}</TableCell>
              <TableCell>{language === 'sw' ? 'Vitendo' : 'Actions'}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredCustomers.map((customer) => (
              <TableRow key={customer._id}>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2">{customer.name}</Typography>
                    {customer.demographics?.occupation && (
                      <Typography variant="caption" color="textSecondary">
                        {customer.demographics.occupation}
                      </Typography>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Box>
                    {customer.phone && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                        <PhoneIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                        <Typography variant="body2">{customer.phone}</Typography>
                      </Box>
                    )}
                    {customer.email && (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <EmailIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                        <Typography variant="body2">{customer.email}</Typography>
                      </Box>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  {customer.address?.district || customer.address?.region ? (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <LocationIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {[customer.address.district, customer.address.region].filter(Boolean).join(', ')}
                      </Typography>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="textSecondary">-</Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2" color="primary">
                      {formatCurrency(customer.total_purchases)}
                    </Typography>
                    {customer.last_purchase_date && (
                      <Typography variant="caption" color="textSecondary">
                        {language === 'sw' ? 'Mwisho:' : 'Last:'} {formatDate(customer.last_purchase_date)}
                      </Typography>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => handleOpenDialog(customer)}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => {
                      setCustomerToDelete(customer);
                      setDeleteDialogOpen(true);
                    }}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredCustomers.length === 0 && !loading && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <PeopleIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="textSecondary">
            {language === 'sw' ? 'Hakuna wateja' : 'No customers found'}
          </Typography>
        </Box>
      )}

      {/* Add/Edit Customer Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingCustomer 
            ? (language === 'sw' ? 'Hariri Mteja' : 'Edit Customer')
            : (language === 'sw' ? 'Ongeza Mteja' : 'Add Customer')
          }
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Jina Kamili' : 'Full Name'}
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                error={!!formErrors.name}
                helperText={formErrors.name}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Nambari ya Simu' : 'Phone Number'}
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                error={!!formErrors.phone}
                helperText={formErrors.phone}
                placeholder="+255XXXXXXXXX"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Barua Pepe' : 'Email Address'}
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                error={!!formErrors.email}
                helperText={formErrors.email}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Kazi' : 'Occupation'}
                value={formData.occupation}
                onChange={(e) => setFormData({ ...formData, occupation: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Barabara/Mtaa' : 'Street Address'}
                value={formData.street}
                onChange={(e) => setFormData({ ...formData, street: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Kata' : 'Ward'}
                value={formData.ward}
                onChange={(e) => setFormData({ ...formData, ward: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Wilaya' : 'District'}
                value={formData.district}
                onChange={(e) => setFormData({ ...formData, district: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Mkoa' : 'Region'}
                value={formData.region}
                onChange={(e) => setFormData({ ...formData, region: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {language === 'sw' ? 'Ghairi' : 'Cancel'}
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={submitting}
          >
            {submitting ? <CircularProgress size={20} /> : 
              (editingCustomer 
                ? (language === 'sw' ? 'Sasisha' : 'Update')
                : (language === 'sw' ? 'Ongeza' : 'Add')
              )
            }
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>
          {language === 'sw' ? 'Thibitisha Kufuta' : 'Confirm Delete'}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {language === 'sw' 
              ? `Je, una uhakika unataka kufuta mteja "${customerToDelete?.name}"?`
              : `Are you sure you want to delete the customer "${customerToDelete?.name}"?`
            }
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            {language === 'sw' ? 'Ghairi' : 'Cancel'}
          </Button>
          <Button 
            onClick={handleDelete} 
            color="error" 
            variant="contained"
            disabled={submitting}
          >
            {submitting ? <CircularProgress size={20} /> : 
              (language === 'sw' ? 'Futa' : 'Delete')
            }
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
