/**
 * Sales Management Page
 * Complete CRUD operations for sales with bilingual support
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  InputAdornment,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Divider,
  Autocomplete
} from '@mui/material';
import {
  Add as AddIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  ShoppingCart as SalesIcon,
  Receipt as ReceiptIcon
} from '@mui/icons-material';
import { api } from '../../services/api';

interface Sale {
  _id: string;
  customer_name?: string;
  customer_phone?: string;
  items: Array<{
    product_id: string;
    product_name: string;
    quantity: number;
    unit_price: number;
    total_price: number;
  }>;
  total_amount: number;
  payment_method: string;
  payment_status: string;
  created_at: string;
  created_by: string;
}

interface Product {
  _id: string;
  name: string;
  price: number;
  stock_quantity: number;
}

interface SaleItem {
  product_id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

export const SalesPage: React.FC = () => {
  const [sales, setSales] = useState<Sale[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [language] = useState<'en' | 'sw'>('sw');
  
  // Dialog states
  const [newSaleDialogOpen, setNewSaleDialogOpen] = useState(false);
  const [viewSaleDialogOpen, setViewSaleDialogOpen] = useState(false);
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  
  // New sale form state
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [saleItems, setSaleItems] = useState<SaleItem[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchSales();
    fetchProducts();
  }, []);

  const fetchSales = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.get('/sales');
      setSales(response.data.data || []);
    } catch (err: any) {
      console.error('Failed to fetch sales:', err);
      setError(err.response?.data?.message || 'Failed to load sales');
    } finally {
      setLoading(false);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await api.get('/products');
      setProducts(response.data.data || []);
    } catch (err: any) {
      console.error('Failed to fetch products:', err);
    }
  };

  const handleAddItem = () => {
    if (!selectedProduct || quantity <= 0) return;

    const existingItemIndex = saleItems.findIndex(item => item.product_id === selectedProduct._id);
    
    if (existingItemIndex >= 0) {
      // Update existing item
      const updatedItems = [...saleItems];
      updatedItems[existingItemIndex].quantity += quantity;
      updatedItems[existingItemIndex].total_price = 
        updatedItems[existingItemIndex].quantity * selectedProduct.price;
      setSaleItems(updatedItems);
    } else {
      // Add new item
      const newItem: SaleItem = {
        product_id: selectedProduct._id,
        product_name: selectedProduct.name,
        quantity,
        unit_price: selectedProduct.price,
        total_price: quantity * selectedProduct.price
      };
      setSaleItems([...saleItems, newItem]);
    }

    setSelectedProduct(null);
    setQuantity(1);
  };

  const handleRemoveItem = (productId: string) => {
    setSaleItems(saleItems.filter(item => item.product_id !== productId));
  };

  const calculateTotal = () => {
    return saleItems.reduce((total, item) => total + item.total_price, 0);
  };

  const handleCreateSale = async () => {
    if (saleItems.length === 0) {
      setError(language === 'sw' ? 'Ongeza bidhaa kwanza' : 'Add products first');
      return;
    }

    try {
      setSubmitting(true);
      
      const saleData = {
        customer_name: customerName.trim() || undefined,
        customer_phone: customerPhone.trim() || undefined,
        items: saleItems,
        payment_method: paymentMethod
      };

      await api.post('/sales', saleData);
      await fetchSales();
      
      // Reset form
      setCustomerName('');
      setCustomerPhone('');
      setSaleItems([]);
      setPaymentMethod('cash');
      setNewSaleDialogOpen(false);
      
    } catch (err: any) {
      console.error('Failed to create sale:', err);
      setError(err.response?.data?.message || 'Failed to create sale');
    } finally {
      setSubmitting(false);
    }
  };

  const filteredSales = sales.filter(sale =>
    (sale.customer_name && sale.customer_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (sale.customer_phone && sale.customer_phone.includes(searchTerm)) ||
    sale.items.some(item => item.product_name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sw-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPaymentMethodLabel = (method: string) => {
    const methods: Record<string, { sw: string; en: string }> = {
      cash: { sw: 'Taslimu', en: 'Cash' },
      mpesa: { sw: 'M-Pesa', en: 'M-Pesa' },
      bank: { sw: 'Benki', en: 'Bank Transfer' },
      credit: { sw: 'Mkopo', en: 'Credit' }
    };
    return methods[method] ? methods[method][language] : method;
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          {language === 'sw' ? 'Mauzo' : 'Sales'}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setNewSaleDialogOpen(true)}
        >
          {language === 'sw' ? 'Uzo Mpya' : 'New Sale'}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Search */}
      <TextField
        fullWidth
        variant="outlined"
        placeholder={language === 'sw' ? 'Tafuta mauzo...' : 'Search sales...'}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      {/* Sales Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{language === 'sw' ? 'Mteja' : 'Customer'}</TableCell>
              <TableCell>{language === 'sw' ? 'Bidhaa' : 'Items'}</TableCell>
              <TableCell>{language === 'sw' ? 'Jumla' : 'Total'}</TableCell>
              <TableCell>{language === 'sw' ? 'Malipo' : 'Payment'}</TableCell>
              <TableCell>{language === 'sw' ? 'Tarehe' : 'Date'}</TableCell>
              <TableCell>{language === 'sw' ? 'Vitendo' : 'Actions'}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredSales.map((sale) => (
              <TableRow key={sale._id}>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2">
                      {sale.customer_name || (language === 'sw' ? 'Mteja' : 'Customer')}
                    </Typography>
                    {sale.customer_phone && (
                      <Typography variant="caption" color="textSecondary">
                        {sale.customer_phone}
                      </Typography>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {sale.items.length} {language === 'sw' ? 'bidhaa' : 'items'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle1" color="primary">
                    {formatCurrency(sale.total_amount)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getPaymentMethodLabel(sale.payment_method)}
                    color={getPaymentStatusColor(sale.payment_status) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatDate(sale.created_at)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => {
                      setSelectedSale(sale);
                      setViewSaleDialogOpen(true);
                    }}
                    color="primary"
                  >
                    <ViewIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredSales.length === 0 && !loading && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <SalesIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="textSecondary">
            {language === 'sw' ? 'Hakuna mauzo' : 'No sales found'}
          </Typography>
        </Box>
      )}

      {/* New Sale Dialog */}
      <Dialog open={newSaleDialogOpen} onClose={() => setNewSaleDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {language === 'sw' ? 'Uzo Mpya' : 'New Sale'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {/* Customer Information */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Jina la Mteja' : 'Customer Name'}
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Nambari ya Simu' : 'Phone Number'}
                value={customerPhone}
                onChange={(e) => setCustomerPhone(e.target.value)}
              />
            </Grid>

            {/* Add Products */}
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={products}
                getOptionLabel={(option) => option.name}
                value={selectedProduct}
                onChange={(_, newValue) => setSelectedProduct(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={language === 'sw' ? 'Chagua Bidhaa' : 'Select Product'}
                    fullWidth
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Idadi' : 'Quantity'}
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                inputProps={{ min: 1 }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                onClick={handleAddItem}
                disabled={!selectedProduct}
                sx={{ height: '56px' }}
              >
                {language === 'sw' ? 'Ongeza' : 'Add'}
              </Button>
            </Grid>

            {/* Sale Items */}
            {saleItems.length > 0 && (
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {language === 'sw' ? 'Bidhaa Zilizochaguliwa' : 'Selected Items'}
                    </Typography>
                    <List>
                      {saleItems.map((item, index) => (
                        <React.Fragment key={item.product_id}>
                          <ListItem
                            secondaryAction={
                              <IconButton
                                edge="end"
                                onClick={() => handleRemoveItem(item.product_id)}
                                color="error"
                              >
                                <DeleteIcon />
                              </IconButton>
                            }
                          >
                            <ListItemText
                              primary={item.product_name}
                              secondary={
                                <Box>
                                  <Typography variant="body2">
                                    {item.quantity} × {formatCurrency(item.unit_price)} = {formatCurrency(item.total_price)}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItem>
                          {index < saleItems.length - 1 && <Divider />}
                        </React.Fragment>
                      ))}
                    </List>
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="h6">
                        {language === 'sw' ? 'Jumla:' : 'Total:'}
                      </Typography>
                      <Typography variant="h6" color="primary">
                        {formatCurrency(calculateTotal())}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewSaleDialogOpen(false)}>
            {language === 'sw' ? 'Ghairi' : 'Cancel'}
          </Button>
          <Button 
            onClick={handleCreateSale} 
            variant="contained"
            disabled={submitting || saleItems.length === 0}
          >
            {submitting ? <CircularProgress size={20} /> : 
              (language === 'sw' ? 'Uza' : 'Complete Sale')
            }
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Sale Dialog */}
      <Dialog open={viewSaleDialogOpen} onClose={() => setViewSaleDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ReceiptIcon sx={{ mr: 1 }} />
            {language === 'sw' ? 'Maelezo ya Uzo' : 'Sale Details'}
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedSale && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {language === 'sw' ? 'Mteja' : 'Customer'}
                  </Typography>
                  <Typography variant="body1">
                    {selectedSale.customer_name || (language === 'sw' ? 'Mteja' : 'Customer')}
                  </Typography>
                  {selectedSale.customer_phone && (
                    <Typography variant="body2" color="textSecondary">
                      {selectedSale.customer_phone}
                    </Typography>
                  )}
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {language === 'sw' ? 'Tarehe' : 'Date'}
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(selectedSale.created_at)}
                  </Typography>
                </Grid>
              </Grid>

              <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
                {language === 'sw' ? 'Bidhaa' : 'Items'}
              </Typography>
              <List>
                {selectedSale.items.map((item, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemText
                        primary={item.product_name}
                        secondary={
                          <Typography variant="body2">
                            {item.quantity} × {formatCurrency(item.unit_price)} = {formatCurrency(item.total_price)}
                          </Typography>
                        }
                      />
                    </ListItem>
                    {index < selectedSale.items.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>

              <Divider sx={{ my: 2 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  {language === 'sw' ? 'Jumla:' : 'Total:'}
                </Typography>
                <Typography variant="h6" color="primary">
                  {formatCurrency(selectedSale.total_amount)}
                </Typography>
              </Box>

              <Box sx={{ mt: 2 }}>
                <Chip
                  label={getPaymentMethodLabel(selectedSale.payment_method)}
                  color={getPaymentStatusColor(selectedSale.payment_status) as any}
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewSaleDialogOpen(false)}>
            {language === 'sw' ? 'Funga' : 'Close'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
