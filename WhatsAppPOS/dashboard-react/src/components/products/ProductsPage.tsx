/**
 * Products Management Page
 * Complete CRUD operations for products with bilingual support
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Fab,
  InputAdornment,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Inventory as InventoryIcon
} from '@mui/icons-material';
import { api } from '../../services/api';

interface Product {
  _id: string;
  name: string;
  description?: string;
  price: number;
  cost?: number;
  stock_quantity: number;
  category: string;
  sku?: string;
  is_active: boolean;
  created_at: string;
}

interface ProductFormData {
  name: string;
  description: string;
  price: string;
  cost: string;
  stock_quantity: string;
  category: string;
  sku: string;
}

const categories = [
  { value: 'food', label: 'Chakula', labelEn: 'Food' },
  { value: 'drinks', label: 'Vinywaji', labelEn: 'Drinks' },
  { value: 'electronics', label: 'Vifaa vya Umeme', labelEn: 'Electronics' },
  { value: 'clothing', label: 'Nguo', labelEn: 'Clothing' },
  { value: 'household', label: 'Vifaa vya Nyumbani', labelEn: 'Household' },
  { value: 'other', label: 'Nyingine', labelEn: 'Other' }
];

export const ProductsPage: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [language] = useState<'en' | 'sw'>('sw');
  
  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  
  // Form state
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    price: '',
    cost: '',
    stock_quantity: '',
    category: '',
    sku: ''
  });
  const [formErrors, setFormErrors] = useState<Partial<ProductFormData>>({});
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.get('/products');
      setProducts(response.data.data || []);
    } catch (err: any) {
      console.error('Failed to fetch products:', err);
      setError(err.response?.data?.message || 'Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (product?: Product) => {
    if (product) {
      setEditingProduct(product);
      setFormData({
        name: product.name,
        description: product.description || '',
        price: product.price.toString(),
        cost: product.cost?.toString() || '',
        stock_quantity: product.stock_quantity.toString(),
        category: product.category,
        sku: product.sku || ''
      });
    } else {
      setEditingProduct(null);
      setFormData({
        name: '',
        description: '',
        price: '',
        cost: '',
        stock_quantity: '',
        category: '',
        sku: ''
      });
    }
    setFormErrors({});
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingProduct(null);
    setFormData({
      name: '',
      description: '',
      price: '',
      cost: '',
      stock_quantity: '',
      category: '',
      sku: ''
    });
    setFormErrors({});
  };

  const validateForm = (): boolean => {
    const errors: Partial<ProductFormData> = {};

    if (!formData.name.trim()) {
      errors.name = language === 'sw' ? 'Jina la bidhaa linahitajika' : 'Product name is required';
    }

    if (!formData.price || parseFloat(formData.price) <= 0) {
      errors.price = language === 'sw' ? 'Bei sahihi inahitajika' : 'Valid price is required';
    }

    if (!formData.stock_quantity || parseInt(formData.stock_quantity) < 0) {
      errors.stock_quantity = language === 'sw' ? 'Idadi sahihi inahitajika' : 'Valid quantity is required';
    }

    if (!formData.category) {
      errors.category = language === 'sw' ? 'Aina ya bidhaa inahitajika' : 'Category is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setSubmitting(true);
      
      const productData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: parseFloat(formData.price),
        cost: formData.cost ? parseFloat(formData.cost) : undefined,
        stock_quantity: parseInt(formData.stock_quantity),
        category: formData.category,
        sku: formData.sku.trim() || undefined
      };

      if (editingProduct) {
        await api.put(`/products/${editingProduct._id}`, productData);
      } else {
        await api.post('/products', productData);
      }

      await fetchProducts();
      handleCloseDialog();
      
    } catch (err: any) {
      console.error('Failed to save product:', err);
      setError(err.response?.data?.message || 'Failed to save product');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!productToDelete) return;

    try {
      setSubmitting(true);
      await api.delete(`/products/${productToDelete._id}`);
      await fetchProducts();
      setDeleteDialogOpen(false);
      setProductToDelete(null);
    } catch (err: any) {
      console.error('Failed to delete product:', err);
      setError(err.response?.data?.message || 'Failed to delete product');
    } finally {
      setSubmitting(false);
    }
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.sku && product.sku.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getCategoryLabel = (category: string) => {
    const cat = categories.find(c => c.value === category);
    return cat ? (language === 'sw' ? cat.label : cat.labelEn) : category;
  };

  const getStockStatus = (quantity: number) => {
    if (quantity === 0) {
      return { 
        label: language === 'sw' ? 'Hakuna' : 'Out of Stock', 
        color: 'error' as const 
      };
    } else if (quantity < 10) {
      return { 
        label: language === 'sw' ? 'Kidogo' : 'Low Stock', 
        color: 'warning' as const 
      };
    } else {
      return { 
        label: language === 'sw' ? 'Kutosha' : 'In Stock', 
        color: 'success' as const 
      };
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          {language === 'sw' ? 'Bidhaa' : 'Products'}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          {language === 'sw' ? 'Ongeza Bidhaa' : 'Add Product'}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Search */}
      <TextField
        fullWidth
        variant="outlined"
        placeholder={language === 'sw' ? 'Tafuta bidhaa...' : 'Search products...'}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      {/* Products Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{language === 'sw' ? 'Jina' : 'Name'}</TableCell>
              <TableCell>{language === 'sw' ? 'Aina' : 'Category'}</TableCell>
              <TableCell>{language === 'sw' ? 'Bei' : 'Price'}</TableCell>
              <TableCell>{language === 'sw' ? 'Idadi' : 'Stock'}</TableCell>
              <TableCell>{language === 'sw' ? 'Hali' : 'Status'}</TableCell>
              <TableCell>{language === 'sw' ? 'Vitendo' : 'Actions'}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredProducts.map((product) => {
              const stockStatus = getStockStatus(product.stock_quantity);
              return (
                <TableRow key={product._id}>
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2">{product.name}</Typography>
                      {product.sku && (
                        <Typography variant="caption" color="textSecondary">
                          SKU: {product.sku}
                        </Typography>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>{getCategoryLabel(product.category)}</TableCell>
                  <TableCell>{formatCurrency(product.price)}</TableCell>
                  <TableCell>{product.stock_quantity}</TableCell>
                  <TableCell>
                    <Chip
                      label={stockStatus.label}
                      color={stockStatus.color}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(product)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => {
                        setProductToDelete(product);
                        setDeleteDialogOpen(true);
                      }}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredProducts.length === 0 && !loading && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <InventoryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="textSecondary">
            {language === 'sw' ? 'Hakuna bidhaa' : 'No products found'}
          </Typography>
        </Box>
      )}

      {/* Add/Edit Product Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingProduct 
            ? (language === 'sw' ? 'Hariri Bidhaa' : 'Edit Product')
            : (language === 'sw' ? 'Ongeza Bidhaa' : 'Add Product')
          }
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Jina la Bidhaa' : 'Product Name'}
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                error={!!formErrors.name}
                helperText={formErrors.name}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="SKU"
                value={formData.sku}
                onChange={(e) => setFormData({ ...formData, sku: e.target.value })}
                error={!!formErrors.sku}
                helperText={formErrors.sku}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Maelezo' : 'Description'}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required error={!!formErrors.category}>
                <InputLabel>{language === 'sw' ? 'Aina' : 'Category'}</InputLabel>
                <Select
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  label={language === 'sw' ? 'Aina' : 'Category'}
                >
                  {categories.map((category) => (
                    <MenuItem key={category.value} value={category.value}>
                      {language === 'sw' ? category.label : category.labelEn}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Idadi' : 'Stock Quantity'}
                type="number"
                value={formData.stock_quantity}
                onChange={(e) => setFormData({ ...formData, stock_quantity: e.target.value })}
                error={!!formErrors.stock_quantity}
                helperText={formErrors.stock_quantity}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Bei ya Mauzo' : 'Selling Price'}
                type="number"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                error={!!formErrors.price}
                helperText={formErrors.price}
                InputProps={{
                  startAdornment: <InputAdornment position="start">TZS</InputAdornment>,
                }}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={language === 'sw' ? 'Bei ya Ununuzi' : 'Cost Price'}
                type="number"
                value={formData.cost}
                onChange={(e) => setFormData({ ...formData, cost: e.target.value })}
                InputProps={{
                  startAdornment: <InputAdornment position="start">TZS</InputAdornment>,
                }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {language === 'sw' ? 'Ghairi' : 'Cancel'}
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={submitting}
          >
            {submitting ? <CircularProgress size={20} /> : 
              (editingProduct 
                ? (language === 'sw' ? 'Sasisha' : 'Update')
                : (language === 'sw' ? 'Ongeza' : 'Add')
              )
            }
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>
          {language === 'sw' ? 'Thibitisha Kufuta' : 'Confirm Delete'}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {language === 'sw' 
              ? `Je, una uhakika unataka kufuta bidhaa "${productToDelete?.name}"?`
              : `Are you sure you want to delete the product "${productToDelete?.name}"?`
            }
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            {language === 'sw' ? 'Ghairi' : 'Cancel'}
          </Button>
          <Button 
            onClick={handleDelete} 
            color="error" 
            variant="contained"
            disabled={submitting}
          >
            {submitting ? <CircularProgress size={20} /> : 
              (language === 'sw' ? 'Futa' : 'Delete')
            }
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
