import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Link,
  InputAdornment,
  MenuItem,
  Stepper,
  Step,
  StepLabel,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Business,
  Person,
  Phone,
  LocationOn,
  WhatsApp
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { authService } from '@services/authService';
import { RegisterRequest } from '@types/index';

// Validation schema
const registerSchema = yup.object({
  name: yup
    .string()
    .required('Business name is required')
    .min(2, 'Business name must be at least 2 characters')
    .max(100, 'Business name must be less than 100 characters'),
  phone: yup
    .string()
    .required('Phone number is required')
    .matches(/^\+255[67]\d{8}$/, 'Please enter a valid Tanzanian phone number (+255XXXXXXXXX)'),
  owner_name: yup
    .string()
    .required('Owner name is required')
    .min(2, 'Owner name must be at least 2 characters')
    .max(100, 'Owner name must be less than 100 characters'),
  business_type: yup
    .string()
    .required('Business type is required'),
  location: yup.object({
    address: yup.string().optional(),
    city: yup.string().optional(),
    region: yup.string().optional()
  }).optional(),
  language: yup
    .string()
    .oneOf(['sw', 'en'])
    .optional()
});

interface RegisterFormProps {
  onSuccess: () => void;
  onSwitchToLogin: () => void;
  language: 'sw' | 'en';
}

const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess, onSwitchToLogin, language }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);

  const {
    control,
    handleSubmit,
    formState: { errors },
    trigger,
    getValues
  } = useForm<RegisterRequest>({
    resolver: yupResolver(registerSchema),
    defaultValues: {
      name: '',
      phone: '',
      owner_name: '',
      business_type: '',
      location: {
        address: '',
        city: '',
        region: ''
      },
      language: language
    }
  });

  const translations = {
    sw: {
      title: 'Sajili Biashara Yako',
      subtitle: 'Anza safari yako ya biashara ya kidijitali',
      businessInfo: 'Taarifa za Biashara',
      ownerInfo: 'Taarifa za Mmiliki',
      locationInfo: 'Mahali',
      businessName: 'Jina la Biashara',
      businessNamePlaceholder: 'Mfano: Duka la Mama Juma',
      phone: 'Nambari ya Simu',
      phonePlaceholder: '+255XXXXXXXXX',
      ownerName: 'Jina la Mmiliki',
      ownerNamePlaceholder: 'Jina lako kamili',
      businessType: 'Aina ya Biashara',
      address: 'Anwani',
      addressPlaceholder: 'Mfano: Mtaa wa Uhuru, Jengo la 5',
      city: 'Jiji',
      cityPlaceholder: 'Mfano: Dar es Salaam',
      region: 'Mkoa',
      regionPlaceholder: 'Mfano: Dar es Salaam',
      next: 'Endelea',
      back: 'Rudi',
      register: 'Sajili',
      haveAccount: 'Una akaunti tayari?',
      login: 'Ingia hapa',
      businessTypes: {
        duka: 'Duka la Jumla',
        restaurant: 'Mgahawa',
        pharmacy: 'Duka la Dawa',
        electronics: 'Vifaa vya Umeme',
        clothing: 'Nguo',
        hardware: 'Vifaa vya Ujenzi',
        salon: 'Saluni',
        other: 'Nyingine'
      }
    },
    en: {
      title: 'Register Your Business',
      subtitle: 'Start your digital business journey',
      businessInfo: 'Business Information',
      ownerInfo: 'Owner Information',
      locationInfo: 'Location',
      businessName: 'Business Name',
      businessNamePlaceholder: 'e.g., Mama Juma Shop',
      phone: 'Phone Number',
      phonePlaceholder: '+255XXXXXXXXX',
      ownerName: 'Owner Name',
      ownerNamePlaceholder: 'Your full name',
      businessType: 'Business Type',
      address: 'Address',
      addressPlaceholder: 'e.g., Uhuru Street, Building 5',
      city: 'City',
      cityPlaceholder: 'e.g., Dar es Salaam',
      region: 'Region',
      regionPlaceholder: 'e.g., Dar es Salaam',
      next: 'Next',
      back: 'Back',
      register: 'Register',
      haveAccount: 'Already have an account?',
      login: 'Login here',
      businessTypes: {
        duka: 'General Store',
        restaurant: 'Restaurant',
        pharmacy: 'Pharmacy',
        electronics: 'Electronics',
        clothing: 'Clothing',
        hardware: 'Hardware',
        salon: 'Salon',
        other: 'Other'
      }
    }
  };

  const t = translations[language];

  const steps = [t.businessInfo, t.ownerInfo, t.locationInfo];

  const handleNext = async () => {
    const fieldsToValidate = getFieldsForStep(activeStep);
    const isValid = await trigger(fieldsToValidate);
    
    if (isValid) {
      setActiveStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const getFieldsForStep = (step: number): (keyof RegisterRequest)[] => {
    switch (step) {
      case 0:
        return ['name', 'business_type'];
      case 1:
        return ['owner_name', 'phone'];
      case 2:
        return ['location'];
      default:
        return [];
    }
  };

  const handleRegister = async (data: RegisterRequest) => {
    setIsLoading(true);
    setError(null);

    try {
      await authService.register(data);
      onSuccess();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label={t.businessName}
                  placeholder={t.businessNamePlaceholder}
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Business />
                      </InputAdornment>
                    )
                  }}
                  sx={{ mb: 3 }}
                />
              )}
            />

            <Controller
              name="business_type"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  select
                  label={t.businessType}
                  error={!!errors.business_type}
                  helperText={errors.business_type?.message}
                  sx={{ mb: 3 }}
                >
                  {Object.entries(t.businessTypes).map(([key, value]) => (
                    <MenuItem key={key} value={key}>
                      {value}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Box>
        );

      case 1:
        return (
          <Box>
            <Controller
              name="owner_name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label={t.ownerName}
                  placeholder={t.ownerNamePlaceholder}
                  error={!!errors.owner_name}
                  helperText={errors.owner_name?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Person />
                      </InputAdornment>
                    )
                  }}
                  sx={{ mb: 3 }}
                />
              )}
            />

            <Controller
              name="phone"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label={t.phone}
                  placeholder={t.phonePlaceholder}
                  error={!!errors.phone}
                  helperText={errors.phone?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Phone />
                      </InputAdornment>
                    )
                  }}
                  sx={{ mb: 3 }}
                />
              )}
            />
          </Box>
        );

      case 2:
        return (
          <Box>
            <Controller
              name="location.address"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label={t.address}
                  placeholder={t.addressPlaceholder}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LocationOn />
                      </InputAdornment>
                    )
                  }}
                  sx={{ mb: 3 }}
                />
              )}
            />

            <Controller
              name="location.city"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label={t.city}
                  placeholder={t.cityPlaceholder}
                  sx={{ mb: 3 }}
                />
              )}
            />

            <Controller
              name="location.region"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label={t.region}
                  placeholder={t.regionPlaceholder}
                  sx={{ mb: 3 }}
                />
              )}
            />
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: 2
      }}
    >
      <Card
        sx={{
          maxWidth: 500,
          width: '100%',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          borderRadius: 3
        }}
      >
        <CardContent sx={{ p: 4 }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
              <WhatsApp sx={{ fontSize: 40, color: '#25d366', mr: 1 }} />
              <Business sx={{ fontSize: 40, color: '#667eea' }} />
            </Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
              {t.title}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {t.subtitle}
            </Typography>
          </Box>

          {/* Stepper */}
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Error Alert */}
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit(handleRegister)}>
            {renderStepContent(activeStep)}

            {/* Navigation Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button
                onClick={handleBack}
                disabled={activeStep === 0}
                variant="outlined"
              >
                {t.back}
              </Button>

              {activeStep === steps.length - 1 ? (
                <Button
                  type="submit"
                  variant="contained"
                  disabled={isLoading}
                  sx={{ minWidth: 120 }}
                >
                  {isLoading ? <CircularProgress size={24} /> : t.register}
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  variant="contained"
                >
                  {t.next}
                </Button>
              )}
            </Box>
          </form>

          {/* Login Link */}
          <Box sx={{ textAlign: 'center', mt: 3 }}>
            <Typography variant="body2" color="text.secondary">
              {t.haveAccount}{' '}
              <Link
                component="button"
                variant="body2"
                onClick={onSwitchToLogin}
                sx={{ textDecoration: 'none' }}
              >
                {t.login}
              </Link>
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default RegisterForm;
