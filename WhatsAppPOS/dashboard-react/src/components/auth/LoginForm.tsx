import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Link,
  InputAdornment,
  IconButton,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Phone,
  WhatsApp,
  Business
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { authService } from '@services/authService';
import { LoginRequest } from '@types/index';

// Validation schema
const loginSchema = yup.object({
  phone: yup
    .string()
    .required('Phone number is required')
    .matches(/^\+255[67]\d{8}$/, 'Please enter a valid Tanzanian phone number (+255XXXXXXXXX)'),
  password: yup
    .string()
    .min(6, 'Password must be at least 6 characters')
    .optional()
});

interface LoginFormProps {
  onSuccess: () => void;
  onSwitchToRegister: () => void;
  language: 'sw' | 'en';
}

const LoginForm: React.FC<LoginFormProps> = ({ onSuccess, onSwitchToRegister, language }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isWhatsAppLogin, setIsWhatsAppLogin] = useState(true);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<LoginRequest>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      phone: '',
      password: ''
    }
  });

  const phoneValue = watch('phone');

  const translations = {
    sw: {
      title: 'Ingia kwenye Akaunti Yako',
      subtitle: 'Simamia biashara yako kwa urahisi',
      phone: 'Nambari ya Simu',
      phonePlaceholder: '+255XXXXXXXXX',
      password: 'Nenosiri',
      passwordPlaceholder: 'Ingiza nenosiri lako',
      loginButton: 'Ingia',
      whatsappLogin: 'Ingia kwa WhatsApp',
      dashboardLogin: 'Ingia kwa Dashboard',
      noAccount: 'Huna akaunti?',
      register: 'Jisajili hapa',
      forgotPassword: 'Umesahau nenosiri?',
      whatsappNote: 'Tuma ujumbe wa "login" kwa WhatsApp ili kupata kiungo cha kuingia',
      or: 'AU'
    },
    en: {
      title: 'Login to Your Account',
      subtitle: 'Manage your business with ease',
      phone: 'Phone Number',
      phonePlaceholder: '+255XXXXXXXXX',
      password: 'Password',
      passwordPlaceholder: 'Enter your password',
      loginButton: 'Login',
      whatsappLogin: 'Login via WhatsApp',
      dashboardLogin: 'Dashboard Login',
      noAccount: "Don't have an account?",
      register: 'Register here',
      forgotPassword: 'Forgot password?',
      whatsappNote: 'Send "login" message to WhatsApp to get login link',
      or: 'OR'
    }
  };

  const t = translations[language];

  const handleLogin = async (data: LoginRequest) => {
    setIsLoading(true);
    setError(null);

    try {
      await authService.login(data);
      onSuccess();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleWhatsAppLogin = () => {
    if (phoneValue) {
      // Format phone number for WhatsApp
      const whatsappNumber = phoneValue.replace('+', '');
      const message = encodeURIComponent('login');
      const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
      window.open(whatsappUrl, '_blank');
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: 2
      }}
    >
      <Card
        sx={{
          maxWidth: 450,
          width: '100%',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          borderRadius: 3
        }}
      >
        <CardContent sx={{ p: 4 }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
              <WhatsApp sx={{ fontSize: 40, color: '#25d366', mr: 1 }} />
              <Business sx={{ fontSize: 40, color: '#667eea' }} />
            </Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
              {t.title}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {t.subtitle}
            </Typography>
          </Box>

          {/* Error Alert */}
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Login Method Toggle */}
          <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
            <Button
              fullWidth
              variant={isWhatsAppLogin ? 'contained' : 'outlined'}
              onClick={() => setIsWhatsAppLogin(true)}
              startIcon={<WhatsApp />}
              sx={{ 
                backgroundColor: isWhatsAppLogin ? '#25d366' : 'transparent',
                '&:hover': {
                  backgroundColor: isWhatsAppLogin ? '#1da851' : 'rgba(37, 211, 102, 0.1)'
                }
              }}
            >
              WhatsApp
            </Button>
            <Button
              fullWidth
              variant={!isWhatsAppLogin ? 'contained' : 'outlined'}
              onClick={() => setIsWhatsAppLogin(false)}
              startIcon={<Business />}
            >
              Dashboard
            </Button>
          </Box>

          {/* WhatsApp Login */}
          {isWhatsAppLogin ? (
            <Box>
              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label={t.phone}
                    placeholder={t.phonePlaceholder}
                    error={!!errors.phone}
                    helperText={errors.phone?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Phone />
                        </InputAdornment>
                      )
                    }}
                    sx={{ mb: 3 }}
                  />
                )}
              />

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleWhatsAppLogin}
                disabled={!phoneValue || !!errors.phone}
                startIcon={<WhatsApp />}
                sx={{
                  backgroundColor: '#25d366',
                  '&:hover': { backgroundColor: '#1da851' },
                  py: 1.5,
                  mb: 2
                }}
              >
                {t.whatsappLogin}
              </Button>

              <Alert severity="info" sx={{ mb: 3 }}>
                {t.whatsappNote}
              </Alert>
            </Box>
          ) : (
            /* Dashboard Login Form */
            <form onSubmit={handleSubmit(handleLogin)}>
              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label={t.phone}
                    placeholder={t.phonePlaceholder}
                    error={!!errors.phone}
                    helperText={errors.phone?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Phone />
                        </InputAdornment>
                      )
                    }}
                    sx={{ mb: 3 }}
                  />
                )}
              />

              <Controller
                name="password"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type={showPassword ? 'text' : 'password'}
                    label={t.password}
                    placeholder={t.passwordPlaceholder}
                    error={!!errors.password}
                    helperText={errors.password?.message}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      )
                    }}
                    sx={{ mb: 3 }}
                  />
                )}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={isLoading}
                sx={{ py: 1.5, mb: 2 }}
              >
                {isLoading ? <CircularProgress size={24} /> : t.loginButton}
              </Button>

              <Box sx={{ textAlign: 'center', mb: 2 }}>
                <Link href="#" variant="body2" color="primary">
                  {t.forgotPassword}
                </Link>
              </Box>
            </form>
          )}

          <Divider sx={{ my: 3 }}>
            <Typography variant="body2" color="text.secondary">
              {t.or}
            </Typography>
          </Divider>

          {/* Register Link */}
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              {t.noAccount}{' '}
              <Link
                component="button"
                variant="body2"
                onClick={onSwitchToRegister}
                sx={{ textDecoration: 'none' }}
              >
                {t.register}
              </Link>
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default LoginForm;
