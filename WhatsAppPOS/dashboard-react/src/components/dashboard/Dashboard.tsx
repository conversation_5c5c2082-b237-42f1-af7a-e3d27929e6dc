/**
 * Main Dashboard Component
 * Displays business overview, statistics, and recent activity
 */

import React, { useState, useEffect } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  TrendingUp,
  Inventory,
  People,
  AttachMoney,
  ShoppingCart,
  Warning,
  CheckCircle
} from '@mui/icons-material';
import { api } from '../../services/api';

interface DashboardStats {
  totalRevenue: number;
  totalProducts: number;
  totalSales: number;
  totalCustomers: number;
  lowStockProducts: number;
  recentSales: Array<{
    id: string;
    customer_name?: string;
    total_amount: number;
    created_at: string;
    items: Array<{
      product_name: string;
      quantity: number;
    }>;
  }>;
}

interface StatCardProps {
  title: string;
  titleSw: string;
  value: string | number;
  icon: React.ReactElement;
  color: string;
  language: 'en' | 'sw';
}

const StatCard: React.FC<StatCardProps> = ({ title, titleSw, value, icon, color, language }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="overline">
            {language === 'sw' ? titleSw : title}
          </Typography>
          <Typography variant="h4" component="div">
            {value}
          </Typography>
        </Box>
        <Box sx={{ color, display: 'flex', alignItems: 'center' }}>
          {icon}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

export const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [language] = useState<'en' | 'sw'>('sw'); // TODO: Get from context

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch dashboard statistics
      const [reportsResponse, productsResponse, salesResponse, customersResponse] = await Promise.all([
        api.get('/reports/dashboard'),
        api.get('/products?limit=1'),
        api.get('/sales?limit=5'),
        api.get('/customers?limit=1')
      ]);

      const dashboardData = reportsResponse.data.data;
      const recentSales = salesResponse.data.data;

      setStats({
        totalRevenue: dashboardData.totalRevenue || 0,
        totalProducts: dashboardData.totalProducts || 0,
        totalSales: dashboardData.totalSales || 0,
        totalCustomers: dashboardData.totalCustomers || 0,
        lowStockProducts: dashboardData.lowStockProducts || 0,
        recentSales: recentSales || []
      });

    } catch (err: any) {
      console.error('Failed to fetch dashboard data:', err);
      setError(err.response?.data?.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sw-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!stats) {
    return (
      <Alert severity="warning" sx={{ mb: 2 }}>
        {language === 'sw' ? 'Hakuna data ya kuonyesha' : 'No data to display'}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {language === 'sw' ? 'Dashibodi' : 'Dashboard'}
      </Typography>
      
      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Revenue"
            titleSw="Mapato Jumla"
            value={formatCurrency(stats.totalRevenue)}
            icon={<AttachMoney sx={{ fontSize: 40 }} />}
            color="#4caf50"
            language={language}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Products"
            titleSw="Bidhaa"
            value={stats.totalProducts}
            icon={<Inventory sx={{ fontSize: 40 }} />}
            color="#2196f3"
            language={language}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Sales"
            titleSw="Mauzo"
            value={stats.totalSales}
            icon={<ShoppingCart sx={{ fontSize: 40 }} />}
            color="#ff9800"
            language={language}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Customers"
            titleSw="Wateja"
            value={stats.totalCustomers}
            icon={<People sx={{ fontSize: 40 }} />}
            color="#9c27b0"
            language={language}
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Sales */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              {language === 'sw' ? 'Mauzo ya Hivi Karibuni' : 'Recent Sales'}
            </Typography>
            
            {stats.recentSales.length === 0 ? (
              <Typography color="textSecondary">
                {language === 'sw' ? 'Hakuna mauzo ya hivi karibuni' : 'No recent sales'}
              </Typography>
            ) : (
              <List>
                {stats.recentSales.map((sale) => (
                  <ListItem key={sale.id} divider>
                    <ListItemIcon>
                      <ShoppingCart color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="subtitle1">
                            {sale.customer_name || (language === 'sw' ? 'Mteja' : 'Customer')}
                          </Typography>
                          <Typography variant="h6" color="primary">
                            {formatCurrency(sale.total_amount)}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="textSecondary">
                            {sale.items.map(item => `${item.product_name} (${item.quantity})`).join(', ')}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {formatDate(sale.created_at)}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Alerts and Notifications */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              {language === 'sw' ? 'Arifa' : 'Alerts'}
            </Typography>
            
            <List>
              {stats.lowStockProducts > 0 && (
                <ListItem>
                  <ListItemIcon>
                    <Warning color="warning" />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography variant="body2">
                        {language === 'sw' 
                          ? `Bidhaa ${stats.lowStockProducts} zina stock kidogo`
                          : `${stats.lowStockProducts} products have low stock`
                        }
                      </Typography>
                    }
                  />
                  <Chip 
                    label={stats.lowStockProducts} 
                    color="warning" 
                    size="small" 
                  />
                </ListItem>
              )}
              
              <ListItem>
                <ListItemIcon>
                  <CheckCircle color="success" />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography variant="body2">
                      {language === 'sw' 
                        ? 'Mfumo unafanya kazi vizuri'
                        : 'System is running smoothly'
                      }
                    </Typography>
                  }
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};
