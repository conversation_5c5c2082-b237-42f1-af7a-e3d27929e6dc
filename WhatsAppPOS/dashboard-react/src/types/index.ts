// Core Types for WhatsApp POS Dashboard
export interface User {
  _id: string;
  name: string;
  phone: string;
  role: 'owner' | 'manager' | 'employee' | 'viewer';
  business_id: string;
  is_active: boolean;
  preferences: {
    language: 'sw' | 'en';
    notifications: {
      whatsapp: boolean;
      daily_summary: boolean;
      low_stock_alerts: boolean;
    };
    dashboard_layout: 'compact' | 'detailed';
  };
  permissions: UserPermissions;
  created_at: string;
  last_active: string;
}

export interface UserPermissions {
  can_add_products: boolean;
  can_edit_products: boolean;
  can_delete_products: boolean;
  can_record_sales: boolean;
  can_view_sales: boolean;
  can_add_customers: boolean;
  can_edit_customers: boolean;
  can_view_reports: boolean;
  can_manage_users: boolean;
  can_manage_settings: boolean;
}

export interface Business {
  _id: string;
  name: string;
  phone: string;
  owner_name: string;
  business_type: string;
  location?: {
    address?: string;
    city?: string;
    region?: string;
    coordinates?: [number, number];
  };
  subscription: {
    plan: 'trial' | 'basic' | 'premium' | 'enterprise';
    status: 'active' | 'expired' | 'cancelled';
    start_date: string;
    end_date: string;
  };
  settings: {
    language: 'sw' | 'en';
    currency: string;
    timezone: string;
    business_hours: {
      open: string;
      close: string;
      days: string[];
    };
  };
  stats: {
    total_products: number;
    total_sales: number;
    total_revenue: number;
    total_customers: number;
  };
  is_active: boolean;
  created_at: string;
}

export interface Product {
  _id: string;
  business_id: string;
  name: string;
  description?: string;
  price: number;
  cost?: number;
  stock: number;
  low_stock_alert: number;
  category: string;
  sku?: string;
  barcode?: string;
  is_service: boolean;
  is_active: boolean;
  images?: string[];
  tags?: string[];
  sales_data: {
    total_sold: number;
    revenue_generated: number;
    last_sold_date?: string;
  };
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Sale {
  _id: string;
  business_id: string;
  sale_number: string;
  items: SaleItem[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  total_items: number;
  payment: {
    method: 'cash' | 'mpesa' | 'tigo' | 'airtel' | 'bank';
    amount_paid: number;
    change_given: number;
    reference?: string;
  };
  customer_id?: string;
  customer_info?: {
    name?: string;
    phone?: string;
  };
  status: 'pending' | 'completed' | 'cancelled' | 'refunded';
  notes?: string;
  created_by: string;
  sale_date: string;
  created_at: string;
}

export interface SaleItem {
  product_id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  cost?: number;
}

export interface Customer {
  _id: string;
  business_id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  purchase_history: {
    total_purchases: number;
    total_spent: number;
    last_purchase_date?: string;
    average_order_value: number;
  };
  loyalty: {
    points: number;
    tier: 'bronze' | 'silver' | 'gold' | 'platinum';
    tier_benefits: string[];
  };
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  sw?: string;
  en?: string;
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    items: T[];
    pagination: {
      current_page: number;
      total_pages: number;
      total_items: number;
      items_per_page: number;
      has_next: boolean;
      has_prev: boolean;
    };
  };
}

// Authentication Types
export interface LoginRequest {
  phone: string;
  password?: string;
}

export interface LoginResponse {
  success: boolean;
  data: {
    user: User;
    business: Business;
    tokens: {
      access_token: string;
      refresh_token: string;
      expires_in: string;
    };
  };
}

export interface RegisterRequest {
  name: string;
  phone: string;
  owner_name: string;
  business_type: string;
  location?: {
    address?: string;
    city?: string;
    region?: string;
  };
  language?: 'sw' | 'en';
}

// Dashboard Types
export interface DashboardStats {
  today: {
    sales_count: number;
    revenue: number;
    items_sold: number;
  };
  week: {
    total_sales: number;
    total_revenue: number;
  };
  month: {
    total_sales: number;
    total_revenue: number;
  };
  top_products: Array<{
    product_name: string;
    quantity_sold: number;
    revenue: number;
  }>;
  low_stock_products: Product[];
  recent_sales: Sale[];
  business_stats: Business['stats'];
}

// Chart Data Types
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface SalesChartData {
  daily: ChartDataPoint[];
  weekly: ChartDataPoint[];
  monthly: ChartDataPoint[];
}

// Language Types
export type Language = 'sw' | 'en';

export interface TranslationKeys {
  [key: string]: {
    sw: string;
    en: string;
  };
}

// Form Types
export interface ProductFormData {
  name: string;
  description?: string;
  price: number;
  cost?: number;
  stock: number;
  low_stock_alert: number;
  category: string;
  sku?: string;
  is_service: boolean;
  tags?: string[];
}

export interface CustomerFormData {
  name: string;
  phone?: string;
  email?: string;
  address?: string;
}

export interface SaleFormData {
  items: Array<{
    product_id: string;
    quantity: number;
  }>;
  payment_method: 'cash' | 'mpesa' | 'tigo' | 'airtel' | 'bank';
  customer_id?: string;
  notes?: string;
}

// Error Types
export interface AppError {
  message: string;
  code?: string;
  field?: string;
}

// Loading States
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

// Filter Types
export interface ProductFilters {
  search?: string;
  category?: string;
  in_stock?: boolean;
  is_service?: boolean;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface SaleFilters {
  start_date?: string;
  end_date?: string;
  payment_method?: string;
  status?: string;
  customer_id?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface CustomerFilters {
  search?: string;
  loyalty_tier?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}
