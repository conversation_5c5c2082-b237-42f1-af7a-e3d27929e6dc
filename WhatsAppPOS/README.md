# WhatsApp POS Tanzania 🇹🇿

A complete, production-ready WhatsApp Point of Sale system specifically designed for Tanzanian businesses. This system enables small businesses to manage their operations entirely through WhatsApp, with support for both Swahili and English languages.

## 🌟 Features

### WhatsApp Bot (Bilingual: Swahili/English)
- **Business Registration**: Multi-step onboarding with phone verification
- **Product Management**: Add, update, list, delete products with categories
- **Service Management**: Handle both physical products and services
- **Sales Recording**: Quick sale recording with multiple payment methods
- **Inventory Management**: Stock tracking, low stock alerts, automatic deductions
- **Customer Recording**: Basic customer data for repeat business and loyalty tracking
- **Daily/Weekly Reports**: Sales analytics, popular items, revenue summaries
- **Multi-user Support**: Owner can add employees with role-based permissions

### Web Dashboard
- **Business Analytics**: Revenue charts, sales trends, inventory status
- **Product Management**: Bulk product upload via CSV, categories management
- **User Management**: Add/remove employees, set permissions, activity logs
- **Reports Export**: PDF/Excel export for business records
- **Subscription Management**: Plan selection, billing status, usage metrics
- **Multi-language Interface**: Swahili/English toggle

### Payment Methods Supported
- Cash (Fedha taslimu)
- M-Pesa
- Tigo Pesa
- Airtel Money
- Bank Transfer

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm 8+
- MongoDB (local or MongoDB Atlas)
- Redis (local or Redis Cloud)
- Twilio Account with WhatsApp Business API

### 1. Clone and Install
```bash
git clone <repository-url>
cd WhatsAppPOS
npm install
```

### 2. Environment Setup
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
# Database
MONGODB_URI=mongodb://localhost:27017/tanzanian_pos

# JWT Secrets
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_REFRESH_SECRET=your-refresh-token-secret

# Twilio WhatsApp
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
WEBHOOK_URL=https://yourdomain.com/webhook/whatsapp

# Redis
REDIS_URL=redis://localhost:6379
```

### 3. Start Services
```bash
# Development mode
npm run dev

# Production mode
npm start
```

### 4. Access the System
- **Dashboard**: http://localhost:3000
- **API**: http://localhost:3000/api
- **WhatsApp Webhook**: http://localhost:3000/webhook/whatsapp

## 📱 WhatsApp Commands

### Swahili Commands
```
msaada          - Ona maelekezo kamili
sajili          - Sajili biashara mpya
ongeza bidhaa   - Ongeza bidhaa mpya
bidhaa          - Ona bidhaa zote
uza             - Rekodi mauzo
mauzo ya leo    - Ripoti ya leo
ongeza mteja    - Ongeza mteja mpya
wateja          - Ona wateja wote
```

### English Commands
```
help            - View complete instructions
register        - Register new business
add product     - Add new product
products        - View all products
sell            - Record sale
today sales     - Today's report
add customer    - Add new customer
customers       - View all customers
```

## 🏗️ Architecture

### Backend Structure
```
backend/
├── server.js              # Main server entry point
├── app.js                 # Express app configuration
├── models/                # MongoDB schemas
├── routes/                # API endpoints
├── middleware/            # Authentication, validation, etc.
├── services/              # Business logic
├── utils/                 # Utility functions
└── config/                # Database, Redis, Twilio config
```

### Database Schema
- **Businesses**: Business registration and subscription data
- **Users**: Multi-user support with role-based permissions
- **Products**: Inventory management with categories
- **Sales**: Transaction records with payment methods
- **Customers**: Customer data and loyalty tracking

## 🔧 Configuration

### Subscription Plans
```javascript
Trial: 14 days, 50 products, 2 users, 100 monthly sales
Basic: TSh 10,000/month, 500 products, 5 users, 1000 monthly sales
Pro: TSh 25,000/month, unlimited products, 20 users, unlimited sales
```

### Supported Business Types
- Duka (Shop)
- Salon
- Restaurant
- Pharmacy
- Electronics
- Clothing
- Hardware
- Services
- Other

## 🌐 Deployment

### Railway (Recommended)
1. Connect your GitHub repository to Railway
2. Set environment variables in Railway dashboard
3. Deploy automatically on push

### Manual Deployment
```bash
# Build the application
npm run build

# Start production server
NODE_ENV=production npm start
```

### Environment Variables for Production
```env
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb+srv://username:<EMAIL>/tanzanian_pos
REDIS_URL=redis://redis-url:6379
WEBHOOK_URL=https://yourdomain.com/webhook/whatsapp
```

## 🧪 Testing

### Run Tests
```bash
# Unit tests
npm test

# Integration tests
npm run test:integration

# Coverage report
npm run test:coverage
```

### Test WhatsApp Webhook (Development)
```bash
curl -X POST http://localhost:3000/webhook/test/whatsapp \
  -H "Content-Type: application/json" \
  -d '{"From": "whatsapp:+255700000000", "Body": "msaada"}'
```

## 📊 API Documentation

### Authentication
```bash
# Register business
POST /api/auth/register
{
  "name": "Duka la Mama",
  "phone": "+255700000000",
  "owner_name": "Mama Juma",
  "business_type": "duka"
}

# Login
POST /api/auth/login
{
  "phone": "+255700000000"
}
```

### Products
```bash
# Get products
GET /api/products?page=1&limit=20

# Add product
POST /api/products
{
  "name": "Coca Cola",
  "price": 1000,
  "stock": 50,
  "category": "drinks"
}
```

### Sales
```bash
# Record sale
POST /api/sales
{
  "items": [
    {
      "product_id": "product_id_here",
      "quantity": 2
    }
  ],
  "payment_method": "mpesa"
}
```

## 🔒 Security Features

- JWT-based authentication with refresh tokens
- Business data isolation
- Rate limiting per endpoint and user
- Input validation and sanitization
- Webhook signature verification
- Role-based access control

## 🌍 Localization

The system supports both Swahili and English:
- All user-facing messages are translated
- Currency formatting in Tanzanian Shillings (TSh)
- Date formatting in local format
- Phone number validation for Tanzanian numbers

## 📈 Monitoring and Logging

- Comprehensive logging with Winston
- Error tracking and reporting
- Performance monitoring
- Business analytics and insights

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- WhatsApp: +255 XXX XXX XXX
- Documentation: https://docs.whatsapppos.co.tz

## 🙏 Acknowledgments

- Built for Tanzanian small businesses
- Optimized for mobile-first usage
- Designed for low-bandwidth environments
- Supports local payment methods

---

**Asante sana kwa kutumia mfumo wetu! / Thank you for using our system!** 🇹🇿
