/**
 * Test Twilio WhatsApp Integration
 * This script tests sending WhatsApp messages using the provided Twilio credentials
 */

require('dotenv').config();

const accountSid = process.env.TWILIO_ACCOUNT_SID || 'AC4c0a9c264cf0b3f38b5c9f58d71abd17';
const authToken = process.env.TWILIO_AUTH_TOKEN || 'your_actual_auth_token_here';
const client = require('twilio')(accountSid, authToken);

console.log('🧪 Testing Twilio WhatsApp Integration...');
console.log(`📱 Account SID: ${accountSid}`);
console.log(`🔑 Auth Token: ${authToken.substring(0, 8)}...`);

// Test 1: Send a simple text message
async function testSimpleMessage() {
  console.log('\n📤 Test 1: Sending simple WhatsApp message...');
  
  try {
    const message = await client.messages.create({
      from: 'whatsapp:+***********',
      body: '🎉 Karibu WhatsApp POS Tanzania!\n\nMfumo wako wa mauzo umesajiliwa kikamilifu. Tumia amri zifuatazo:\n\n📋 *msaada* - Kupata msaada\n🏪 *jiunge* - Kusajili biashara\n📊 *ripoti* - Kuona ripoti za mauzo\n\nAsante kwa kutumia huduma yetu!',
      to: 'whatsapp:+************'
    });
    
    console.log('✅ Message sent successfully!');
    console.log(`📨 Message SID: ${message.sid}`);
    console.log(`📱 Status: ${message.status}`);
    return true;
  } catch (error) {
    console.error('❌ Failed to send message:', error.message);
    return false;
  }
}

// Test 2: Send a message with content template (like your example)
async function testTemplateMessage() {
  console.log('\n📤 Test 2: Sending template message...');
  
  try {
    const message = await client.messages.create({
      from: 'whatsapp:+***********',
      contentSid: 'HXb5b62575e6e4ff6129ad7c8efe1f983e',
      contentVariables: '{"1":"12/1","2":"3pm"}',
      to: 'whatsapp:+************'
    });
    
    console.log('✅ Template message sent successfully!');
    console.log(`📨 Message SID: ${message.sid}`);
    console.log(`📱 Status: ${message.status}`);
    return true;
  } catch (error) {
    console.error('❌ Failed to send template message:', error.message);
    return false;
  }
}

// Test 3: Test WhatsApp POS business registration flow
async function testBusinessRegistrationFlow() {
  console.log('\n📤 Test 3: Testing business registration flow...');
  
  try {
    const welcomeMessage = await client.messages.create({
      from: 'whatsapp:+***********',
      body: `🏪 *KARIBU WHATSAPP POS TANZANIA*

Mfumo wa kisasa wa mauzo kwa biashara za Tanzania!

📋 *AMRI ZA MSINGI:*
• *jiunge* - Kusajili biashara mpya
• *msaada* - Kupata msaada
• *bidhaa* - Kuona bidhaa zako
• *mauzo* - Kurekodi mauzo
• *ripoti* - Kuona ripoti za mauzo

🎯 *FAIDA:*
✅ Usimamizi wa bidhaa
✅ Ufuatiliaji wa mauzo
✅ Ripoti za kila siku
✅ Usimamizi wa wateja
✅ Kumbusho za stock

Andika *jiunge* kuanza!`,
      to: 'whatsapp:+************'
    });
    
    console.log('✅ Business registration flow message sent!');
    console.log(`📨 Message SID: ${welcomeMessage.sid}`);
    return true;
  } catch (error) {
    console.error('❌ Failed to send business registration message:', error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('🚀 Starting WhatsApp POS Tanzania Integration Tests...\n');
  
  const results = {
    simpleMessage: await testSimpleMessage(),
    templateMessage: await testTemplateMessage(),
    businessFlow: await testBusinessRegistrationFlow()
  };
  
  console.log('\n📊 TEST RESULTS:');
  console.log('================');
  console.log(`Simple Message: ${results.simpleMessage ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Template Message: ${results.templateMessage ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Business Flow: ${results.businessFlow ? '✅ PASS' : '❌ FAIL'}`);
  
  const passCount = Object.values(results).filter(Boolean).length;
  console.log(`\n🎯 Overall: ${passCount}/3 tests passed`);
  
  if (passCount === 3) {
    console.log('🎉 All tests passed! WhatsApp integration is working perfectly!');
  } else {
    console.log('⚠️  Some tests failed. Check your Twilio credentials and phone number.');
  }
}

// Run the tests
runTests().catch(console.error);
