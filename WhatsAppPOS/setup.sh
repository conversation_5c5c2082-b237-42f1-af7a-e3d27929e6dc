#!/bin/bash

# WhatsApp POS Tanzania - Quick Setup Script
echo "🇹🇿 Setting up WhatsApp POS Tanzania..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js version: $(node --version)"
echo "✅ npm version: $(npm --version)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration before starting the server"
else
    echo "✅ .env file already exists"
fi

# Create logs directory
mkdir -p logs
echo "✅ Created logs directory"

# Create uploads directory
mkdir -p uploads
echo "✅ Created uploads directory"

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Edit .env file with your configuration:"
echo "   - MongoDB URI"
echo "   - Redis URL"
echo "   - <PERSON><PERSON><PERSON> credentials"
echo ""
echo "2. Start the services:"
echo "   - MongoDB: mongod"
echo "   - Redis: redis-server"
echo ""
echo "3. Start the application:"
echo "   npm run dev    (development)"
echo "   npm start      (production)"
echo ""
echo "4. Test the system:"
echo "   npm run test:system"
echo ""
echo "5. Access the dashboard:"
echo "   http://localhost:3000"
echo ""
echo "📱 For WhatsApp integration:"
echo "   - Set up Twilio WhatsApp sandbox"
echo "   - Configure webhook URL in Twilio console"
echo "   - Test with: 'msaada' message"
echo ""
echo "🐳 Alternative: Use Docker Compose:"
echo "   docker-compose up -d"
echo ""
echo "Happy coding! 🚀"
