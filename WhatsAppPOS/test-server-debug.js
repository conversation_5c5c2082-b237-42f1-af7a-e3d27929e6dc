require('dotenv').config();

const { connectDB } = require('./backend/config/database');
const logger = require('./backend/config/logger');

// Start server function
async function startServer() {
  try {
    logger.info('Starting server initialization...');

    // Connect to MongoDB
    logger.info('Connecting to MongoDB...');
    await connectDB();
    logger.info('MongoDB connected successfully');

    // Connect to Redis
    logger.info('Connecting to Redis...');
    const { connectRedis } = require('./backend/config/redis');
    await connectRedis();
    logger.info('Redis connected successfully');

    // Load the debug app
    logger.info('Loading debug app...');
    const app = require('./backend/app-debug');
    logger.info('Debug app loaded successfully');

    // Start HTTP server
    const PORT = process.env.PORT || 3000;
    const server = app.listen(PORT, () => {
      logger.info(`🚀 WhatsApp POS Tanzania server running on port ${PORT}`);
      logger.info(`📱 Environment: ${process.env.NODE_ENV}`);
      logger.info(`🌍 Timezone: ${process.env.DEFAULT_TIMEZONE || 'Africa/Dar_es_Salaam'}`);
      logger.info(`💰 Currency: ${process.env.DEFAULT_CURRENCY || 'TZS'}`);
      logger.info(`🗣️  Default Language: ${process.env.DEFAULT_LANGUAGE || 'sw'}`);

      if (process.env.NODE_ENV === 'development') {
        logger.info(`📊 Dashboard: ${process.env.FRONTEND_URL || 'http://localhost:3001'}`);
        logger.info(`🔗 Webhook URL: ${process.env.WEBHOOK_URL || 'Not configured'}`);
      }
    });

    return server;

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
startServer().catch(error => {
  logger.error('Unhandled error during server startup:', error);
  process.exit(1);
});
