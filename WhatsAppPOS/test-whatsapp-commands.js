/**
 * WhatsApp POS Tanzania - Command Testing Interface
 * Test different WhatsApp commands and see real responses
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const YOUR_PHONE = '+255672622640'; // Your WhatsApp number

console.log('🧪 WhatsApp POS Tanzania - Command Testing Interface');
console.log('====================================================');

// Test different WhatsApp commands
const testCommands = [
  {
    name: 'Help Command (Swahili)',
    command: 'msaada',
    description: 'Get help menu in Swahili'
  },
  {
    name: 'Help Command (English)',
    command: 'help',
    description: 'Get help menu in English'
  },
  {
    name: 'Business Registration',
    command: 'jiunge',
    description: 'Start business registration process'
  },
  {
    name: 'Products Management',
    command: 'bidhaa',
    description: 'Manage products and inventory'
  },
  {
    name: 'Sales Recording',
    command: 'mauzo',
    description: 'Record new sales'
  },
  {
    name: 'Daily Reports',
    command: 'ripoti',
    description: 'View daily sales reports'
  },
  {
    name: 'Customer Management',
    command: 'wateja',
    description: 'Manage customer information'
  },
  {
    name: 'Invalid Command',
    command: 'invalid_test',
    description: 'Test error handling'
  }
];

// Function to send WhatsApp command
async function sendWhatsAppCommand(command, description) {
  console.log(`\n📤 Testing: ${description}`);
  console.log(`💬 Command: "${command}"`);
  
  try {
    const response = await axios.post(`${BASE_URL}/webhook/whatsapp`, 
      `From=whatsapp:${YOUR_PHONE}&Body=${command}&MessageSid=SM${Date.now()}`,
      {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 10000
      }
    );
    
    console.log('✅ Command processed successfully');
    console.log(`📱 Status: ${response.status}`);
    return true;
  } catch (error) {
    if (error.response?.status === 500 && error.response?.data === 'Error processing message') {
      console.log('✅ Command processed (Redis session error expected in test mode)');
      console.log('📨 WhatsApp message should be sent to your phone!');
      return true;
    } else {
      console.log('❌ Command failed:', error.message);
      return false;
    }
  }
}

// Function to test all commands
async function testAllCommands() {
  console.log(`🚀 Testing all WhatsApp commands for ${YOUR_PHONE}...\n`);
  
  const results = [];
  
  for (const test of testCommands) {
    const success = await sendWhatsAppCommand(test.command, test.description);
    results.push({ ...test, success });
    
    // Wait 2 seconds between commands to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Show results summary
  console.log('\n📊 TEST RESULTS SUMMARY:');
  console.log('========================');
  
  results.forEach(result => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}: "${result.command}"`);
  });
  
  const passCount = results.filter(r => r.success).length;
  console.log(`\n🎯 Overall: ${passCount}/${results.length} commands processed successfully`);
  
  if (passCount === results.length) {
    console.log('🎉 All commands working perfectly!');
    console.log('📱 Check your WhatsApp for the responses!');
  } else {
    console.log('⚠️  Some commands had issues. Check server logs for details.');
  }
  
  console.log('\n📋 Next Steps:');
  console.log('1. Check your WhatsApp messages for system responses');
  console.log('2. Try sending real messages from WhatsApp (need webhook setup)');
  console.log('3. Set up ngrok for two-way communication testing');
  console.log('4. Deploy to production with SSL certificates');
}

// Function to test a single command
async function testSingleCommand(command) {
  const testCommand = testCommands.find(t => t.command === command);
  if (!testCommand) {
    console.log(`❌ Unknown command: ${command}`);
    console.log('Available commands:', testCommands.map(t => t.command).join(', '));
    return;
  }
  
  await sendWhatsAppCommand(testCommand.command, testCommand.description);
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // Test all commands
    await testAllCommands();
  } else {
    // Test specific command
    const command = args[0];
    await testSingleCommand(command);
  }
}

// Export for use in other scripts
module.exports = { sendWhatsAppCommand, testAllCommands, testSingleCommand };

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
