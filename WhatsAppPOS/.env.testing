# WhatsApp POS Tanzania - Complete Testing Environment Configuration
# Copy this to .env for testing setup

# Application Environment
NODE_ENV=development
PORT=3000

# Database Configuration (Local Testing)
MONGODB_URI=mongodb://localhost:27017/tanzanian_pos_test
MONGODB_TEST_URI=mongodb://localhost:27017/tanzanian_pos_test

# Redis Configuration (Local Testing)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT Configuration (Testing - Use secure values in production)
JWT_SECRET=whatsapp-pos-tanzania-jwt-secret-key-for-testing-2024-very-secure
JWT_REFRESH_SECRET=whatsapp-pos-tanzania-refresh-secret-key-for-testing-2024-very-secure
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Twilio WhatsApp Configuration (Sandbox for Testing)
# Your actual Twilio credentials
TWILIO_ACCOUNT_SID=AC4c0a9c264cf0b3f38b5c9f58d71abd17
TWILIO_AUTH_TOKEN=[AuthToken]
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
WEBHOOK_URL=https://your-ngrok-url.ngrok.io/webhook/whatsapp

# Application URLs
FRONTEND_URL=http://localhost:3000
DASHBOARD_URL=http://localhost:3000

# Localization Settings
DEFAULT_LANGUAGE=sw
DEFAULT_TIMEZONE=Africa/Dar_es_Salaam
DEFAULT_CURRENCY=TZS

# Security Settings (Testing)
BCRYPT_ROUNDS=10
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Business Settings
TRIAL_DURATION_DAYS=14
MAX_BUSINESSES_PER_IP=10

# File Upload Settings
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=debug

# Development/Testing Flags
DEBUG=whatsapp-pos:*
ENABLE_SWAGGER=true
SKIP_WEBHOOK_VERIFICATION=true

# Test Data Settings
ENABLE_TEST_ENDPOINTS=true
AUTO_SEED_DATABASE=false
