/**
 * AI Service Test Suite
 * Tests for OpenRouter integration and natural language understanding
 */

const aiService = require('../services/aiService');
const { expect } = require('chai');

describe('AI Service Tests', function() {
  this.timeout(10000); // AI calls can take time

  describe('Service Initialization', function() {
    it('should initialize correctly', function() {
      const health = aiService.getHealthStatus();
      expect(health).to.have.property('enabled');
      expect(health).to.have.property('models');
    });
  });

  describe('Message Enhancement', function() {
    const testCases = [
      {
        input: 'ad prodct',
        expected: 'add_product',
        description: 'should correct typos in commands'
      },
      {
        input: 'sel somthing',
        expected: 'record_sale',
        description: 'should understand misspelled sale commands'
      },
      {
        input: 'nataka kuuza',
        expected: 'record_sale',
        description: 'should understand Swahili intent'
      },
      {
        input: 'I want to sell something',
        expected: 'record_sale',
        description: 'should understand natural language intent'
      },
      {
        input: 'ongeza bidhaa mpya',
        expected: 'add_product',
        description: 'should understand Swahili product commands'
      }
    ];

    testCases.forEach(testCase => {
      it(testCase.description, async function() {
        const context = { currentStep: 'welcome', language: 'sw' };
        const result = await aiService.enhanceMessageProcessing(testCase.input, context);
        
        if (result.enhanced) {
          expect(result.intent).to.equal(testCase.expected);
          expect(result.confidence).to.be.above(0.5);
        }
        // Note: Test passes even if AI is disabled (graceful degradation)
      });
    });
  });

  describe('Product Search', function() {
    const mockProducts = [
      { name: 'Coca Cola', price: 2000, stock: 50 },
      { name: 'Pepsi', price: 1800, stock: 30 },
      { name: 'Fanta Orange', price: 1900, stock: 25 },
      { name: 'Sprite', price: 1900, stock: 20 }
    ];

    const searchTests = [
      {
        query: 'coka',
        expected: 'Coca Cola',
        description: 'should find products with typos'
      },
      {
        query: 'orange soda',
        expected: 'Fanta Orange',
        description: 'should understand descriptive searches'
      },
      {
        query: 'pepsi',
        expected: 'Pepsi',
        description: 'should find exact matches'
      }
    ];

    searchTests.forEach(test => {
      it(test.description, async function() {
        const result = await aiService.smartProductSearch(test.query, mockProducts, 'en');
        
        if (result.found) {
          expect(result.product.name).to.equal(test.expected);
        } else if (result.suggestions.length > 0) {
          const suggestionNames = result.suggestions.map(p => p.name);
          expect(suggestionNames).to.include(test.expected);
        }
        // Test passes with fallback search if AI is disabled
      });
    });
  });

  describe('Quantity and Price Extraction', function() {
    const extractionTests = [
      {
        input: 'I want 5 bottles',
        expectedQuantity: 5,
        description: 'should extract quantity from English'
      },
      {
        input: 'nataka miwili',
        expectedQuantity: 2,
        description: 'should extract Swahili numbers'
      },
      {
        input: 'give me three for 6000 shillings',
        expectedQuantity: 3,
        expectedPrice: 6000,
        description: 'should extract both quantity and price'
      },
      {
        input: 'kilo moja',
        expectedQuantity: 1,
        description: 'should understand units'
      }
    ];

    extractionTests.forEach(test => {
      it(test.description, async function() {
        const result = await aiService.extractQuantityAndPrice(test.input, 'sw');
        
        if (test.expectedQuantity) {
          expect(result.quantity).to.equal(test.expectedQuantity);
        }
        
        if (test.expectedPrice) {
          expect(result.price).to.equal(test.expectedPrice);
        }
      });
    });
  });

  describe('Error Handling', function() {
    it('should handle API failures gracefully', async function() {
      // Temporarily disable AI to test fallback
      const originalEnabled = aiService.enabled;
      aiService.enabled = false;
      
      const result = await aiService.enhanceMessageProcessing('test message', {});
      expect(result.enhanced).to.be.false;
      expect(result.originalMessage).to.equal('test message');
      
      // Restore original state
      aiService.enabled = originalEnabled;
    });

    it('should handle malformed responses', async function() {
      // This test would require mocking the OpenRouter API
      // For now, we test that the service doesn't crash
      const result = await aiService.enhanceMessageProcessing('', {});
      expect(result).to.have.property('enhanced');
    });
  });

  describe('Caching', function() {
    it('should cache frequent queries', async function() {
      const message = 'test cache message';
      const context = { currentStep: 'welcome', language: 'sw' };
      
      // First call
      const result1 = await aiService.enhanceMessageProcessing(message, context);
      
      // Second call should be faster (cached)
      const start = Date.now();
      const result2 = await aiService.enhanceMessageProcessing(message, context);
      const duration = Date.now() - start;
      
      expect(duration).to.be.below(100); // Should be very fast if cached
      expect(result2.enhanced).to.equal(result1.enhanced);
    });
  });

  describe('Performance', function() {
    it('should respond within acceptable time limits', async function() {
      const start = Date.now();
      await aiService.enhanceMessageProcessing('quick test', { language: 'en' });
      const duration = Date.now() - start;
      
      // Should respond within 5 seconds (including network time)
      expect(duration).to.be.below(5000);
    });
  });
});

// Integration tests with actual WhatsApp service
describe('AI Integration Tests', function() {
  this.timeout(15000);

  describe('End-to-End Message Processing', function() {
    const integrationTests = [
      {
        message: 'I want to add a new product called Coca Cola for 2000 shillings',
        expectedFlow: 'add_product',
        description: 'should handle complex natural language requests'
      },
      {
        message: 'nataka kuona ripoti ya leo',
        expectedFlow: 'daily_report',
        description: 'should handle Swahili business requests'
      },
      {
        message: 'can you help me sell 5 bottles of water',
        expectedFlow: 'record_sale',
        description: 'should extract product and quantity from natural language'
      }
    ];

    integrationTests.forEach(test => {
      it(test.description, async function() {
        const context = {
          currentStep: 'welcome',
          language: 'sw',
          businessContext: {}
        };
        
        const result = await aiService.enhanceMessageProcessing(test.message, context);
        
        if (result.enhanced) {
          expect(result.intent).to.include(test.expectedFlow);
        }
        
        // Test should pass even if AI is disabled
        expect(result).to.have.property('originalMessage');
      });
    });
  });
});

// Mock data for testing
const mockBusinessContext = {
  products: [
    { id: 1, name: 'Coca Cola', price: 2000, stock: 50 },
    { id: 2, name: 'Bread', price: 1500, stock: 20 },
    { id: 3, name: 'Milk', price: 3000, stock: 15 }
  ],
  customers: [
    { id: 1, name: 'John Doe', phone: '+255123456789' },
    { id: 2, name: 'Jane Smith', phone: '+255987654321' }
  ]
};

// Helper functions for testing
function createTestContext(step = 'welcome', language = 'sw', data = {}) {
  return {
    currentStep: step,
    language: language,
    businessContext: { ...mockBusinessContext, ...data }
  };
}

module.exports = {
  mockBusinessContext,
  createTestContext
};
