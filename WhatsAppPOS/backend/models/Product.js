/**
 * Product Model - Handles inventory management for businesses
 * Supports both physical products and services with stock tracking
 */

const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  business_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Business',
    required: [true, 'Business ID is required'],
    index: true
  },
  
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [100, 'Product name cannot exceed 100 characters'],
    index: true
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  
  price: {
    type: Number,
    required: [true, 'Product price is required'],
    min: [0, 'Price cannot be negative'],
    validate: {
      validator: function(v) {
        return v >= 0 && v <= 999999999; // Max 999 million TSh
      },
      message: 'Price must be between 0 and 999,999,999 TSh'
    }
  },
  
  cost: {
    type: Number,
    min: [0, 'Cost cannot be negative'],
    default: 0,
    validate: {
      validator: function(v) {
        return v >= 0 && v <= 999999999;
      },
      message: 'Cost must be between 0 and 999,999,999 TSh'
    }
  },
  
  stock: {
    type: Number,
    required: function() {
      return !this.is_service;
    },
    min: [0, 'Stock cannot be negative'],
    default: 0,
    validate: {
      validator: function(v) {
        // Services don't need stock validation
        if (this.is_service) return true;
        return Number.isInteger(v) && v >= 0;
      },
      message: 'Stock must be a non-negative integer'
    }
  },
  
  category: {
    type: String,
    required: [true, 'Product category is required'],
    trim: true,
    enum: {
      values: [
        'food', 'drinks', 'electronics', 'clothing', 'beauty', 'health',
        'household', 'stationery', 'tools', 'automotive', 'books',
        'toys', 'sports', 'services', 'other'
      ],
      message: 'Invalid product category'
    },
    index: true
  },
  
  is_service: {
    type: Boolean,
    default: false,
    index: true
  },
  
  sku: {
    type: String,
    trim: true,
    maxlength: [50, 'SKU cannot exceed 50 characters'],
    index: true,
    sparse: true // Allow multiple null values
  },
  
  barcode: {
    type: String,
    trim: true,
    maxlength: [50, 'Barcode cannot exceed 50 characters'],
    index: true,
    sparse: true
  },
  
  low_stock_alert: {
    type: Number,
    min: [0, 'Low stock alert cannot be negative'],
    default: function() {
      // Get business default or use 5
      return 5;
    }
  },
  
  pricing: {
    wholesale_price: {
      type: Number,
      min: [0, 'Wholesale price cannot be negative']
    },
    bulk_discount: {
      min_quantity: {
        type: Number,
        min: [1, 'Minimum quantity must be at least 1']
      },
      discount_percentage: {
        type: Number,
        min: [0, 'Discount percentage cannot be negative'],
        max: [100, 'Discount percentage cannot exceed 100%']
      }
    }
  },
  
  supplier: {
    name: {
      type: String,
      trim: true,
      maxlength: [100, 'Supplier name too long']
    },
    phone: {
      type: String,
      trim: true,
      validate: {
        validator: function(v) {
          if (!v) return true; // Optional field
          return /^(\+255|255|0)?[67]\d{8}$/.test(v.replace(/\s/g, ''));
        },
        message: 'Please enter a valid Tanzanian phone number'
      }
    },
    last_order_date: {
      type: Date
    }
  },
  
  images: [{
    url: {
      type: String,
      trim: true
    },
    alt_text: {
      type: String,
      trim: true,
      maxlength: [100, 'Alt text too long']
    },
    is_primary: {
      type: Boolean,
      default: false
    }
  }],
  
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag too long']
  }],
  
  sales_data: {
    total_sold: {
      type: Number,
      default: 0,
      min: [0, 'Total sold cannot be negative']
    },
    last_sold_date: {
      type: Date
    },
    revenue_generated: {
      type: Number,
      default: 0,
      min: [0, 'Revenue cannot be negative']
    }
  },
  
  is_active: {
    type: Boolean,
    default: true,
    index: true
  },
  
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Creator is required']
  },
  
  last_updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for performance
productSchema.index({ business_id: 1, is_active: 1 });
productSchema.index({ business_id: 1, category: 1 });
productSchema.index({ business_id: 1, name: 'text', description: 'text' });
productSchema.index({ business_id: 1, stock: 1, low_stock_alert: 1 });
productSchema.index({ business_id: 1, 'sales_data.total_sold': -1 });

// Ensure SKU is unique within business
productSchema.index({ business_id: 1, sku: 1 }, { 
  unique: true, 
  sparse: true,
  partialFilterExpression: { sku: { $exists: true, $ne: null } }
});

// Virtual for profit margin
productSchema.virtual('profit_margin').get(function() {
  if (this.cost && this.cost > 0) {
    return ((this.price - this.cost) / this.cost * 100).toFixed(2);
  }
  return 0;
});

// Virtual for stock status
productSchema.virtual('stock_status').get(function() {
  if (this.is_service) return 'service';
  if (this.stock === 0) return 'out_of_stock';
  if (this.stock <= this.low_stock_alert) return 'low_stock';
  return 'in_stock';
});

// Virtual for formatted price
productSchema.virtual('formatted_price').get(function() {
  return `TSh ${this.price.toLocaleString('en-US')}`;
});

// Virtual for primary image
productSchema.virtual('primary_image').get(function() {
  const primaryImg = this.images.find(img => img.is_primary);
  return primaryImg ? primaryImg.url : (this.images.length > 0 ? this.images[0].url : null);
});

// Pre-save middleware
productSchema.pre('save', function(next) {
  // Auto-generate SKU if not provided
  if (!this.sku && this.isNew) {
    const prefix = this.category.substring(0, 3).toUpperCase();
    const timestamp = Date.now().toString().slice(-6);
    this.sku = `${prefix}${timestamp}`;
  }
  
  // Ensure only one primary image
  if (this.images && this.images.length > 0) {
    let primaryCount = 0;
    this.images.forEach((img, index) => {
      if (img.is_primary) {
        primaryCount++;
        if (primaryCount > 1) {
          img.is_primary = false;
        }
      }
    });
    
    // If no primary image, make the first one primary
    if (primaryCount === 0) {
      this.images[0].is_primary = true;
    }
  }
  
  // Update last_updated_by if modified
  if (this.isModified() && !this.isNew) {
    this.last_updated_by = this.last_updated_by || this.created_by;
  }
  
  next();
});

// Instance methods
productSchema.methods.updateStock = function(quantity, operation = 'subtract') {
  if (this.is_service) {
    throw new Error('Cannot update stock for services');
  }
  
  if (operation === 'subtract') {
    if (this.stock < quantity) {
      throw new Error('Insufficient stock');
    }
    this.stock -= quantity;
  } else if (operation === 'add') {
    this.stock += quantity;
  } else {
    throw new Error('Invalid operation. Use "add" or "subtract"');
  }
  
  return this.save();
};

productSchema.methods.recordSale = function(quantity, salePrice = null) {
  this.sales_data.total_sold += quantity;
  this.sales_data.last_sold_date = new Date();
  this.sales_data.revenue_generated += (salePrice || this.price) * quantity;
  
  // Update stock for physical products
  if (!this.is_service) {
    this.updateStock(quantity, 'subtract');
  }
  
  return this.save();
};

productSchema.methods.isLowStock = function() {
  if (this.is_service) return false;
  return this.stock <= this.low_stock_alert;
};

productSchema.methods.isOutOfStock = function() {
  if (this.is_service) return false;
  return this.stock === 0;
};

// Static methods
productSchema.statics.findByBusiness = function(businessId, options = {}) {
  const query = { business_id: businessId };
  
  if (options.activeOnly !== false) {
    query.is_active = true;
  }
  
  if (options.category) {
    query.category = options.category;
  }
  
  if (options.inStock) {
    query.$or = [
      { is_service: true },
      { stock: { $gt: 0 } }
    ];
  }
  
  return this.find(query);
};

productSchema.statics.searchProducts = function(businessId, searchTerm) {
  return this.find({
    business_id: businessId,
    is_active: true,
    $or: [
      { name: { $regex: searchTerm, $options: 'i' } },
      { description: { $regex: searchTerm, $options: 'i' } },
      { sku: { $regex: searchTerm, $options: 'i' } },
      { tags: { $in: [new RegExp(searchTerm, 'i')] } }
    ]
  });
};

productSchema.statics.getLowStockProducts = function(businessId) {
  return this.find({
    business_id: businessId,
    is_active: true,
    is_service: false,
    $expr: { $lte: ['$stock', '$low_stock_alert'] }
  });
};

productSchema.statics.getTopSellingProducts = function(businessId, limit = 10) {
  return this.find({
    business_id: businessId,
    is_active: true
  })
  .sort({ 'sales_data.total_sold': -1 })
  .limit(limit);
};

module.exports = mongoose.model('Product', productSchema);
