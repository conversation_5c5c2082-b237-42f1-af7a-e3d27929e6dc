/**
 * Sale Model - Handles sales transactions and receipts
 * Comprehensive sales tracking with payment methods and customer data
 */

const mongoose = require('mongoose');

const saleSchema = new mongoose.Schema({
  business_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Business',
    required: [true, 'Business ID is required'],
    index: true
  },
  
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    index: true
  },
  
  customer_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer',
    index: true
  },
  
  sale_number: {
    type: String,
    required: [true, 'Sale number is required'],
    unique: true,
    index: true
  },
  
  items: [{
    product_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: [true, 'Product ID is required']
    },
    product_name: {
      type: String,
      required: [true, 'Product name is required'],
      trim: true
    },
    product_sku: {
      type: String,
      trim: true
    },
    quantity: {
      type: Number,
      required: [true, 'Quantity is required'],
      min: [0.01, 'Quantity must be greater than 0'],
      validate: {
        validator: function(v) {
          return v > 0 && v <= 10000; // Max 10,000 items per line
        },
        message: 'Quantity must be between 0.01 and 10,000'
      }
    },
    unit_price: {
      type: Number,
      required: [true, 'Unit price is required'],
      min: [0, 'Unit price cannot be negative'],
      validate: {
        validator: function(v) {
          return v >= 0 && v <= 999999999;
        },
        message: 'Unit price must be between 0 and 999,999,999 TSh'
      }
    },
    total_price: {
      type: Number,
      required: [true, 'Total price is required'],
      min: [0, 'Total price cannot be negative']
    },
    discount_amount: {
      type: Number,
      default: 0,
      min: [0, 'Discount cannot be negative']
    },
    is_service: {
      type: Boolean,
      default: false
    }
  }],
  
  subtotal: {
    type: Number,
    required: [true, 'Subtotal is required'],
    min: [0, 'Subtotal cannot be negative']
  },
  
  discount: {
    amount: {
      type: Number,
      default: 0,
      min: [0, 'Discount amount cannot be negative']
    },
    percentage: {
      type: Number,
      default: 0,
      min: [0, 'Discount percentage cannot be negative'],
      max: [100, 'Discount percentage cannot exceed 100%']
    },
    reason: {
      type: String,
      trim: true,
      maxlength: [100, 'Discount reason too long']
    }
  },
  
  tax: {
    amount: {
      type: Number,
      default: 0,
      min: [0, 'Tax amount cannot be negative']
    },
    percentage: {
      type: Number,
      default: 0,
      min: [0, 'Tax percentage cannot be negative'],
      max: [100, 'Tax percentage cannot exceed 100%']
    }
  },
  
  total: {
    type: Number,
    required: [true, 'Total is required'],
    min: [0, 'Total cannot be negative']
  },
  
  payment: {
    method: {
      type: String,
      required: [true, 'Payment method is required'],
      enum: {
        values: ['cash', 'mpesa', 'tigo', 'airtel', 'bank', 'credit', 'mixed'],
        message: 'Invalid payment method'
      },
      index: true
    },
    reference: {
      type: String,
      trim: true,
      maxlength: [50, 'Payment reference too long']
    },
    amount_paid: {
      type: Number,
      required: [true, 'Amount paid is required'],
      min: [0, 'Amount paid cannot be negative']
    },
    change_given: {
      type: Number,
      default: 0,
      min: [0, 'Change cannot be negative']
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'refunded'],
      default: 'completed',
      index: true
    }
  },
  
  customer_info: {
    name: {
      type: String,
      trim: true,
      maxlength: [100, 'Customer name too long']
    },
    phone: {
      type: String,
      trim: true,
      validate: {
        validator: function(v) {
          if (!v) return true; // Optional field
          return /^(\+255|255|0)?[67]\d{8}$/.test(v.replace(/\s/g, ''));
        },
        message: 'Please enter a valid Tanzanian phone number'
      }
    }
  },
  
  notes: {
    type: String,
    trim: true,
    maxlength: [500, 'Notes cannot exceed 500 characters']
  },
  
  receipt: {
    number: {
      type: String,
      unique: true,
      sparse: true
    },
    printed: {
      type: Boolean,
      default: false
    },
    sent_via_whatsapp: {
      type: Boolean,
      default: false
    },
    sent_at: {
      type: Date
    }
  },
  
  sale_date: {
    type: Date,
    required: [true, 'Sale date is required'],
    default: Date.now,
    index: true
  },
  
  location: {
    type: String,
    enum: ['store', 'delivery', 'pickup'],
    default: 'store'
  },
  
  status: {
    type: String,
    enum: ['completed', 'pending', 'cancelled', 'refunded'],
    default: 'completed',
    index: true
  },
  
  refund_info: {
    refunded_amount: {
      type: Number,
      default: 0,
      min: [0, 'Refund amount cannot be negative']
    },
    refund_date: {
      type: Date
    },
    refund_reason: {
      type: String,
      trim: true,
      maxlength: [200, 'Refund reason too long']
    },
    refunded_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
saleSchema.index({ business_id: 1, sale_date: -1 });
saleSchema.index({ business_id: 1, user_id: 1, sale_date: -1 });
saleSchema.index({ business_id: 1, 'payment.method': 1, sale_date: -1 });
saleSchema.index({ business_id: 1, status: 1 });
saleSchema.index({ sale_number: 1 }, { unique: true });
saleSchema.index({ 'receipt.number': 1 }, { unique: true, sparse: true });

// Virtual for total items count
saleSchema.virtual('total_items').get(function() {
  return this.items.reduce((total, item) => total + item.quantity, 0);
});

// Virtual for profit calculation
saleSchema.virtual('profit').get(function() {
  // This would need product cost data to calculate accurately
  // For now, return null - can be calculated in business logic
  return null;
});

// Virtual for formatted total
saleSchema.virtual('formatted_total').get(function() {
  return `TSh ${this.total.toLocaleString('en-US')}`;
});

// Virtual for sale summary
saleSchema.virtual('summary').get(function() {
  const itemCount = this.items.length;
  const totalQty = this.total_items;
  return `${itemCount} item${itemCount !== 1 ? 's' : ''} (${totalQty} qty) - ${this.formatted_total}`;
});

// Pre-save middleware
saleSchema.pre('save', function(next) {
  // Auto-generate sale number if not provided
  if (!this.sale_number && this.isNew) {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = Date.now().toString().slice(-6);
    this.sale_number = `SALE-${dateStr}-${timeStr}`;
  }
  
  // Auto-generate receipt number if not provided
  if (!this.receipt.number && this.isNew) {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = Date.now().toString().slice(-4);
    this.receipt.number = `RCP-${dateStr}-${timeStr}`;
  }
  
  // Calculate totals
  this.calculateTotals();
  
  // Validate payment
  if (this.payment.amount_paid < this.total && this.payment.method !== 'credit') {
    return next(new Error('Amount paid is less than total'));
  }
  
  // Calculate change
  if (this.payment.amount_paid > this.total) {
    this.payment.change_given = this.payment.amount_paid - this.total;
  }
  
  next();
});

// Instance methods
saleSchema.methods.calculateTotals = function() {
  // Calculate subtotal from items
  this.subtotal = this.items.reduce((total, item) => {
    item.total_price = item.quantity * item.unit_price - (item.discount_amount || 0);
    return total + item.total_price;
  }, 0);
  
  // Apply discount
  let discountAmount = this.discount.amount || 0;
  if (this.discount.percentage > 0) {
    discountAmount = (this.subtotal * this.discount.percentage) / 100;
    this.discount.amount = discountAmount;
  }
  
  // Calculate tax
  let taxAmount = this.tax.amount || 0;
  if (this.tax.percentage > 0) {
    taxAmount = ((this.subtotal - discountAmount) * this.tax.percentage) / 100;
    this.tax.amount = taxAmount;
  }
  
  // Calculate final total
  this.total = this.subtotal - discountAmount + taxAmount;
  
  return this.total;
};

saleSchema.methods.addItem = function(productId, productName, quantity, unitPrice, productSku = null) {
  const item = {
    product_id: productId,
    product_name: productName,
    product_sku: productSku,
    quantity: quantity,
    unit_price: unitPrice,
    total_price: quantity * unitPrice
  };
  
  this.items.push(item);
  this.calculateTotals();
  
  return this;
};

saleSchema.methods.removeItem = function(itemIndex) {
  if (itemIndex >= 0 && itemIndex < this.items.length) {
    this.items.splice(itemIndex, 1);
    this.calculateTotals();
  }
  return this;
};

saleSchema.methods.applyDiscount = function(amount, percentage = 0, reason = '') {
  this.discount.amount = amount;
  this.discount.percentage = percentage;
  this.discount.reason = reason;
  this.calculateTotals();
  return this;
};

saleSchema.methods.generateReceipt = function() {
  const receipt = {
    number: this.receipt.number,
    date: this.sale_date,
    items: this.items.map(item => ({
      name: item.product_name,
      quantity: item.quantity,
      price: item.unit_price,
      total: item.total_price
    })),
    subtotal: this.subtotal,
    discount: this.discount.amount,
    tax: this.tax.amount,
    total: this.total,
    payment_method: this.payment.method,
    amount_paid: this.payment.amount_paid,
    change: this.payment.change_given
  };
  
  return receipt;
};

// Static methods
saleSchema.statics.findByBusiness = function(businessId, options = {}) {
  const query = { business_id: businessId };
  
  if (options.startDate && options.endDate) {
    query.sale_date = {
      $gte: new Date(options.startDate),
      $lte: new Date(options.endDate)
    };
  }
  
  if (options.paymentMethod) {
    query['payment.method'] = options.paymentMethod;
  }
  
  if (options.status) {
    query.status = options.status;
  }
  
  return this.find(query).sort({ sale_date: -1 });
};

saleSchema.statics.getDailySales = function(businessId, date = new Date()) {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  
  return this.find({
    business_id: businessId,
    sale_date: { $gte: startOfDay, $lte: endOfDay },
    status: 'completed'
  });
};

saleSchema.statics.getSalesAnalytics = function(businessId, startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        business_id: mongoose.Types.ObjectId(businessId),
        sale_date: { $gte: new Date(startDate), $lte: new Date(endDate) },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: null,
        total_sales: { $sum: 1 },
        total_revenue: { $sum: '$total' },
        total_items_sold: { $sum: '$total_items' },
        average_sale_value: { $avg: '$total' },
        payment_methods: {
          $push: '$payment.method'
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Sale', saleSchema);
