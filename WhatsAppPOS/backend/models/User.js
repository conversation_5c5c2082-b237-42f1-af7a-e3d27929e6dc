/**
 * User Model - Handles business users with role-based permissions
 * Supports multi-user businesses with granular access control
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  business_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Business',
    required: [true, 'Business ID is required'],
    index: true
  },

  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    unique: true,
    trim: true,
    validate: {
      validator: function(v) {
        // Validate Tanzanian phone numbers
        return /^(\+255|255|0)?[67]\d{8}$/.test(v.replace(/\s/g, ''));
      },
      message: 'Please enter a valid Tanzanian phone number'
    },
    index: true
  },

  name: {
    type: String,
    required: [true, 'User name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },

  role: {
    type: String,
    enum: {
      values: ['owner', 'manager', 'employee', 'viewer'],
      message: 'Invalid user role'
    },
    required: [true, 'User role is required'],
    index: true
  },

  permissions: {
    // Product management
    can_add_products: {
      type: Boolean,
      default: function() {
        return ['owner', 'manager'].includes(this.role);
      }
    },
    can_edit_products: {
      type: Boolean,
      default: function() {
        return ['owner', 'manager'].includes(this.role);
      }
    },
    can_delete_products: {
      type: Boolean,
      default: function() {
        return this.role === 'owner';
      }
    },

    // Sales management
    can_record_sales: {
      type: Boolean,
      default: function() {
        return ['owner', 'manager', 'employee'].includes(this.role);
      }
    },
    can_edit_sales: {
      type: Boolean,
      default: function() {
        return ['owner', 'manager'].includes(this.role);
      }
    },
    can_delete_sales: {
      type: Boolean,
      default: function() {
        return this.role === 'owner';
      }
    },

    // Customer management
    can_add_customers: {
      type: Boolean,
      default: function() {
        return ['owner', 'manager', 'employee'].includes(this.role);
      }
    },
    can_edit_customers: {
      type: Boolean,
      default: function() {
        return ['owner', 'manager'].includes(this.role);
      }
    },

    // Reports and analytics
    can_view_reports: {
      type: Boolean,
      default: function() {
        return ['owner', 'manager', 'viewer'].includes(this.role);
      }
    },
    can_export_data: {
      type: Boolean,
      default: function() {
        return ['owner', 'manager'].includes(this.role);
      }
    },

    // User management
    can_manage_users: {
      type: Boolean,
      default: function() {
        return this.role === 'owner';
      }
    },
    can_view_users: {
      type: Boolean,
      default: function() {
        return ['owner', 'manager'].includes(this.role);
      }
    },

    // Settings and configuration
    can_manage_settings: {
      type: Boolean,
      default: function() {
        return this.role === 'owner';
      }
    },
    can_view_settings: {
      type: Boolean,
      default: function() {
        return ['owner', 'manager'].includes(this.role);
      }
    }
  },

  preferences: {
    language: {
      type: String,
      enum: ['sw', 'en'],
      default: 'sw'
    },
    notifications: {
      whatsapp: {
        type: Boolean,
        default: true
      },
      daily_summary: {
        type: Boolean,
        default: true
      },
      low_stock_alerts: {
        type: Boolean,
        default: true
      }
    },
    dashboard_layout: {
      type: String,
      enum: ['compact', 'detailed'],
      default: 'detailed'
    }
  },

  authentication: {
    password_hash: {
      type: String,
      select: false // Don't include in queries by default
    },
    last_login: {
      type: Date
    },
    login_attempts: {
      type: Number,
      default: 0,
      select: false
    },
    locked_until: {
      type: Date,
      select: false
    },
    password_reset_token: {
      type: String,
      select: false
    },
    password_reset_expires: {
      type: Date,
      select: false
    }
  },

  activity: {
    total_sales_recorded: {
      type: Number,
      default: 0
    },
    total_products_added: {
      type: Number,
      default: 0
    },
    last_whatsapp_activity: {
      type: Date
    },
    last_dashboard_activity: {
      type: Date
    }
  },

  is_active: {
    type: Boolean,
    default: true,
    index: true
  },

  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },

  last_active: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    virtuals: true,
    transform: function(doc, ret) {
      // Remove sensitive fields from JSON output
      delete ret.authentication;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Compound indexes for performance
userSchema.index({ business_id: 1, role: 1 });
userSchema.index({ business_id: 1, is_active: 1 });
userSchema.index({ phone: 1 }, { unique: true });
userSchema.index({ last_active: -1 });

// Virtual for full permissions object
userSchema.virtual('all_permissions').get(function() {
  const basePermissions = this.permissions.toObject();

  // Add role-based permissions that can't be overridden
  if (this.role === 'owner') {
    basePermissions.is_owner = true;
    basePermissions.can_delete_business = true;
    basePermissions.can_manage_subscription = true;
  }

  return basePermissions;
});

// Virtual for account status
userSchema.virtual('account_status').get(function() {
  if (!this.is_active) return 'inactive';
  if (this.authentication.locked_until && this.authentication.locked_until > new Date()) {
    return 'locked';
  }
  return 'active';
});

// Pre-save middleware
userSchema.pre('save', async function(next) {
  // Format phone number
  if (this.isModified('phone')) {
    this.phone = this.phone.replace(/\s/g, '');
    if (this.phone.startsWith('0')) {
      this.phone = '+255' + this.phone.substring(1);
    } else if (!this.phone.startsWith('+')) {
      this.phone = '+' + this.phone;
    }
  }

  // Hash password if modified
  if (this.isModified('authentication.password_hash') && this.authentication.password_hash) {
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    this.authentication.password_hash = await bcrypt.hash(this.authentication.password_hash, saltRounds);
  }

  // Update permissions based on role
  if (this.isModified('role')) {
    this.setPermissionsByRole();
  }

  // Update last activity
  this.last_active = new Date();

  next();
});

// Instance methods
userSchema.methods.setPermissionsByRole = function() {
  const rolePermissions = {
    owner: {
      can_add_products: true,
      can_edit_products: true,
      can_delete_products: true,
      can_record_sales: true,
      can_edit_sales: true,
      can_delete_sales: true,
      can_add_customers: true,
      can_edit_customers: true,
      can_view_reports: true,
      can_export_data: true,
      can_manage_users: true,
      can_view_users: true,
      can_manage_settings: true,
      can_view_settings: true
    },
    manager: {
      can_add_products: true,
      can_edit_products: true,
      can_delete_products: false,
      can_record_sales: true,
      can_edit_sales: true,
      can_delete_sales: false,
      can_add_customers: true,
      can_edit_customers: true,
      can_view_reports: true,
      can_export_data: true,
      can_manage_users: false,
      can_view_users: true,
      can_manage_settings: false,
      can_view_settings: true
    },
    employee: {
      can_add_products: false,
      can_edit_products: false,
      can_delete_products: false,
      can_record_sales: true,
      can_edit_sales: false,
      can_delete_sales: false,
      can_add_customers: true,
      can_edit_customers: false,
      can_view_reports: false,
      can_export_data: false,
      can_manage_users: false,
      can_view_users: false,
      can_manage_settings: false,
      can_view_settings: false
    },
    viewer: {
      can_add_products: false,
      can_edit_products: false,
      can_delete_products: false,
      can_record_sales: false,
      can_edit_sales: false,
      can_delete_sales: false,
      can_add_customers: false,
      can_edit_customers: false,
      can_view_reports: true,
      can_export_data: false,
      can_manage_users: false,
      can_view_users: false,
      can_manage_settings: false,
      can_view_settings: false
    }
  };

  if (rolePermissions[this.role]) {
    Object.assign(this.permissions, rolePermissions[this.role]);
  }
};

userSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.authentication.password_hash) {
    return false;
  }
  return bcrypt.compare(candidatePassword, this.authentication.password_hash);
};

userSchema.methods.incrementLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.authentication.locked_until && this.authentication.locked_until < Date.now()) {
    return this.updateOne({
      $unset: { 'authentication.locked_until': 1 },
      $set: { 'authentication.login_attempts': 1 }
    });
  }

  const updates = { $inc: { 'authentication.login_attempts': 1 } };

  // If we have max attempts and no lock, lock the account
  if (this.authentication.login_attempts + 1 >= 5 && !this.authentication.locked_until) {
    updates.$set = { 'authentication.locked_until': Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }

  return this.updateOne(updates);
};

userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: {
      'authentication.login_attempts': 1,
      'authentication.locked_until': 1
    }
  });
};

userSchema.methods.hasPermission = function(permission) {
  return this.permissions[permission] === true;
};

// Static methods
userSchema.statics.findByPhone = function(phone) {
  const formattedPhone = phone.replace(/\s/g, '');
  // Escape special regex characters in phone number
  const escapedPhone = formattedPhone.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  return this.findOne({ phone: new RegExp(escapedPhone, 'i') });
};

userSchema.statics.findBusinessUsers = function(businessId, activeOnly = true) {
  const query = { business_id: businessId };
  if (activeOnly) {
    query.is_active = true;
  }
  return this.find(query).populate('business_id', 'name phone');
};

module.exports = mongoose.model('User', userSchema);
