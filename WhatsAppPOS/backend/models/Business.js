/**
 * Business Model - Core business entity for the POS system
 * Handles business registration, subscription management, and settings
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const businessSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Business name is required'],
    trim: true,
    maxlength: [100, 'Business name cannot exceed 100 characters'],
    index: true
  },

  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    unique: true,
    trim: true,
    validate: {
      validator: function(v) {
        // Validate Tanzanian phone numbers
        return /^(\+255|255|0)?[67]\d{8}$/.test(v.replace(/\s/g, ''));
      },
      message: 'Please enter a valid Tanzanian phone number'
    },
    index: true
  },

  owner_name: {
    type: String,
    required: [true, 'Owner name is required'],
    trim: true,
    maxlength: [100, 'Owner name cannot exceed 100 characters']
  },

  business_type: {
    type: String,
    required: [true, 'Business type is required'],
    enum: {
      values: [
        'duka', 'shop', 'salon', 'restaurant', 'pharmacy', 'electronics',
        'clothing', 'hardware', 'grocery', 'services', 'repair', 'other'
      ],
      message: 'Invalid business type'
    },
    index: true
  },

  location: {
    region: {
      type: String,
      trim: true,
      maxlength: [50, 'Region name too long']
    },
    district: {
      type: String,
      trim: true,
      maxlength: [50, 'District name too long']
    },
    ward: {
      type: String,
      trim: true,
      maxlength: [50, 'Ward name too long']
    },
    street: {
      type: String,
      trim: true,
      maxlength: [100, 'Street address too long']
    }
  },

  subscription: {
    plan: {
      type: String,
      enum: ['trial', 'basic', 'pro'],
      default: 'trial',
      index: true
    },
    status: {
      type: String,
      enum: ['active', 'expired', 'cancelled', 'suspended'],
      default: 'active',
      index: true
    },
    start_date: {
      type: Date,
      default: Date.now
    },
    end_date: {
      type: Date,
      default: function() {
        // Trial period: 14 days from now
        const trialDays = parseInt(process.env.TRIAL_DURATION_DAYS) || 14;
        return new Date(Date.now() + (trialDays * 24 * 60 * 60 * 1000));
      }
    },
    next_billing: {
      type: Date
    },
    payment_method: {
      type: String,
      enum: ['mpesa', 'tigo', 'airtel', 'bank', 'cash'],
      default: 'mpesa'
    }
  },

  settings: {
    language: {
      type: String,
      enum: ['sw', 'en'],
      default: 'sw'
    },
    currency_format: {
      type: String,
      default: 'TSh {amount}'
    },
    low_stock_threshold: {
      type: Number,
      default: 5,
      min: [0, 'Threshold cannot be negative']
    },
    notifications: {
      low_stock: {
        type: Boolean,
        default: true
      },
      daily_summary: {
        type: Boolean,
        default: true
      },
      payment_reminders: {
        type: Boolean,
        default: true
      }
    },
    business_hours: {
      open_time: {
        type: String,
        default: '08:00'
      },
      close_time: {
        type: String,
        default: '18:00'
      },
      working_days: [{
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
      }],
      default: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
    }
  },

  verification: {
    phone_verified: {
      type: Boolean,
      default: false
    },
    verification_code: {
      type: String,
      select: false // Don't include in queries by default
    },
    verification_expires: {
      type: Date,
      select: false
    },
    verification_attempts: {
      type: Number,
      default: 0,
      select: false
    }
  },

  stats: {
    total_products: {
      type: Number,
      default: 0
    },
    total_sales: {
      type: Number,
      default: 0
    },
    total_revenue: {
      type: Number,
      default: 0
    },
    total_customers: {
      type: Number,
      default: 0
    },
    last_sale_date: {
      type: Date
    }
  },

  is_active: {
    type: Boolean,
    default: true,
    index: true
  },

  last_activity: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
businessSchema.index({ phone: 1 }, { unique: true });
businessSchema.index({ 'subscription.status': 1, 'subscription.end_date': 1 });
businessSchema.index({ business_type: 1, is_active: 1 });
businessSchema.index({ created_at: -1 });

// Virtual for subscription status
businessSchema.virtual('subscription_active').get(function() {
  return this.subscription.status === 'active' &&
         this.subscription.end_date > new Date();
});

// Virtual for days remaining in subscription
businessSchema.virtual('days_remaining').get(function() {
  if (this.subscription.end_date) {
    const now = new Date();
    const endDate = new Date(this.subscription.end_date);
    const diffTime = endDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }
  return 0;
});

// Pre-save middleware
businessSchema.pre('save', async function(next) {
  // Format phone number
  if (this.isModified('phone')) {
    this.phone = this.phone.replace(/\s/g, '');
    if (this.phone.startsWith('0')) {
      this.phone = '+255' + this.phone.substring(1);
    } else if (!this.phone.startsWith('+')) {
      this.phone = '+' + this.phone;
    }
  }

  // Update last activity
  this.last_activity = new Date();

  next();
});

// Instance methods
businessSchema.methods.generateVerificationCode = function() {
  const code = Math.floor(100000 + Math.random() * 900000).toString();
  this.verification.verification_code = code;
  this.verification.verification_expires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  this.verification.verification_attempts = 0;
  return code;
};

businessSchema.methods.verifyCode = function(code) {
  if (this.verification.verification_attempts >= 3) {
    throw new Error('Too many verification attempts');
  }

  if (this.verification.verification_expires < new Date()) {
    throw new Error('Verification code expired');
  }

  if (this.verification.verification_code !== code) {
    this.verification.verification_attempts += 1;
    throw new Error('Invalid verification code');
  }

  this.verification.phone_verified = true;
  this.verification.verification_code = undefined;
  this.verification.verification_expires = undefined;
  this.verification.verification_attempts = 0;

  return true;
};

businessSchema.methods.updateStats = async function() {
  const Product = mongoose.model('Product');
  const Sale = mongoose.model('Sale');
  const Customer = mongoose.model('Customer');

  try {
    // Update product count
    this.stats.total_products = await Product.countDocuments({
      business_id: this._id,
      is_active: true
    });

    // Update sales stats
    const salesStats = await Sale.aggregate([
      { $match: { business_id: this._id } },
      {
        $group: {
          _id: null,
          total_sales: { $sum: 1 },
          total_revenue: { $sum: '$total' },
          last_sale: { $max: '$sale_date' }
        }
      }
    ]);

    if (salesStats.length > 0) {
      this.stats.total_sales = salesStats[0].total_sales;
      this.stats.total_revenue = salesStats[0].total_revenue;
      this.stats.last_sale_date = salesStats[0].last_sale;
    }

    // Update customer count
    this.stats.total_customers = await Customer.countDocuments({
      business_id: this._id
    });

    await this.save();
  } catch (error) {
    console.error('Error updating business stats:', error);
  }
};

// Static methods
businessSchema.statics.findByPhone = function(phone) {
  const formattedPhone = phone.replace(/\s/g, '');
  // Escape special regex characters
  const escapedPhone = formattedPhone.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  return this.findOne({ phone: new RegExp(escapedPhone, 'i') });
};

businessSchema.statics.getActiveBusinesses = function() {
  return this.find({
    is_active: true,
    'subscription.status': 'active',
    'subscription.end_date': { $gt: new Date() }
  });
};

module.exports = mongoose.model('Business', businessSchema);
