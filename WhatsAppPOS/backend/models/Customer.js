/**
 * Customer Model - Handles customer data and loyalty tracking
 * Supports customer relationship management for repeat business
 */

const mongoose = require('mongoose');

const customerSchema = new mongoose.Schema({
  business_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Business',
    required: [true, 'Business ID is required'],
    index: true
  },
  
  name: {
    type: String,
    required: [true, 'Customer name is required'],
    trim: true,
    maxlength: [100, 'Customer name cannot exceed 100 characters'],
    index: true
  },
  
  phone: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        if (!v) return true; // Optional field
        return /^(\+255|255|0)?[67]\d{8}$/.test(v.replace(/\s/g, ''));
      },
      message: 'Please enter a valid Tanzanian phone number'
    },
    index: true
  },
  
  email: {
    type: String,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(v) {
        if (!v) return true; // Optional field
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
      },
      message: 'Please enter a valid email address'
    }
  },
  
  address: {
    street: {
      type: String,
      trim: true,
      maxlength: [200, 'Street address too long']
    },
    ward: {
      type: String,
      trim: true,
      maxlength: [50, 'Ward name too long']
    },
    district: {
      type: String,
      trim: true,
      maxlength: [50, 'District name too long']
    },
    region: {
      type: String,
      trim: true,
      maxlength: [50, 'Region name too long']
    }
  },
  
  demographics: {
    date_of_birth: {
      type: Date,
      validate: {
        validator: function(v) {
          if (!v) return true;
          return v <= new Date(); // Cannot be in the future
        },
        message: 'Date of birth cannot be in the future'
      }
    },
    gender: {
      type: String,
      enum: ['male', 'female', 'other', 'prefer_not_to_say'],
      lowercase: true
    },
    occupation: {
      type: String,
      trim: true,
      maxlength: [50, 'Occupation too long']
    }
  },
  
  purchase_history: {
    total_purchases: {
      type: Number,
      default: 0,
      min: [0, 'Total purchases cannot be negative']
    },
    total_spent: {
      type: Number,
      default: 0,
      min: [0, 'Total spent cannot be negative']
    },
    average_order_value: {
      type: Number,
      default: 0,
      min: [0, 'Average order value cannot be negative']
    },
    first_purchase_date: {
      type: Date
    },
    last_purchase_date: {
      type: Date,
      index: true
    },
    favorite_products: [{
      product_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product'
      },
      product_name: {
        type: String,
        trim: true
      },
      purchase_count: {
        type: Number,
        default: 1,
        min: [1, 'Purchase count must be at least 1']
      }
    }],
    preferred_payment_method: {
      type: String,
      enum: ['cash', 'mpesa', 'tigo', 'airtel', 'bank', 'credit']
    }
  },
  
  loyalty: {
    points: {
      type: Number,
      default: 0,
      min: [0, 'Loyalty points cannot be negative']
    },
    tier: {
      type: String,
      enum: ['bronze', 'silver', 'gold', 'platinum'],
      default: 'bronze'
    },
    points_earned: {
      type: Number,
      default: 0,
      min: [0, 'Points earned cannot be negative']
    },
    points_redeemed: {
      type: Number,
      default: 0,
      min: [0, 'Points redeemed cannot be negative']
    },
    last_points_activity: {
      type: Date
    }
  },
  
  preferences: {
    communication: {
      whatsapp: {
        type: Boolean,
        default: true
      },
      sms: {
        type: Boolean,
        default: false
      },
      email: {
        type: Boolean,
        default: false
      }
    },
    language: {
      type: String,
      enum: ['sw', 'en'],
      default: 'sw'
    },
    marketing_consent: {
      type: Boolean,
      default: false
    }
  },
  
  notes: {
    type: String,
    trim: true,
    maxlength: [500, 'Notes cannot exceed 500 characters']
  },
  
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag too long']
  }],
  
  credit_info: {
    credit_limit: {
      type: Number,
      default: 0,
      min: [0, 'Credit limit cannot be negative']
    },
    current_balance: {
      type: Number,
      default: 0
    },
    last_payment_date: {
      type: Date
    }
  },
  
  is_active: {
    type: Boolean,
    default: true,
    index: true
  },
  
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Creator is required']
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for performance
customerSchema.index({ business_id: 1, is_active: 1 });
customerSchema.index({ business_id: 1, name: 1 });
customerSchema.index({ business_id: 1, phone: 1 });
customerSchema.index({ business_id: 1, 'purchase_history.total_spent': -1 });
customerSchema.index({ business_id: 1, 'purchase_history.last_purchase_date': -1 });
customerSchema.index({ business_id: 1, 'loyalty.points': -1 });

// Ensure phone is unique within business (if provided)
customerSchema.index({ business_id: 1, phone: 1 }, { 
  unique: true, 
  sparse: true,
  partialFilterExpression: { phone: { $exists: true, $ne: null, $ne: '' } }
});

// Virtual for customer status
customerSchema.virtual('status').get(function() {
  if (!this.is_active) return 'inactive';
  
  const daysSinceLastPurchase = this.purchase_history.last_purchase_date 
    ? Math.floor((new Date() - this.purchase_history.last_purchase_date) / (1000 * 60 * 60 * 24))
    : null;
  
  if (!daysSinceLastPurchase) return 'new';
  if (daysSinceLastPurchase <= 30) return 'active';
  if (daysSinceLastPurchase <= 90) return 'inactive';
  return 'dormant';
});

// Virtual for age
customerSchema.virtual('age').get(function() {
  if (!this.demographics.date_of_birth) return null;
  
  const today = new Date();
  const birthDate = new Date(this.demographics.date_of_birth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
});

// Virtual for available credit
customerSchema.virtual('available_credit').get(function() {
  return Math.max(0, this.credit_info.credit_limit - this.credit_info.current_balance);
});

// Virtual for loyalty tier progress
customerSchema.virtual('loyalty_progress').get(function() {
  const tiers = {
    bronze: { min: 0, max: 1000 },
    silver: { min: 1000, max: 5000 },
    gold: { min: 5000, max: 15000 },
    platinum: { min: 15000, max: Infinity }
  };
  
  const currentTier = tiers[this.loyalty.tier];
  const progress = currentTier.max === Infinity 
    ? 100 
    : Math.min(100, (this.loyalty.points / currentTier.max) * 100);
  
  return {
    current_tier: this.loyalty.tier,
    points: this.loyalty.points,
    progress_percentage: Math.round(progress),
    next_tier_points: currentTier.max === Infinity ? null : currentTier.max - this.loyalty.points
  };
});

// Pre-save middleware
customerSchema.pre('save', function(next) {
  // Format phone number
  if (this.isModified('phone') && this.phone) {
    this.phone = this.phone.replace(/\s/g, '');
    if (this.phone.startsWith('0')) {
      this.phone = '+255' + this.phone.substring(1);
    } else if (!this.phone.startsWith('+')) {
      this.phone = '+' + this.phone;
    }
  }
  
  // Calculate average order value
  if (this.purchase_history.total_purchases > 0) {
    this.purchase_history.average_order_value = 
      this.purchase_history.total_spent / this.purchase_history.total_purchases;
  }
  
  // Update loyalty tier based on points
  this.updateLoyaltyTier();
  
  next();
});

// Instance methods
customerSchema.methods.updateLoyaltyTier = function() {
  const points = this.loyalty.points;
  
  if (points >= 15000) {
    this.loyalty.tier = 'platinum';
  } else if (points >= 5000) {
    this.loyalty.tier = 'gold';
  } else if (points >= 1000) {
    this.loyalty.tier = 'silver';
  } else {
    this.loyalty.tier = 'bronze';
  }
};

customerSchema.methods.addPurchase = function(saleAmount, paymentMethod = null) {
  this.purchase_history.total_purchases += 1;
  this.purchase_history.total_spent += saleAmount;
  this.purchase_history.last_purchase_date = new Date();
  
  if (!this.purchase_history.first_purchase_date) {
    this.purchase_history.first_purchase_date = new Date();
  }
  
  if (paymentMethod) {
    this.purchase_history.preferred_payment_method = paymentMethod;
  }
  
  // Award loyalty points (1 point per 100 TSh spent)
  const pointsEarned = Math.floor(saleAmount / 100);
  this.addLoyaltyPoints(pointsEarned);
  
  return this.save();
};

customerSchema.methods.addLoyaltyPoints = function(points) {
  this.loyalty.points += points;
  this.loyalty.points_earned += points;
  this.loyalty.last_points_activity = new Date();
  this.updateLoyaltyTier();
};

customerSchema.methods.redeemLoyaltyPoints = function(points) {
  if (this.loyalty.points < points) {
    throw new Error('Insufficient loyalty points');
  }
  
  this.loyalty.points -= points;
  this.loyalty.points_redeemed += points;
  this.loyalty.last_points_activity = new Date();
  this.updateLoyaltyTier();
  
  return this.save();
};

customerSchema.methods.addFavoriteProduct = function(productId, productName) {
  const existingFavorite = this.purchase_history.favorite_products.find(
    fav => fav.product_id.toString() === productId.toString()
  );
  
  if (existingFavorite) {
    existingFavorite.purchase_count += 1;
  } else {
    this.purchase_history.favorite_products.push({
      product_id: productId,
      product_name: productName,
      purchase_count: 1
    });
  }
  
  // Keep only top 10 favorite products
  this.purchase_history.favorite_products.sort((a, b) => b.purchase_count - a.purchase_count);
  this.purchase_history.favorite_products = this.purchase_history.favorite_products.slice(0, 10);
  
  return this.save();
};

// Static methods
customerSchema.statics.findByBusiness = function(businessId, options = {}) {
  const query = { business_id: businessId };
  
  if (options.activeOnly !== false) {
    query.is_active = true;
  }
  
  if (options.status) {
    // This would need to be implemented with aggregation for virtual status
  }
  
  return this.find(query);
};

customerSchema.statics.findByPhone = function(businessId, phone) {
  const formattedPhone = phone.replace(/\s/g, '');
  return this.findOne({ 
    business_id: businessId,
    phone: new RegExp(formattedPhone, 'i') 
  });
};

customerSchema.statics.getTopCustomers = function(businessId, limit = 10) {
  return this.find({
    business_id: businessId,
    is_active: true
  })
  .sort({ 'purchase_history.total_spent': -1 })
  .limit(limit);
};

customerSchema.statics.getCustomerAnalytics = function(businessId) {
  return this.aggregate([
    { $match: { business_id: mongoose.Types.ObjectId(businessId), is_active: true } },
    {
      $group: {
        _id: null,
        total_customers: { $sum: 1 },
        total_spent: { $sum: '$purchase_history.total_spent' },
        average_spent: { $avg: '$purchase_history.total_spent' },
        loyalty_tiers: {
          $push: '$loyalty.tier'
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Customer', customerSchema);
