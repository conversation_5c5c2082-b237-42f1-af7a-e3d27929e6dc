/**
 * WhatsApp POS Tanzania - Express Application Configuration (Debug Version)
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const path = require('path');

console.log('Basic imports loaded...');

const logger = require('./config/logger');
console.log('Logger loaded...');

const errorHandler = require('./middleware/errorHandler');
console.log('Error handler loaded...');

// Import routes with error handling
let authRoutes, businessRoutes, productRoutes, salesRoutes, customerRoutes, reportRoutes, userRoutes, webhookRoutes;

try {
  console.log('Loading auth routes...');
  authRoutes = require('./routes/auth');
  console.log('Auth routes loaded');
} catch (error) {
  console.error('Failed to load auth routes:', error.message);
  process.exit(1);
}

try {
  console.log('Loading business routes...');
  businessRoutes = require('./routes/businesses');
  console.log('Business routes loaded');
} catch (error) {
  console.error('Failed to load business routes:', error.message);
  process.exit(1);
}

try {
  console.log('Loading product routes...');
  productRoutes = require('./routes/products');
  console.log('Product routes loaded');
} catch (error) {
  console.error('Failed to load product routes:', error.message);
  process.exit(1);
}

try {
  console.log('Loading sales routes...');
  salesRoutes = require('./routes/sales');
  console.log('Sales routes loaded');
} catch (error) {
  console.error('Failed to load sales routes:', error.message);
  process.exit(1);
}

try {
  console.log('Loading customer routes...');
  customerRoutes = require('./routes/customers');
  console.log('Customer routes loaded');
} catch (error) {
  console.error('Failed to load customer routes:', error.message);
  process.exit(1);
}

try {
  console.log('Loading report routes...');
  reportRoutes = require('./routes/reports');
  console.log('Report routes loaded');
} catch (error) {
  console.error('Failed to load report routes:', error.message);
  process.exit(1);
}

try {
  console.log('Loading user routes...');
  userRoutes = require('./routes/users');
  console.log('User routes loaded');
} catch (error) {
  console.error('Failed to load user routes:', error.message);
  process.exit(1);
}

try {
  console.log('Loading webhook routes...');
  webhookRoutes = require('./routes/webhook');
  console.log('Webhook routes loaded');
} catch (error) {
  console.error('Failed to load webhook routes:', error.message);
  process.exit(1);
}

console.log('All routes loaded successfully');

const app = express();
console.log('Express app created');

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);
console.log('Trust proxy set');

try {
  console.log('Setting up security middleware...');
  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"]
      }
    },
    crossOriginEmbedderPolicy: false
  }));
  console.log('Security middleware set');
} catch (error) {
  console.error('Failed to set security middleware:', error.message);
  process.exit(1);
}

try {
  console.log('Setting up compression middleware...');
  // Compression middleware
  app.use(compression());
  console.log('Compression middleware set');
} catch (error) {
  console.error('Failed to set compression middleware:', error.message);
  process.exit(1);
}

try {
  console.log('Setting up CORS middleware...');
  // CORS configuration
  const corsOptions = {
    origin: function (origin, callback) {
      const allowedOrigins = [
        process.env.FRONTEND_URL,
        process.env.DASHBOARD_URL,
        'http://localhost:3001',
        'http://localhost:3000'
      ].filter(Boolean);

      // Allow requests with no origin (mobile apps, Postman, etc.)
      if (!origin) return callback(null, true);

      if (allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  };

  app.use(cors(corsOptions));
  console.log('CORS middleware set');
} catch (error) {
  console.error('Failed to set CORS middleware:', error.message);
  process.exit(1);
}

try {
  console.log('Setting up rate limiting middleware...');
  // Rate limiting
  const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    message: {
      error: 'Too many requests from this IP, please try again later.',
      sw: 'Maombi mengi sana kutoka IP hii, jaribu tena baadaye.',
      en: 'Too many requests from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for webhook endpoints
      return req.path.startsWith('/webhook/');
    }
  });

  app.use(limiter);
  console.log('Rate limiting middleware set');
} catch (error) {
  console.error('Failed to set rate limiting middleware:', error.message);
  process.exit(1);
}

try {
  console.log('Setting up body parsing middleware...');
  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  console.log('Body parsing middleware set');
} catch (error) {
  console.error('Failed to set body parsing middleware:', error.message);
  process.exit(1);
}

try {
  console.log('Setting up monitoring middleware...');
  const { requestMonitoring } = require('./middleware/monitoring');
  app.use(requestMonitoring);
  console.log('Monitoring middleware set');
} catch (error) {
  console.error('Failed to set monitoring middleware:', error.message);
  process.exit(1);
}

try {
  console.log('Setting up logging middleware...');
  // Logging middleware
  if (process.env.NODE_ENV === 'development') {
    app.use(morgan('dev'));
  } else {
    app.use(morgan('combined', {
      stream: {
        write: (message) => logger.info(message.trim())
      }
    }));
  }
  console.log('Logging middleware set');
} catch (error) {
  console.error('Failed to set logging middleware:', error.message);
  process.exit(1);
}

try {
  console.log('Setting up enhanced health check endpoints...');
  const { getHealthStatus, getMetrics } = require('./middleware/monitoring');

  // Basic health check
  app.get('/health', (req, res) => {
    const healthStatus = getHealthStatus();
    const statusCode = healthStatus.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(healthStatus);
  });

  // Detailed metrics endpoint
  app.get('/metrics', (req, res) => {
    res.status(200).json(getMetrics());
  });

  // Simple status for load balancers
  app.get('/status', (req, res) => {
    res.status(200).send('OK');
  });

  console.log('Enhanced health check endpoints set');
} catch (error) {
  console.error('Failed to set health check endpoints:', error.message);
  process.exit(1);
}

try {
  console.log('Setting up API routes...');
  // API routes
  app.use('/api/auth', authRoutes);
  app.use('/api/businesses', businessRoutes);
  app.use('/api/products', productRoutes);
  app.use('/api/sales', salesRoutes);
  app.use('/api/customers', customerRoutes);
  app.use('/api/reports', reportRoutes);
  app.use('/api/users', userRoutes);

  // Webhook routes (no auth required)
  app.use('/webhook', webhookRoutes);
  console.log('API routes set');
} catch (error) {
  console.error('Failed to set API routes:', error.message);
  process.exit(1);
}

try {
  console.log('Setting up dashboard static files...');
  // Serve static files from dashboard
  app.use(express.static(path.join(__dirname, '../dashboard/public')));

  // Serve dashboard for all non-API routes
  app.get('*', (req, res) => {
    if (!req.path.startsWith('/api/') && !req.path.startsWith('/webhook/') && !req.path.startsWith('/health') && !req.path.startsWith('/metrics') && !req.path.startsWith('/status')) {
      res.sendFile(path.join(__dirname, '../dashboard/public/index.html'));
    } else {
      res.status(404).json({
        error: 'Endpoint not found',
        sw: 'Njia haijapatikana',
        en: 'Endpoint not found'
      });
    }
  });
  console.log('Dashboard static files set');
} catch (error) {
  console.error('Failed to set dashboard static files:', error.message);
  process.exit(1);
}

console.log('App setup completed successfully');

module.exports = app;
