/**
 * Authentication Middleware
 * JWT-based authentication with business isolation and role-based access
 */

const jwt = require('jsonwebtoken');
const { User, Business } = require('../models');
const logger = require('../config/logger');

/**
 * Verify JWT token and authenticate user
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    
    if (!token) {
      return res.status(401).json({
        error: 'Access token required',
        sw: 'Tokeni ya ufikiaji inahitajika',
        en: 'Access token required'
      });
    }
    
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Find user and populate business data
    const user = await User.findById(decoded.userId)
      .populate('business_id', 'name phone subscription settings is_active')
      .select('+authentication.last_login');
    
    if (!user) {
      logger.security('Authentication failed - user not found', { userId: decoded.userId });
      return res.status(401).json({
        error: 'Invalid token - user not found',
        sw: 'Tokeni si sahihi - mtumiaji hajapatikana',
        en: 'Invalid token - user not found'
      });
    }
    
    if (!user.is_active) {
      logger.security('Authentication failed - user inactive', { userId: user._id });
      return res.status(401).json({
        error: 'Account is inactive',
        sw: 'Akaunti haifanyi kazi',
        en: 'Account is inactive'
      });
    }
    
    // Check if business is active
    if (!user.business_id || !user.business_id.is_active) {
      logger.security('Authentication failed - business inactive', { 
        userId: user._id, 
        businessId: user.business_id?._id 
      });
      return res.status(401).json({
        error: 'Business account is inactive',
        sw: 'Akaunti ya biashara haifanyi kazi',
        en: 'Business account is inactive'
      });
    }
    
    // Check subscription status
    const business = user.business_id;
    if (business.subscription.status !== 'active' || 
        business.subscription.end_date < new Date()) {
      logger.security('Authentication failed - subscription expired', { 
        userId: user._id, 
        businessId: business._id 
      });
      return res.status(402).json({
        error: 'Subscription expired',
        sw: 'Usajili umekwisha',
        en: 'Subscription expired',
        subscription: {
          status: business.subscription.status,
          end_date: business.subscription.end_date,
          plan: business.subscription.plan
        }
      });
    }
    
    // Update last activity
    user.last_active = new Date();
    user.authentication.last_login = new Date();
    await user.save();
    
    // Attach user and business to request
    req.user = user;
    req.business = business;
    
    logger.info('User authenticated successfully', {
      userId: user._id,
      businessId: business._id,
      role: user.role
    });
    
    next();
    
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      logger.security('Invalid JWT token', { error: error.message });
      return res.status(401).json({
        error: 'Invalid token',
        sw: 'Tokeni si sahihi',
        en: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      logger.security('JWT token expired', { error: error.message });
      return res.status(401).json({
        error: 'Token expired',
        sw: 'Tokeni imekwisha',
        en: 'Token expired'
      });
    }
    
    logger.error('Authentication error:', error);
    res.status(500).json({
      error: 'Authentication failed',
      sw: 'Uthibitisho umeshindwa',
      en: 'Authentication failed'
    });
  }
};

/**
 * Check if user has specific permission
 */
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        sw: 'Uthibitisho unahitajika',
        en: 'Authentication required'
      });
    }
    
    if (!req.user.hasPermission(permission)) {
      logger.security('Permission denied', {
        userId: req.user._id,
        permission,
        userRole: req.user.role
      });
      
      return res.status(403).json({
        error: 'Insufficient permissions',
        sw: 'Huna ruhusa ya kutosha',
        en: 'Insufficient permissions',
        required_permission: permission
      });
    }
    
    next();
  };
};

/**
 * Check if user has specific role
 */
const requireRole = (roles) => {
  const allowedRoles = Array.isArray(roles) ? roles : [roles];
  
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        sw: 'Uthibitisho unahitajika',
        en: 'Authentication required'
      });
    }
    
    if (!allowedRoles.includes(req.user.role)) {
      logger.security('Role access denied', {
        userId: req.user._id,
        userRole: req.user.role,
        requiredRoles: allowedRoles
      });
      
      return res.status(403).json({
        error: 'Insufficient role permissions',
        sw: 'Huna ruhusa ya kutosha kwa jukumu lako',
        en: 'Insufficient role permissions',
        required_roles: allowedRoles
      });
    }
    
    next();
  };
};

/**
 * Optional authentication - doesn't fail if no token provided
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
      return next(); // Continue without authentication
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId)
      .populate('business_id', 'name phone subscription settings is_active');
    
    if (user && user.is_active && user.business_id && user.business_id.is_active) {
      req.user = user;
      req.business = user.business_id;
    }
    
    next();
    
  } catch (error) {
    // Ignore authentication errors for optional auth
    next();
  }
};

/**
 * Generate JWT token for user
 */
const generateToken = (userId, businessId) => {
  const payload = {
    userId,
    businessId,
    iat: Math.floor(Date.now() / 1000)
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  });
};

/**
 * Generate refresh token
 */
const generateRefreshToken = (userId, businessId) => {
  const payload = {
    userId,
    businessId,
    type: 'refresh',
    iat: Math.floor(Date.now() / 1000)
  };
  
  return jwt.sign(payload, process.env.JWT_REFRESH_SECRET, {
    expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  });
};

/**
 * Verify refresh token
 */
const verifyRefreshToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_REFRESH_SECRET);
    
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    throw new Error('Invalid refresh token');
  }
};

/**
 * Business isolation middleware - ensures user can only access their business data
 */
const ensureBusinessAccess = (req, res, next) => {
  const businessId = req.params.businessId || req.body.business_id || req.query.business_id;
  
  if (businessId && businessId !== req.business._id.toString()) {
    logger.security('Business access violation attempt', {
      userId: req.user._id,
      userBusinessId: req.business._id,
      attemptedBusinessId: businessId
    });
    
    return res.status(403).json({
      error: 'Access denied to this business',
      sw: 'Hauruhusiwi kufikia biashara hii',
      en: 'Access denied to this business'
    });
  }
  
  next();
};

module.exports = {
  authenticateToken,
  requirePermission,
  requireRole,
  optionalAuth,
  generateToken,
  generateRefreshToken,
  verifyRefreshToken,
  ensureBusinessAccess
};
