/**
 * Security Middleware for Production SSL/HTTPS Configuration
 * Comprehensive security headers and HTTPS enforcement
 */

const helmet = require('helmet');
const logger = require('../config/logger');

/**
 * HTTPS redirect middleware
 * Redirects HTTP requests to HTTPS in production
 */
const httpsRedirect = (req, res, next) => {
  // Skip in development or if already HTTPS
  if (process.env.NODE_ENV !== 'production' || req.secure || req.headers['x-forwarded-proto'] === 'https') {
    return next();
  }

  // Log redirect attempt
  logger.security('HTTP to HTTPS redirect', {
    originalUrl: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Redirect to HTTPS
  const httpsUrl = `https://${req.get('host')}${req.originalUrl}`;
  res.redirect(301, httpsUrl);
};

/**
 * Enhanced Helmet configuration for production security
 */
const securityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: [
        "'self'",
        "'unsafe-inline'", // Required for Material-UI
        "https://fonts.googleapis.com",
        "https://cdnjs.cloudflare.com"
      ],
      fontSrc: [
        "'self'",
        "https://fonts.gstatic.com",
        "https://cdnjs.cloudflare.com"
      ],
      scriptSrc: [
        "'self'",
        process.env.NODE_ENV === 'development' ? "'unsafe-eval'" : null // Only in dev
      ].filter(Boolean),
      imgSrc: [
        "'self'",
        "data:",
        "https:"
      ],
      connectSrc: [
        "'self'",
        "https://api.twilio.com",
        "wss:"
      ],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null
    }
  },

  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },

  // X-Frame-Options
  frameguard: {
    action: 'deny'
  },

  // X-Content-Type-Options
  noSniff: true,

  // X-XSS-Protection
  xssFilter: true,

  // Referrer Policy
  referrerPolicy: {
    policy: 'strict-origin-when-cross-origin'
  },

  // Permissions Policy
  permissionsPolicy: {
    features: {
      camera: [],
      microphone: [],
      geolocation: ['self'],
      payment: ['self']
    }
  }
});

/**
 * Secure cookie configuration
 */
const secureCookies = (req, res, next) => {
  if (process.env.NODE_ENV === 'production') {
    // Override res.cookie to add secure flags
    const originalCookie = res.cookie;
    res.cookie = function(name, value, options = {}) {
      options.secure = true;
      options.httpOnly = true;
      options.sameSite = 'strict';
      return originalCookie.call(this, name, value, options);
    };
  }
  next();
};

/**
 * Security monitoring middleware
 */
const securityMonitoring = (req, res, next) => {
  // Log security-relevant events
  const securityHeaders = {
    'x-forwarded-for': req.get('x-forwarded-for'),
    'x-real-ip': req.get('x-real-ip'),
    'user-agent': req.get('user-agent'),
    'origin': req.get('origin'),
    'referer': req.get('referer')
  };

  // Detect potential security threats
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /vbscript:/i,  // VBScript injection
    /onload=/i,  // Event handler injection
    /onerror=/i  // Error handler injection
  ];

  const url = req.originalUrl;
  const body = JSON.stringify(req.body || {});
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(url) || pattern.test(body)) {
      logger.security('Suspicious request detected', {
        pattern: pattern.toString(),
        url,
        ip: req.ip,
        headers: securityHeaders,
        body: req.body
      });
      break;
    }
  }

  next();
};

module.exports = {
  httpsRedirect,
  securityHeaders,
  secureCookies,
  securityMonitoring
};
