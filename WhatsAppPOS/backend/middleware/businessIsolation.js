/**
 * Business Isolation Middleware
 * Ensures strict data isolation between businesses for security and privacy
 */

const mongoose = require('mongoose');
const logger = require('../config/logger');
const { AppError } = require('./errorHandler');

/**
 * Ensure business isolation for database queries
 * Automatically adds business_id filter to prevent cross-business data access
 */
const ensureBusinessIsolation = (req, res, next) => {
  if (!req.user || !req.business) {
    return next(new AppError('Authentication required for business isolation', 401));
  }
  
  const businessId = req.business._id;
  
  // Store original query methods to add business_id filter
  const originalFind = mongoose.Model.find;
  const originalFindOne = mongoose.Model.findOne;
  const originalFindOneAndUpdate = mongoose.Model.findOneAndUpdate;
  const originalFindOneAndDelete = mongoose.Model.findOneAndDelete;
  const originalUpdateOne = mongoose.Model.updateOne;
  const originalUpdateMany = mongoose.Model.updateMany;
  const originalDeleteOne = mongoose.Model.deleteOne;
  const originalDeleteMany = mongoose.Model.deleteMany;
  const originalCountDocuments = mongoose.Model.countDocuments;
  const originalAggregate = mongoose.Model.aggregate;
  
  // Override query methods to add business_id filter
  const addBusinessFilter = function(filter = {}) {
    // Skip business isolation for Business and User models when appropriate
    const modelName = this.modelName;
    
    if (modelName === 'Business') {
      // For Business model, only allow access to user's own business
      if (!filter._id) {
        filter._id = businessId;
      } else if (filter._id.toString() !== businessId.toString()) {
        logger.security('Business isolation violation attempt', {
          userId: req.user._id,
          userBusinessId: businessId,
          attemptedBusinessId: filter._id,
          modelName
        });
        throw new AppError('Access denied to this business', 403);
      }
    } else if (modelName === 'User') {
      // For User model, only allow access to users in the same business
      filter.business_id = businessId;
    } else {
      // For all other models, add business_id filter
      if (this.schema.paths.business_id) {
        filter.business_id = businessId;
      }
    }
    
    return filter;
  };
  
  // Override find methods
  mongoose.Model.find = function(filter, ...args) {
    try {
      filter = addBusinessFilter.call(this, filter);
      return originalFind.call(this, filter, ...args);
    } catch (error) {
      throw error;
    }
  };
  
  mongoose.Model.findOne = function(filter, ...args) {
    try {
      filter = addBusinessFilter.call(this, filter);
      return originalFindOne.call(this, filter, ...args);
    } catch (error) {
      throw error;
    }
  };
  
  mongoose.Model.findOneAndUpdate = function(filter, update, ...args) {
    try {
      filter = addBusinessFilter.call(this, filter);
      return originalFindOneAndUpdate.call(this, filter, update, ...args);
    } catch (error) {
      throw error;
    }
  };
  
  mongoose.Model.findOneAndDelete = function(filter, ...args) {
    try {
      filter = addBusinessFilter.call(this, filter);
      return originalFindOneAndDelete.call(this, filter, ...args);
    } catch (error) {
      throw error;
    }
  };
  
  mongoose.Model.updateOne = function(filter, update, ...args) {
    try {
      filter = addBusinessFilter.call(this, filter);
      return originalUpdateOne.call(this, filter, update, ...args);
    } catch (error) {
      throw error;
    }
  };
  
  mongoose.Model.updateMany = function(filter, update, ...args) {
    try {
      filter = addBusinessFilter.call(this, filter);
      return originalUpdateMany.call(this, filter, update, ...args);
    } catch (error) {
      throw error;
    }
  };
  
  mongoose.Model.deleteOne = function(filter, ...args) {
    try {
      filter = addBusinessFilter.call(this, filter);
      return originalDeleteOne.call(this, filter, ...args);
    } catch (error) {
      throw error;
    }
  };
  
  mongoose.Model.deleteMany = function(filter, ...args) {
    try {
      filter = addBusinessFilter.call(this, filter);
      return originalDeleteMany.call(this, filter, ...args);
    } catch (error) {
      throw error;
    }
  };
  
  mongoose.Model.countDocuments = function(filter, ...args) {
    try {
      filter = addBusinessFilter.call(this, filter);
      return originalCountDocuments.call(this, filter, ...args);
    } catch (error) {
      throw error;
    }
  };
  
  mongoose.Model.aggregate = function(pipeline, ...args) {
    try {
      // Add business_id match at the beginning of aggregation pipeline
      if (this.schema.paths.business_id) {
        pipeline.unshift({ $match: { business_id: businessId } });
      }
      return originalAggregate.call(this, pipeline, ...args);
    } catch (error) {
      throw error;
    }
  };
  
  // Restore original methods after request
  res.on('finish', () => {
    mongoose.Model.find = originalFind;
    mongoose.Model.findOne = originalFindOne;
    mongoose.Model.findOneAndUpdate = originalFindOneAndUpdate;
    mongoose.Model.findOneAndDelete = originalFindOneAndDelete;
    mongoose.Model.updateOne = originalUpdateOne;
    mongoose.Model.updateMany = originalUpdateMany;
    mongoose.Model.deleteOne = originalDeleteOne;
    mongoose.Model.deleteMany = originalDeleteMany;
    mongoose.Model.countDocuments = originalCountDocuments;
    mongoose.Model.aggregate = originalAggregate;
  });
  
  next();
};

/**
 * Validate business ownership of a resource
 */
const validateBusinessOwnership = (Model, idField = '_id') => {
  return async (req, res, next) => {
    try {
      const resourceId = req.params[idField] || req.params.id;
      
      if (!resourceId) {
        return next(new AppError('Resource ID is required', 400));
      }
      
      if (!mongoose.Types.ObjectId.isValid(resourceId)) {
        return next(new AppError('Invalid resource ID', 400));
      }
      
      const resource = await Model.findById(resourceId);
      
      if (!resource) {
        return next(new AppError('Resource not found', 404));
      }
      
      // Check business ownership
      const resourceBusinessId = resource.business_id || resource._id; // For Business model
      
      if (resourceBusinessId.toString() !== req.business._id.toString()) {
        logger.security('Business ownership violation attempt', {
          userId: req.user._id,
          userBusinessId: req.business._id,
          resourceId,
          resourceBusinessId,
          modelName: Model.modelName
        });
        
        return next(new AppError('Access denied to this resource', 403));
      }
      
      // Attach resource to request for use in route handler
      req.resource = resource;
      next();
      
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Ensure new resources are created with correct business_id
 */
const enforceBusinessCreation = (req, res, next) => {
  if (!req.user || !req.business) {
    return next(new AppError('Authentication required', 401));
  }
  
  // Automatically set business_id for new resources
  if (req.body && typeof req.body === 'object') {
    req.body.business_id = req.business._id;
    
    // Also set created_by if the field exists
    if (req.user) {
      req.body.created_by = req.user._id;
    }
  }
  
  next();
};

/**
 * Validate business subscription limits
 */
const validateSubscriptionLimits = (resourceType) => {
  return async (req, res, next) => {
    try {
      const business = req.business;
      const subscription = business.subscription;
      
      // Get subscription limits
      const limits = getSubscriptionLimits(subscription.plan);
      
      if (!limits[resourceType]) {
        return next(); // No limits for this resource type
      }
      
      const limit = limits[resourceType];
      
      if (limit === 'unlimited') {
        return next(); // No limits
      }
      
      // Count existing resources
      let currentCount = 0;
      
      switch (resourceType) {
        case 'products':
          const Product = require('../models/Product');
          currentCount = await Product.countDocuments({
            business_id: business._id,
            is_active: true
          });
          break;
          
        case 'users':
          const User = require('../models/User');
          currentCount = await User.countDocuments({
            business_id: business._id,
            is_active: true
          });
          break;
          
        case 'monthly_sales':
          const Sale = require('../models/Sale');
          const startOfMonth = new Date();
          startOfMonth.setDate(1);
          startOfMonth.setHours(0, 0, 0, 0);
          
          currentCount = await Sale.countDocuments({
            business_id: business._id,
            sale_date: { $gte: startOfMonth },
            status: 'completed'
          });
          break;
      }
      
      if (currentCount >= limit) {
        logger.warn('Subscription limit exceeded', {
          businessId: business._id,
          resourceType,
          currentCount,
          limit,
          plan: subscription.plan
        });
        
        return res.status(402).json({
          error: 'Subscription limit exceeded',
          sw: 'Kikomo cha usajili kimezidishwa',
          en: 'Subscription limit exceeded',
          details: {
            resource_type: resourceType,
            current_count: currentCount,
            limit: limit,
            plan: subscription.plan
          }
        });
      }
      
      next();
      
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Get subscription limits based on plan
 */
const getSubscriptionLimits = (plan) => {
  const limits = {
    trial: {
      products: 50,
      users: 2,
      monthly_sales: 100
    },
    basic: {
      products: 500,
      users: 5,
      monthly_sales: 1000
    },
    pro: {
      products: 'unlimited',
      users: 20,
      monthly_sales: 'unlimited'
    }
  };
  
  return limits[plan] || limits.trial;
};

module.exports = {
  ensureBusinessIsolation,
  validateBusinessOwnership,
  enforceBusinessCreation,
  validateSubscriptionLimits,
  getSubscriptionLimits
};
