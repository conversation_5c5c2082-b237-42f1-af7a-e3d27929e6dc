/**
 * Validation Middleware using Joi
 * Comprehensive input validation for all API endpoints
 */

const Joi = require('joi');
const logger = require('../config/logger');

/**
 * Enhanced Tanzanian phone number validation
 */
const tanzanianPhone = Joi.string()
  .pattern(/^(\+255|255|0)?[67]\d{8}$/)
  .custom((value, helpers) => {
    // Normalize phone number
    let normalized = value.replace(/\s+/g, ''); // Remove spaces

    // Convert to international format
    if (normalized.startsWith('0')) {
      normalized = '+255' + normalized.substring(1);
    } else if (normalized.startsWith('255')) {
      normalized = '+' + normalized;
    } else if (!normalized.startsWith('+255')) {
      return helpers.error('string.pattern.base');
    }

    // Validate length
    if (normalized.length !== 13) {
      return helpers.error('string.pattern.base');
    }

    return normalized;
  })
  .messages({
    'string.pattern.base': 'Please enter a valid Tanzanian phone number (e.g., +255712345678)'
  });

/**
 * Enhanced email validation with domain checking
 */
const enhancedEmail = Joi.string()
  .email({ minDomainSegments: 2, tlds: { allow: true } })
  .lowercase()
  .custom((value, helpers) => {
    // Block common disposable email domains
    const disposableDomains = [
      '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
      'mailinator.com', 'throwaway.email', 'temp-mail.org'
    ];

    const domain = value.split('@')[1];
    if (disposableDomains.includes(domain)) {
      return helpers.error('string.email.disposable');
    }

    return value;
  })
  .messages({
    'string.email.disposable': 'Disposable email addresses are not allowed'
  });

/**
 * Secure string validation (prevents XSS and injection)
 */
const secureString = (min = 1, max = 255) => Joi.string()
  .trim()
  .min(min)
  .max(max)
  .pattern(/^[^<>{}\\]*$/) // Prevent HTML tags and common injection characters
  .custom((value, helpers) => {
    // Additional XSS prevention
    const xssPatterns = [
      /<script/i, /javascript:/i, /vbscript:/i, /onload=/i, /onerror=/i,
      /onclick=/i, /onmouseover=/i, /onfocus=/i, /onblur=/i
    ];

    for (const pattern of xssPatterns) {
      if (pattern.test(value)) {
        return helpers.error('string.xss');
      }
    }

    return value;
  })
  .messages({
    'string.xss': 'Input contains potentially dangerous content',
    'string.pattern.base': 'Input contains invalid characters'
  });

/**
 * Monetary amount validation for Tanzanian Shillings
 */
const tanzanianAmount = Joi.number()
  .positive()
  .precision(2)
  .max(999999999.99) // Max ~1 billion TZS
  .custom((value, helpers) => {
    // Ensure reasonable business amounts
    if (value < 0.01) {
      return helpers.error('number.min');
    }

    // Round to 2 decimal places to prevent floating point issues
    return Math.round(value * 100) / 100;
  })
  .messages({
    'number.positive': 'Amount must be positive',
    'number.max': 'Amount is too large',
    'number.min': 'Amount must be at least 0.01 TZS'
  });

/**
 * Business registration validation schema
 */
const businessRegistrationSchema = Joi.object({
  name: secureString(2, 100).required().messages({
    'string.empty': 'Business name is required',
    'string.min': 'Business name must be at least 2 characters',
    'string.max': 'Business name cannot exceed 100 characters'
  }),

  phone: tanzanianPhone.required().messages({
    'any.required': 'Phone number is required'
  }),

  owner_name: secureString(2, 100).required().messages({
    'string.empty': 'Owner name is required',
    'string.min': 'Owner name must be at least 2 characters',
    'string.max': 'Owner name cannot exceed 100 characters'
  }),
  
  business_type: Joi.string().valid(
    'duka', 'shop', 'salon', 'restaurant', 'pharmacy', 'electronics',
    'clothing', 'hardware', 'grocery', 'services', 'repair', 'other'
  ).required().messages({
    'any.only': 'Invalid business type',
    'any.required': 'Business type is required'
  }),
  
  location: Joi.object({
    region: secureString(1, 50).optional(),
    district: secureString(1, 50).optional(),
    ward: secureString(1, 50).optional(),
    street: secureString(1, 100).optional()
  }).optional(),

  language: Joi.string().valid('sw', 'en').default('sw').messages({
    'any.only': 'Language must be either "sw" (Swahili) or "en" (English)'
  })
});

/**
 * User creation validation schema
 */
const userCreationSchema = Joi.object({
  name: secureString(2, 100).required(),
  phone: tanzanianPhone.required(),
  email: enhancedEmail.optional(),
  role: Joi.string().valid('owner', 'manager', 'employee', 'viewer').required(),
  permissions: Joi.object().optional(),

  // Additional security fields
  two_factor_enabled: Joi.boolean().default(false),
  password_expires_at: Joi.date().optional()
});

/**
 * Product validation schema
 */
const productSchema = Joi.object({
  name: secureString(1, 100).required().messages({
    'string.empty': 'Product name is required',
    'string.max': 'Product name cannot exceed 100 characters'
  }),

  description: secureString(0, 500).optional().allow(''),

  price: tanzanianAmount.required().messages({
    'any.required': 'Price is required'
  }),

  cost: tanzanianAmount.optional(),
  
  stock: Joi.number().integer().min(0).when('is_service', {
    is: false,
    then: Joi.required(),
    otherwise: Joi.optional()
  }).messages({
    'number.base': 'Stock must be a number',
    'number.integer': 'Stock must be a whole number',
    'number.min': 'Stock cannot be negative'
  }),
  
  category: Joi.string().valid(
    'food', 'drinks', 'electronics', 'clothing', 'beauty', 'health',
    'household', 'stationery', 'tools', 'automotive', 'books',
    'toys', 'sports', 'services', 'other'
  ).required(),
  
  is_service: Joi.boolean().default(false),
  
  sku: Joi.string().trim().max(50).optional().allow(''),
  
  low_stock_alert: Joi.number().integer().min(0).optional(),
  
  tags: Joi.array().items(Joi.string().trim().max(30)).max(10).optional()
});

/**
 * Sale validation schema
 */
const saleSchema = Joi.object({
  items: Joi.array().items(
    Joi.object({
      product_id: Joi.string().hex().length(24).required(),
      quantity: Joi.number().positive().precision(3).max(10000).required().messages({
        'number.positive': 'Quantity must be positive',
        'number.max': 'Quantity too large'
      }),
      unit_price: tanzanianAmount.optional(),
      discount_amount: tanzanianAmount.optional(),
      notes: secureString(0, 100).optional().allow('')
    })
  ).min(1).max(100).required().messages({
    'array.min': 'At least one item is required',
    'array.max': 'Too many items in single sale'
  }),

  customer_id: Joi.string().hex().length(24).optional(),

  payment_method: Joi.string().valid(
    'cash', 'mpesa', 'tigo_pesa', 'airtel_money', 'bank_transfer', 'credit', 'mixed'
  ).required(),

  payment_reference: secureString(0, 50).optional().allow(''),

  discount: Joi.object({
    amount: tanzanianAmount.optional(),
    percentage: Joi.number().min(0).max(100).precision(2).optional(),
    reason: secureString(0, 100).optional().allow('')
  }).optional(),

  tax: Joi.object({
    rate: Joi.number().min(0).max(50).precision(2).optional(),
    amount: tanzanianAmount.optional()
  }).optional(),

  notes: secureString(0, 500).optional().allow(''),

  customer_info: Joi.object({
    name: secureString(1, 100).optional().allow(''),
    phone: tanzanianPhone.optional().allow(''),
    email: enhancedEmail.optional().allow('')
  }).optional(),

  // Fraud prevention
  total_amount: tanzanianAmount.optional(), // Will be validated against calculated total
  payment_received: tanzanianAmount.optional()
});

/**
 * Customer validation schema
 */
const customerSchema = Joi.object({
  name: secureString(1, 100).required(),
  phone: tanzanianPhone.optional().allow(''),
  email: enhancedEmail.optional().allow(''),
  email: Joi.string().email().optional().allow(''),
  
  address: Joi.object({
    street: Joi.string().trim().max(200).optional().allow(''),
    ward: Joi.string().trim().max(50).optional().allow(''),
    district: Joi.string().trim().max(50).optional().allow(''),
    region: Joi.string().trim().max(50).optional().allow('')
  }).optional(),
  
  demographics: Joi.object({
    date_of_birth: Joi.date().max('now').optional(),
    gender: Joi.string().valid('male', 'female', 'other', 'prefer_not_to_say').optional(),
    occupation: Joi.string().trim().max(50).optional().allow('')
  }).optional(),
  
  notes: Joi.string().trim().max(500).optional().allow(''),
  tags: Joi.array().items(Joi.string().trim().max(30)).max(10).optional()
});

/**
 * Query parameter validation schemas
 */
const querySchemas = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sort: Joi.string().optional(),
    order: Joi.string().valid('asc', 'desc').default('desc')
  }),
  
  dateRange: Joi.object({
    start_date: Joi.date().optional(),
    end_date: Joi.date().min(Joi.ref('start_date')).optional(),
    period: Joi.string().valid('today', 'yesterday', 'week', 'month', 'year').optional()
  }),
  
  search: Joi.object({
    q: Joi.string().trim().min(1).max(100).optional(),
    category: Joi.string().optional(),
    status: Joi.string().optional()
  })
};

/**
 * Validation middleware factory
 */
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });
    
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        sw: getSwahiliValidationMessage(detail.message)
      }));
      
      logger.warn('Validation failed', {
        errors,
        url: req.originalUrl,
        method: req.method,
        userId: req.user?._id
      });
      
      return res.status(400).json({
        error: 'Validation failed',
        sw: 'Uthibitisho umeshindwa',
        en: 'Validation failed',
        details: errors
      });
    }
    
    // Replace the original data with validated and sanitized data
    req[property] = value;
    next();
  };
};

/**
 * Get Swahili validation messages
 */
const getSwahiliValidationMessage = (message) => {
  const translations = {
    'is required': 'inahitajika',
    'must be a string': 'lazima iwe maandishi',
    'must be a number': 'lazima iwe nambari',
    'must be an integer': 'lazima iwe nambari kamili',
    'cannot be negative': 'haiwezi kuwa hasi',
    'is not allowed to be empty': 'haikubali kuwa tupu',
    'must be at least': 'lazima iwe angalau',
    'must be less than or equal to': 'lazima iwe chini au sawa na',
    'must be greater than or equal to': 'lazima iwe juu au sawa na',
    'must be one of': 'lazima iwe moja ya',
    'Please enter a valid Tanzanian phone number': 'Ingiza nambari sahihi ya simu ya Tanzania',
    'Please enter a valid email address': 'Ingiza anwani sahihi ya barua pepe'
  };
  
  let swahiliMessage = message;
  for (const [english, swahili] of Object.entries(translations)) {
    swahiliMessage = swahiliMessage.replace(new RegExp(english, 'gi'), swahili);
  }
  
  return swahiliMessage;
};

/**
 * Sanitize input to prevent XSS and injection attacks
 */
const sanitizeInput = (req, res, next) => {
  const sanitize = (obj) => {
    if (typeof obj === 'string') {
      return obj.trim()
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitize);
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitize(value);
      }
      return sanitized;
    }
    
    return obj;
  };
  
  req.body = sanitize(req.body);
  req.query = sanitize(req.query);
  req.params = sanitize(req.params);
  
  next();
};

/**
 * Advanced security validation middleware
 */
const securityValidation = (req, res, next) => {
  // Check for common attack patterns in all string inputs
  const checkForAttacks = (obj, path = '') => {
    if (typeof obj === 'string') {
      // SQL injection patterns
      const sqlPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
        /(--|\/\*|\*\/|;)/,
        /(\bOR\b.*=.*\bOR\b)/i,
        /(\bAND\b.*=.*\bAND\b)/i
      ];

      // NoSQL injection patterns
      const nosqlPatterns = [
        /\$where/i, /\$ne/i, /\$gt/i, /\$lt/i, /\$regex/i, /\$or/i, /\$and/i
      ];

      // Command injection patterns
      const cmdPatterns = [
        /[;&|`$(){}[\]]/,
        /(rm|cat|ls|ps|kill|wget|curl|nc|netcat)/i
      ];

      const allPatterns = [...sqlPatterns, ...nosqlPatterns, ...cmdPatterns];

      for (const pattern of allPatterns) {
        if (pattern.test(obj)) {
          logger.security('Potential injection attack detected', {
            path,
            value: obj.substring(0, 100),
            pattern: pattern.toString(),
            ip: req.ip,
            userAgent: req.get('User-Agent')
          });

          return false;
        }
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const [key, value] of Object.entries(obj)) {
        if (!checkForAttacks(value, `${path}.${key}`)) {
          return false;
        }
      }
    }

    return true;
  };

  // Check request body, query, and params
  const inputs = [
    { data: req.body, name: 'body' },
    { data: req.query, name: 'query' },
    { data: req.params, name: 'params' }
  ];

  for (const input of inputs) {
    if (!checkForAttacks(input.data, input.name)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid input detected',
        message_sw: 'Uingizaji usio sahihi umegunduliwa'
      });
    }
  }

  next();
};

/**
 * Rate limiting validation for sensitive operations
 */
const sensitiveOperationValidation = (operation) => {
  return (req, res, next) => {
    logger.security('Sensitive operation attempted', {
      operation,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?._id,
      businessId: req.user?.business_id
    });

    next();
  };
};

module.exports = {
  validate,
  sanitizeInput,
  securityValidation,
  sensitiveOperationValidation,

  // Enhanced validators
  tanzanianPhone,
  enhancedEmail,
  secureString,
  tanzanianAmount,

  schemas: {
    businessRegistration: businessRegistrationSchema,
    userCreation: userCreationSchema,
    product: productSchema,
    sale: saleSchema,
    customer: customerSchema,
    query: querySchemas
  }
};
