/**
 * Validation Middleware using Joi
 * Comprehensive input validation for all API endpoints
 */

const Joi = require('joi');
const logger = require('../config/logger');

/**
 * Tanzanian phone number validation
 */
const tanzanianPhone = Joi.string().pattern(/^(\+255|255|0)?[67]\d{8}$/).messages({
  'string.pattern.base': 'Please enter a valid Tanzanian phone number'
});

/**
 * Business registration validation schema
 */
const businessRegistrationSchema = Joi.object({
  name: Joi.string().trim().min(2).max(100).required().messages({
    'string.empty': 'Business name is required',
    'string.min': 'Business name must be at least 2 characters',
    'string.max': 'Business name cannot exceed 100 characters'
  }),
  
  phone: tanzanianPhone.required().messages({
    'any.required': 'Phone number is required'
  }),
  
  owner_name: Joi.string().trim().min(2).max(100).required().messages({
    'string.empty': 'Owner name is required',
    'string.min': 'Owner name must be at least 2 characters',
    'string.max': 'Owner name cannot exceed 100 characters'
  }),
  
  business_type: Joi.string().valid(
    'duka', 'shop', 'salon', 'restaurant', 'pharmacy', 'electronics',
    'clothing', 'hardware', 'grocery', 'services', 'repair', 'other'
  ).required().messages({
    'any.only': 'Invalid business type',
    'any.required': 'Business type is required'
  }),
  
  location: Joi.object({
    region: Joi.string().trim().max(50).optional(),
    district: Joi.string().trim().max(50).optional(),
    ward: Joi.string().trim().max(50).optional(),
    street: Joi.string().trim().max(100).optional()
  }).optional()
});

/**
 * User creation validation schema
 */
const userCreationSchema = Joi.object({
  name: Joi.string().trim().min(2).max(100).required(),
  phone: tanzanianPhone.required(),
  role: Joi.string().valid('owner', 'manager', 'employee', 'viewer').required(),
  permissions: Joi.object().optional()
});

/**
 * Product validation schema
 */
const productSchema = Joi.object({
  name: Joi.string().trim().min(1).max(100).required().messages({
    'string.empty': 'Product name is required',
    'string.max': 'Product name cannot exceed 100 characters'
  }),
  
  description: Joi.string().trim().max(500).optional().allow(''),
  
  price: Joi.number().min(0).max(*********).required().messages({
    'number.base': 'Price must be a number',
    'number.min': 'Price cannot be negative',
    'number.max': 'Price too large',
    'any.required': 'Price is required'
  }),
  
  cost: Joi.number().min(0).max(*********).optional(),
  
  stock: Joi.number().integer().min(0).when('is_service', {
    is: false,
    then: Joi.required(),
    otherwise: Joi.optional()
  }).messages({
    'number.base': 'Stock must be a number',
    'number.integer': 'Stock must be a whole number',
    'number.min': 'Stock cannot be negative'
  }),
  
  category: Joi.string().valid(
    'food', 'drinks', 'electronics', 'clothing', 'beauty', 'health',
    'household', 'stationery', 'tools', 'automotive', 'books',
    'toys', 'sports', 'services', 'other'
  ).required(),
  
  is_service: Joi.boolean().default(false),
  
  sku: Joi.string().trim().max(50).optional().allow(''),
  
  low_stock_alert: Joi.number().integer().min(0).optional(),
  
  tags: Joi.array().items(Joi.string().trim().max(30)).max(10).optional()
});

/**
 * Sale validation schema
 */
const saleSchema = Joi.object({
  items: Joi.array().items(
    Joi.object({
      product_id: Joi.string().hex().length(24).required(),
      quantity: Joi.number().min(0.01).max(10000).required(),
      unit_price: Joi.number().min(0).max(*********).optional()
    })
  ).min(1).required().messages({
    'array.min': 'At least one item is required'
  }),
  
  customer_id: Joi.string().hex().length(24).optional(),
  
  payment_method: Joi.string().valid(
    'cash', 'mpesa', 'tigo', 'airtel', 'bank', 'credit', 'mixed'
  ).required(),
  
  payment_reference: Joi.string().trim().max(50).optional().allow(''),
  
  discount: Joi.object({
    amount: Joi.number().min(0).optional(),
    percentage: Joi.number().min(0).max(100).optional(),
    reason: Joi.string().trim().max(100).optional().allow('')
  }).optional(),
  
  notes: Joi.string().trim().max(500).optional().allow(''),
  
  customer_info: Joi.object({
    name: Joi.string().trim().max(100).optional().allow(''),
    phone: tanzanianPhone.optional().allow('')
  }).optional()
});

/**
 * Customer validation schema
 */
const customerSchema = Joi.object({
  name: Joi.string().trim().min(1).max(100).required(),
  phone: tanzanianPhone.optional().allow(''),
  email: Joi.string().email().optional().allow(''),
  
  address: Joi.object({
    street: Joi.string().trim().max(200).optional().allow(''),
    ward: Joi.string().trim().max(50).optional().allow(''),
    district: Joi.string().trim().max(50).optional().allow(''),
    region: Joi.string().trim().max(50).optional().allow('')
  }).optional(),
  
  demographics: Joi.object({
    date_of_birth: Joi.date().max('now').optional(),
    gender: Joi.string().valid('male', 'female', 'other', 'prefer_not_to_say').optional(),
    occupation: Joi.string().trim().max(50).optional().allow('')
  }).optional(),
  
  notes: Joi.string().trim().max(500).optional().allow(''),
  tags: Joi.array().items(Joi.string().trim().max(30)).max(10).optional()
});

/**
 * Query parameter validation schemas
 */
const querySchemas = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sort: Joi.string().optional(),
    order: Joi.string().valid('asc', 'desc').default('desc')
  }),
  
  dateRange: Joi.object({
    start_date: Joi.date().optional(),
    end_date: Joi.date().min(Joi.ref('start_date')).optional(),
    period: Joi.string().valid('today', 'yesterday', 'week', 'month', 'year').optional()
  }),
  
  search: Joi.object({
    q: Joi.string().trim().min(1).max(100).optional(),
    category: Joi.string().optional(),
    status: Joi.string().optional()
  })
};

/**
 * Validation middleware factory
 */
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });
    
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        sw: getSwahiliValidationMessage(detail.message)
      }));
      
      logger.warn('Validation failed', {
        errors,
        url: req.originalUrl,
        method: req.method,
        userId: req.user?._id
      });
      
      return res.status(400).json({
        error: 'Validation failed',
        sw: 'Uthibitisho umeshindwa',
        en: 'Validation failed',
        details: errors
      });
    }
    
    // Replace the original data with validated and sanitized data
    req[property] = value;
    next();
  };
};

/**
 * Get Swahili validation messages
 */
const getSwahiliValidationMessage = (message) => {
  const translations = {
    'is required': 'inahitajika',
    'must be a string': 'lazima iwe maandishi',
    'must be a number': 'lazima iwe nambari',
    'must be an integer': 'lazima iwe nambari kamili',
    'cannot be negative': 'haiwezi kuwa hasi',
    'is not allowed to be empty': 'haikubali kuwa tupu',
    'must be at least': 'lazima iwe angalau',
    'must be less than or equal to': 'lazima iwe chini au sawa na',
    'must be greater than or equal to': 'lazima iwe juu au sawa na',
    'must be one of': 'lazima iwe moja ya',
    'Please enter a valid Tanzanian phone number': 'Ingiza nambari sahihi ya simu ya Tanzania',
    'Please enter a valid email address': 'Ingiza anwani sahihi ya barua pepe'
  };
  
  let swahiliMessage = message;
  for (const [english, swahili] of Object.entries(translations)) {
    swahiliMessage = swahiliMessage.replace(new RegExp(english, 'gi'), swahili);
  }
  
  return swahiliMessage;
};

/**
 * Sanitize input to prevent XSS and injection attacks
 */
const sanitizeInput = (req, res, next) => {
  const sanitize = (obj) => {
    if (typeof obj === 'string') {
      return obj.trim()
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitize);
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitize(value);
      }
      return sanitized;
    }
    
    return obj;
  };
  
  req.body = sanitize(req.body);
  req.query = sanitize(req.query);
  req.params = sanitize(req.params);
  
  next();
};

module.exports = {
  validate,
  sanitizeInput,
  schemas: {
    businessRegistration: businessRegistrationSchema,
    userCreation: userCreationSchema,
    product: productSchema,
    sale: saleSchema,
    customer: customerSchema,
    query: querySchemas
  }
};
