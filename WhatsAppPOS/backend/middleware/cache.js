/**
 * Cache Middleware
 * Provides caching functionality for API endpoints
 */

const cacheService = require('../services/cacheService');
const logger = require('../config/logger');

/**
 * Cache middleware factory
 * Creates middleware for caching API responses
 */
function createCacheMiddleware(options = {}) {
  const {
    ttl = 300, // 5 minutes default
    keyGenerator = null,
    skipCache = null,
    onHit = null,
    onMiss = null,
    onError = null
  } = options;

  return async (req, res, next) => {
    // Skip caching if not available or conditions not met
    if (!cacheService.isAvailable()) {
      return next();
    }

    // Check if we should skip cache for this request
    if (skipCache && skipCache(req)) {
      return next();
    }

    // Get business ID for cache isolation
    const businessId = req.user?.business_id || req.business?._id;
    if (!businessId) {
      return next(); // No business context, skip caching
    }

    try {
      // Generate cache key
      const cacheKey = keyGenerator 
        ? keyGenerator(req) 
        : generateDefaultCacheKey(req);

      // Try to get from cache
      const cachedResponse = await cacheService.get(businessId, cacheKey);
      
      if (cachedResponse) {
        // Cache hit
        if (onHit) onHit(req, cacheKey);
        
        logger.debug('Cache hit for API request', {
          businessId,
          method: req.method,
          url: req.originalUrl,
          cacheKey
        });

        // Set cache headers
        res.set({
          'X-Cache': 'HIT',
          'X-Cache-Key': cacheKey
        });

        return res.json(cachedResponse);
      }

      // Cache miss - intercept response to cache it
      if (onMiss) onMiss(req, cacheKey);
      
      logger.debug('Cache miss for API request', {
        businessId,
        method: req.method,
        url: req.originalUrl,
        cacheKey
      });

      // Override res.json to cache the response
      const originalJson = res.json;
      res.json = function(data) {
        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // Cache the response asynchronously
          cacheService.set(businessId, cacheKey, data, ttl)
            .catch(error => {
              logger.error('Failed to cache response:', {
                businessId,
                cacheKey,
                error: error.message
              });
              if (onError) onError(error, req, cacheKey);
            });
        }

        // Set cache headers
        res.set({
          'X-Cache': 'MISS',
          'X-Cache-Key': cacheKey
        });

        return originalJson.call(this, data);
      };

      next();

    } catch (error) {
      logger.error('Cache middleware error:', {
        businessId,
        method: req.method,
        url: req.originalUrl,
        error: error.message
      });
      
      if (onError) onError(error, req);
      
      // Continue without caching on error
      next();
    }
  };
}

/**
 * Generate default cache key from request
 */
function generateDefaultCacheKey(req) {
  const method = req.method;
  const path = req.route?.path || req.path;
  const query = JSON.stringify(req.query);
  const params = JSON.stringify(req.params);
  
  return `api:${method}:${path}:${Buffer.from(query + params).toString('base64')}`;
}

/**
 * Cache invalidation middleware
 * Invalidates cache when data is modified
 */
function createCacheInvalidationMiddleware(patterns = []) {
  return async (req, res, next) => {
    // Store original response methods
    const originalJson = res.json;
    const originalSend = res.send;

    // Override response methods to invalidate cache after successful operations
    const invalidateCache = async () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const businessId = req.user?.business_id || req.business?._id;
        
        if (businessId && patterns.length > 0) {
          try {
            for (const pattern of patterns) {
              const cachePattern = typeof pattern === 'function' 
                ? pattern(req) 
                : pattern;
              
              await cacheService.deletePattern(businessId, cachePattern);
              
              logger.debug('Cache invalidated', {
                businessId,
                pattern: cachePattern,
                method: req.method,
                url: req.originalUrl
              });
            }
          } catch (error) {
            logger.error('Cache invalidation failed:', {
              businessId,
              patterns,
              error: error.message
            });
          }
        }
      }
    };

    res.json = function(data) {
      invalidateCache();
      return originalJson.call(this, data);
    };

    res.send = function(data) {
      invalidateCache();
      return originalSend.call(this, data);
    };

    next();
  };
}

/**
 * Predefined cache configurations for common endpoints
 */
const cacheConfigs = {
  // Products cache - 10 minutes
  products: createCacheMiddleware({
    ttl: 600,
    keyGenerator: (req) => `products:${req.method}:${JSON.stringify(req.query)}`
  }),

  // Dashboard cache - 5 minutes
  dashboard: createCacheMiddleware({
    ttl: 300,
    keyGenerator: (req) => `dashboard:${req.method}:${req.originalUrl}`
  }),

  // Reports cache - 15 minutes
  reports: createCacheMiddleware({
    ttl: 900,
    keyGenerator: (req) => `reports:${req.method}:${JSON.stringify(req.query)}`
  }),

  // Customers cache - 10 minutes
  customers: createCacheMiddleware({
    ttl: 600,
    keyGenerator: (req) => `customers:${req.method}:${JSON.stringify(req.query)}`
  }),

  // Business settings cache - 30 minutes
  settings: createCacheMiddleware({
    ttl: 1800,
    keyGenerator: (req) => `settings:${req.method}:${req.originalUrl}`
  })
};

/**
 * Predefined cache invalidation patterns
 */
const invalidationPatterns = {
  // Invalidate product caches when products are modified
  products: createCacheInvalidationMiddleware([
    'products:*',
    'dashboard:*', // Dashboard might show product stats
    'reports:*'    // Reports might include product data
  ]),

  // Invalidate sales caches when sales are modified
  sales: createCacheInvalidationMiddleware([
    'sales:*',
    'dashboard:*',
    'reports:*',
    'customers:*' // Customer data might include purchase history
  ]),

  // Invalidate customer caches when customers are modified
  customers: createCacheInvalidationMiddleware([
    'customers:*',
    'dashboard:*',
    'reports:*'
  ]),

  // Invalidate all caches when business settings change
  settings: createCacheInvalidationMiddleware([
    '*' // Clear all cache when settings change
  ])
};

/**
 * Cache warming utility
 * Pre-populate cache with frequently accessed data
 */
async function warmCache(businessId, endpoints = []) {
  if (!cacheService.isAvailable()) {
    return;
  }

  logger.info('Starting cache warming', { businessId, endpoints: endpoints.length });

  for (const endpoint of endpoints) {
    try {
      const { key, fetchFunction, ttl = 300 } = endpoint;
      const data = await fetchFunction();
      await cacheService.set(businessId, key, data, ttl);
      
      logger.debug('Cache warmed', { businessId, key });
    } catch (error) {
      logger.error('Cache warming failed for endpoint:', {
        businessId,
        key: endpoint.key,
        error: error.message
      });
    }
  }

  logger.info('Cache warming completed', { businessId });
}

/**
 * Cache statistics middleware
 * Adds cache statistics to response headers
 */
function cacheStatsMiddleware(req, res, next) {
  if (cacheService.isAvailable()) {
    res.set('X-Cache-Enabled', 'true');
  } else {
    res.set('X-Cache-Enabled', 'false');
  }
  
  next();
}

module.exports = {
  createCacheMiddleware,
  createCacheInvalidationMiddleware,
  generateDefaultCacheKey,
  cacheConfigs,
  invalidationPatterns,
  warmCache,
  cacheStatsMiddleware
};
