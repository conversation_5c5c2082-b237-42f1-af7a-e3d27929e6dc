/**
 * Production Monitoring Middleware for WhatsApp POS Tanzania
 * Comprehensive system health monitoring and performance tracking
 */

const logger = require('../config/logger');

// Performance metrics storage
const metrics = {
  requests: {
    total: 0,
    successful: 0,
    failed: 0,
    averageResponseTime: 0
  },
  whatsapp: {
    messagesReceived: 0,
    messagesSent: 0,
    messagesFailed: 0,
    averageProcessingTime: 0
  },
  database: {
    queries: 0,
    slowQueries: 0,
    errors: 0,
    averageQueryTime: 0
  },
  system: {
    uptime: process.uptime(),
    memoryUsage: process.memoryUsage(),
    cpuUsage: process.cpuUsage()
  }
};

// Performance tracking
const performanceTracker = {
  requestTimes: [],
  whatsappTimes: [],
  dbTimes: []
};

/**
 * Request monitoring middleware
 */
function requestMonitoring(req, res, next) {
  const startTime = Date.now();
  
  // Track request
  metrics.requests.total++;
  
  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    
    // Update metrics
    if (res.statusCode < 400) {
      metrics.requests.successful++;
    } else {
      metrics.requests.failed++;
    }
    
    // Track response times (keep last 100)
    performanceTracker.requestTimes.push(responseTime);
    if (performanceTracker.requestTimes.length > 100) {
      performanceTracker.requestTimes.shift();
    }
    
    // Calculate average response time
    metrics.requests.averageResponseTime = 
      performanceTracker.requestTimes.reduce((a, b) => a + b, 0) / 
      performanceTracker.requestTimes.length;
    
    // Log slow requests
    if (responseTime > 1000) {
      logger.warn('Slow request detected', {
        method: req.method,
        url: req.url,
        responseTime,
        statusCode: res.statusCode
      });
    }
    
    originalEnd.apply(this, args);
  };
  
  next();
}

/**
 * WhatsApp message monitoring
 */
function trackWhatsAppMessage(type, processingTime, success = true) {
  if (type === 'received') {
    metrics.whatsapp.messagesReceived++;
  } else if (type === 'sent') {
    if (success) {
      metrics.whatsapp.messagesSent++;
    } else {
      metrics.whatsapp.messagesFailed++;
    }
  }
  
  if (processingTime) {
    performanceTracker.whatsappTimes.push(processingTime);
    if (performanceTracker.whatsappTimes.length > 100) {
      performanceTracker.whatsappTimes.shift();
    }
    
    metrics.whatsapp.averageProcessingTime = 
      performanceTracker.whatsappTimes.reduce((a, b) => a + b, 0) / 
      performanceTracker.whatsappTimes.length;
  }
}

/**
 * Database query monitoring
 */
function trackDatabaseQuery(queryTime, isError = false) {
  metrics.database.queries++;
  
  if (isError) {
    metrics.database.errors++;
  }
  
  if (queryTime > 1000) {
    metrics.database.slowQueries++;
    logger.warn('Slow database query detected', { queryTime });
  }
  
  performanceTracker.dbTimes.push(queryTime);
  if (performanceTracker.dbTimes.length > 100) {
    performanceTracker.dbTimes.shift();
  }
  
  metrics.database.averageQueryTime = 
    performanceTracker.dbTimes.reduce((a, b) => a + b, 0) / 
    performanceTracker.dbTimes.length;
}

/**
 * Get current system metrics
 */
function getMetrics() {
  // Update system metrics
  metrics.system.uptime = process.uptime();
  metrics.system.memoryUsage = process.memoryUsage();
  metrics.system.cpuUsage = process.cpuUsage();
  
  // Calculate success rates
  const requestSuccessRate = metrics.requests.total > 0 ? 
    (metrics.requests.successful / metrics.requests.total * 100).toFixed(2) : 0;
  
  const whatsappSuccessRate = (metrics.whatsapp.messagesSent + metrics.whatsapp.messagesFailed) > 0 ?
    (metrics.whatsapp.messagesSent / (metrics.whatsapp.messagesSent + metrics.whatsapp.messagesFailed) * 100).toFixed(2) : 0;
  
  return {
    ...metrics,
    calculated: {
      requestSuccessRate: `${requestSuccessRate}%`,
      whatsappSuccessRate: `${whatsappSuccessRate}%`,
      errorRate: `${((metrics.requests.failed / metrics.requests.total) * 100 || 0).toFixed(2)}%`,
      memoryUsageMB: Math.round(metrics.system.memoryUsage.heapUsed / 1024 / 1024),
      uptimeHours: (metrics.system.uptime / 3600).toFixed(2)
    }
  };
}

/**
 * Health check with detailed status
 */
function getHealthStatus() {
  const currentMetrics = getMetrics();
  
  // Determine health status
  const isHealthy = 
    currentMetrics.calculated.requestSuccessRate > 95 &&
    currentMetrics.calculated.whatsappSuccessRate > 95 &&
    currentMetrics.requests.averageResponseTime < 1000 &&
    currentMetrics.system.memoryUsage.heapUsed < 500 * 1024 * 1024; // 500MB
  
  return {
    status: isHealthy ? 'healthy' : 'degraded',
    timestamp: new Date().toISOString(),
    metrics: currentMetrics,
    alerts: generateAlerts(currentMetrics)
  };
}

/**
 * Generate alerts based on metrics
 */
function generateAlerts(metrics) {
  const alerts = [];
  
  if (parseFloat(metrics.calculated.whatsappSuccessRate) < 95) {
    alerts.push({
      level: 'critical',
      message: `WhatsApp success rate is ${metrics.calculated.whatsappSuccessRate} (below 95%)`,
      metric: 'whatsapp_success_rate'
    });
  }
  
  if (metrics.requests.averageResponseTime > 1000) {
    alerts.push({
      level: 'warning',
      message: `Average response time is ${metrics.requests.averageResponseTime}ms (above 1000ms)`,
      metric: 'response_time'
    });
  }
  
  if (metrics.calculated.memoryUsageMB > 400) {
    alerts.push({
      level: 'warning',
      message: `Memory usage is ${metrics.calculated.memoryUsageMB}MB (above 400MB)`,
      metric: 'memory_usage'
    });
  }
  
  if (metrics.database.slowQueries > 10) {
    alerts.push({
      level: 'warning',
      message: `${metrics.database.slowQueries} slow database queries detected`,
      metric: 'database_performance'
    });
  }
  
  return alerts;
}

module.exports = {
  requestMonitoring,
  trackWhatsAppMessage,
  trackDatabaseQuery,
  getMetrics,
  getHealthStatus
};
