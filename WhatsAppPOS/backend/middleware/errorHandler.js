/**
 * Global Error Handler Middleware
 * Comprehensive error handling with proper logging and user-friendly messages
 */

const logger = require('../config/logger');

/**
 * Development error response
 */
const sendErrorDev = (err, res) => {
  res.status(err.statusCode).json({
    status: err.status,
    error: err,
    message: err.message,
    stack: err.stack,
    sw: err.swahili || 'Kuna tatizo la kiufundi',
    en: err.message
  });
};

/**
 * Production error response
 */
const sendErrorProd = (err, res) => {
  // Operational, trusted error: send message to client
  if (err.isOperational) {
    res.status(err.statusCode).json({
      status: err.status,
      message: err.message,
      sw: err.swahili || getSwahiliError(err.message),
      en: err.message
    });
  } else {
    // Programming or other unknown error: don't leak error details
    logger.error('Unknown error:', err);
    
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong!',
      sw: 'Kuna tatizo! Jaribu tena baadaye.',
      en: 'Something went wrong!'
    });
  }
};

/**
 * Handle MongoDB cast errors
 */
const handleCastErrorDB = (err) => {
  const message = `Invalid ${err.path}: ${err.value}`;
  const swahili = `Thamani si sahihi: ${err.value}`;
  return createError(message, 400, swahili);
};

/**
 * Handle MongoDB duplicate field errors
 */
const handleDuplicateFieldsDB = (err) => {
  const value = err.errmsg.match(/(["'])(\\?.)*?\1/)[0];
  const message = `Duplicate field value: ${value}. Please use another value!`;
  const swahili = `Thamani imeshatumika: ${value}. Tumia thamani nyingine!`;
  return createError(message, 400, swahili);
};

/**
 * Handle MongoDB validation errors
 */
const handleValidationErrorDB = (err) => {
  const errors = Object.values(err.errors).map(el => el.message);
  const message = `Invalid input data. ${errors.join('. ')}`;
  const swahili = `Data si sahihi. ${errors.join('. ')}`;
  return createError(message, 400, swahili);
};

/**
 * Handle JWT errors
 */
const handleJWTError = () => {
  const message = 'Invalid token. Please log in again!';
  const swahili = 'Tokeni si sahihi. Ingia tena!';
  return createError(message, 401, swahili);
};

/**
 * Handle JWT expired errors
 */
const handleJWTExpiredError = () => {
  const message = 'Your token has expired! Please log in again.';
  const swahili = 'Tokeni yako imekwisha! Ingia tena.';
  return createError(message, 401, swahili);
};

/**
 * Handle Twilio errors
 */
const handleTwilioError = (err) => {
  let message = 'WhatsApp service error';
  let swahili = 'Tatizo la huduma ya WhatsApp';
  
  switch (err.code) {
    case 21211:
      message = 'Invalid phone number';
      swahili = 'Nambari ya simu si sahihi';
      break;
    case 21614:
      message = 'Phone number not verified with WhatsApp';
      swahili = 'Nambari ya simu haijathibitishwa na WhatsApp';
      break;
    case 63007:
      message = 'Message could not be delivered';
      swahili = 'Ujumbe haukuweza kutumwa';
      break;
    default:
      message = `WhatsApp error: ${err.message}`;
      swahili = `Tatizo la WhatsApp: ${err.message}`;
  }
  
  return createError(message, 400, swahili);
};

/**
 * Handle rate limiting errors
 */
const handleRateLimitError = () => {
  const message = 'Too many requests. Please try again later.';
  const swahili = 'Maombi mengi sana. Jaribu tena baadaye.';
  return createError(message, 429, swahili);
};

/**
 * Create error object
 */
const createError = (message, statusCode, swahili = null) => {
  const error = new Error(message);
  error.statusCode = statusCode;
  error.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
  error.isOperational = true;
  error.swahili = swahili;
  return error;
};

/**
 * Get Swahili translation for common errors
 */
const getSwahiliError = (message) => {
  const translations = {
    'Validation failed': 'Uthibitisho umeshindwa',
    'Not found': 'Haijapatikana',
    'Unauthorized': 'Hauruhusiwi',
    'Forbidden': 'Hauruhusiwi',
    'Bad request': 'Ombi si sahihi',
    'Internal server error': 'Tatizo la seva',
    'Service unavailable': 'Huduma haipatikani',
    'Network error': 'Tatizo la mtandao',
    'Database error': 'Tatizo la hifadhidata',
    'Authentication failed': 'Uthibitisho umeshindwa',
    'Permission denied': 'Ruhusa imekataliwa',
    'Invalid input': 'Ingizo si sahihi',
    'Resource not found': 'Rasilimali haijapatikana',
    'Duplicate entry': 'Ingizo limeshajibu',
    'Invalid phone number': 'Nambari ya simu si sahihi',
    'Insufficient stock': 'Hisa haitoshi',
    'Payment failed': 'Malipo yameshindwa',
    'Subscription expired': 'Usajili umekwisha'
  };
  
  // Find matching translation
  for (const [english, swahili] of Object.entries(translations)) {
    if (message.toLowerCase().includes(english.toLowerCase())) {
      return swahili;
    }
  }
  
  return 'Kuna tatizo. Jaribu tena.';
};

/**
 * Main error handling middleware
 */
const errorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';
  
  // Log error details
  logger.error('Error occurred:', {
    message: err.message,
    statusCode: err.statusCode,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?._id,
    businessId: req.business?._id
  });
  
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(err, res);
  } else {
    let error = { ...err };
    error.message = err.message;
    
    // Handle specific error types
    if (error.name === 'CastError') error = handleCastErrorDB(error);
    if (error.code === 11000) error = handleDuplicateFieldsDB(error);
    if (error.name === 'ValidationError') error = handleValidationErrorDB(error);
    if (error.name === 'JsonWebTokenError') error = handleJWTError();
    if (error.name === 'TokenExpiredError') error = handleJWTExpiredError();
    if (error.code && error.code.toString().startsWith('2')) error = handleTwilioError(error);
    if (error.statusCode === 429) error = handleRateLimitError();
    
    sendErrorProd(error, res);
  }
};

/**
 * Handle 404 errors
 */
const notFound = (req, res, next) => {
  const message = `Can't find ${req.originalUrl} on this server!`;
  const swahili = `Haikupatikana ${req.originalUrl} kwenye seva hii!`;
  const error = createError(message, 404, swahili);
  next(error);
};

/**
 * Async error wrapper
 */
const catchAsync = (fn) => {
  return (req, res, next) => {
    fn(req, res, next).catch(next);
  };
};

/**
 * Create application error
 */
class AppError extends Error {
  constructor(message, statusCode, swahili = null) {
    super(message);
    
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.swahili = swahili || getSwahiliError(message);
    
    Error.captureStackTrace(this, this.constructor);
  }
}

module.exports = {
  errorHandler,
  notFound,
  catchAsync,
  AppError,
  createError
};
