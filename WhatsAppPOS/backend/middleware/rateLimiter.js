/**
 * Rate Limiting Middleware - Minimal Version for Testing
 */

const rateLimit = require("express-rate-limit");

// Create a simple rate limiter function
const createLimiter = (windowMs = 15 * 60 * 1000, max = 100) => rateLimit({
  windowMs,
  max,
  message: {
    error: "Rate limit exceeded",
    sw: "<PERSON><PERSON>ita kikomo cha maombi. <PERSON><PERSON>bu tena baadaye.",
    en: "Rate limit exceeded. Please try again later."
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => req.path === "/health"
});

// Export all required limiters
module.exports = {
  generalLimiter: createLimiter(),
  whatsappLimiter: createLimiter(60 * 1000, 30),
  authLimiter: createLimiter(15 * 60 * 1000, 5),
  registrationLimiter: createLimiter(60 * 60 * 1000, 3),
  salesLimiter: createLimiter(60 * 1000, 20),
  productLimiter: createLimiter(60 * 1000, 15),
  customerLimiter: createLimiter(60 * 1000, 10),
  reportsLimiter: createLimiter(60 * 1000, 5),
  reportLimiter: createLimiter(60 * 1000, 5),
  subscriptionBasedLimiter: () => createLimiter()
};
