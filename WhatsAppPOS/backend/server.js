#!/usr/bin/env node

/**
 * WhatsApp POS Tanzania - Main Server Entry Point
 * Production-ready server with comprehensive error handling and monitoring
 */

require('dotenv').config();

// Initialize monitoring first (before other imports)
const { initializeSentry } = require('./config/monitoring');
initializeSentry();

const app = require('./app');
const logger = require('./config/logger');
const { initializeEnvironment } = require('./config/environment');
const { connectDB } = require('./config/database');
const { connectRedis } = require('./config/redis');
const { createHTTPSServer, checkSSLHealth } = require('./config/ssl');
const BackupService = require('./services/backupService');

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Rejection:', err);
  process.exit(1);
});

// Graceful shutdown handler
const gracefulShutdown = (signal) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  server.close(() => {
    logger.info('HTTP server closed.');

    // Close database connection
    require('mongoose').connection.close(() => {
      logger.info('MongoDB connection closed.');
      process.exit(0);
    });
  });

  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

// Listen for termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server function
async function startServer() {
  try {
    logger.info('Starting server initialization...');

    // Validate environment configuration
    const envResult = initializeEnvironment();
    if (!envResult.valid && process.env.NODE_ENV === 'production') {
      throw new Error('Environment validation failed');
    }

    // Connect to MongoDB
    logger.info('Connecting to MongoDB...');
    await connectDB();
    logger.info('MongoDB connected successfully');

    // Connect to Redis (temporarily disabled for testing)
    // await connectRedis();
    // logger.info('Redis connected successfully');
    logger.info('Redis connection skipped for testing');

    // Initialize backup service
    if (process.env.BACKUP_ENABLED === 'true') {
      const backupService = new BackupService();
      await backupService.initialize();
      global.backupService = backupService; // Make available globally
    }

    // Check SSL configuration
    const sslHealth = checkSSLHealth();
    logger.info('SSL Health Check:', sslHealth);

    // Start HTTP server
    const PORT = process.env.PORT || 3000;
    const server = app.listen(PORT, () => {
      logger.info(`🚀 WhatsApp POS Tanzania server running on port ${PORT}`);
      logger.info(`📱 Environment: ${process.env.NODE_ENV}`);
      logger.info(`🌍 Timezone: ${process.env.DEFAULT_TIMEZONE || 'Africa/Dar_es_Salaam'}`);
      logger.info(`💰 Currency: ${process.env.DEFAULT_CURRENCY || 'TZS'}`);
      logger.info(`🗣️  Default Language: ${process.env.DEFAULT_LANGUAGE || 'sw'}`);

      if (process.env.NODE_ENV === 'development') {
        logger.info(`📊 Dashboard: ${process.env.FRONTEND_URL || 'http://localhost:3001'}`);
        logger.info(`🔗 Webhook URL: ${process.env.WEBHOOK_URL || 'Not configured'}`);
      }
    });

    // Set server timeout
    server.timeout = 30000; // 30 seconds

    // Start HTTPS server if SSL is configured
    const HTTPS_PORT = process.env.HTTPS_PORT || 443;
    const httpsServer = createHTTPSServer(app);

    if (httpsServer) {
      httpsServer.listen(HTTPS_PORT, () => {
        logger.info(`🔒 WhatsApp POS Tanzania HTTPS server running on port ${HTTPS_PORT}`);
        logger.info(`🔐 Secure Dashboard: https://localhost:${HTTPS_PORT}`);
      });

      // Set HTTPS server timeout
      httpsServer.timeout = 30000;
    }

    // Make server globally accessible for graceful shutdown
    global.server = server;

    return server;

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
if (require.main === module) {
  startServer();
}

module.exports = startServer;
