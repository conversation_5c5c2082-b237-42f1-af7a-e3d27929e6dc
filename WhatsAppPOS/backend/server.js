#!/usr/bin/env node

/**
 * WhatsApp POS Tanzania - Main Server Entry Point
 * Production-ready server with comprehensive error handling and monitoring
 */

require('dotenv').config();
const app = require('./app');
const logger = require('./config/logger');
const connectDB = require('./config/database');
const connectRedis = require('./config/redis');

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Rejection:', err);
  process.exit(1);
});

// Graceful shutdown handler
const gracefulShutdown = (signal) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  server.close(() => {
    logger.info('HTTP server closed.');

    // Close database connection
    require('mongoose').connection.close(() => {
      logger.info('MongoDB connection closed.');
      process.exit(0);
    });
  });

  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

// Listen for termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server function
async function startServer() {
  try {
    logger.info('Starting server initialization...');

    // Connect to MongoDB
    logger.info('Connecting to MongoDB...');
    await connectDB();
    logger.info('MongoDB connected successfully');

    // Connect to Redis (temporarily disabled for testing)
    // await connectRedis();
    // logger.info('Redis connected successfully');
    logger.info('Redis connection skipped for testing');

    // Start HTTP server
    const PORT = process.env.PORT || 3000;
    const server = app.listen(PORT, () => {
      logger.info(`🚀 WhatsApp POS Tanzania server running on port ${PORT}`);
      logger.info(`📱 Environment: ${process.env.NODE_ENV}`);
      logger.info(`🌍 Timezone: ${process.env.DEFAULT_TIMEZONE || 'Africa/Dar_es_Salaam'}`);
      logger.info(`💰 Currency: ${process.env.DEFAULT_CURRENCY || 'TZS'}`);
      logger.info(`🗣️  Default Language: ${process.env.DEFAULT_LANGUAGE || 'sw'}`);

      if (process.env.NODE_ENV === 'development') {
        logger.info(`📊 Dashboard: ${process.env.FRONTEND_URL || 'http://localhost:3001'}`);
        logger.info(`🔗 Webhook URL: ${process.env.WEBHOOK_URL || 'Not configured'}`);
      }
    });

    // Set server timeout
    server.timeout = 30000; // 30 seconds

    // Make server globally accessible for graceful shutdown
    global.server = server;

    return server;

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
if (require.main === module) {
  startServer();
}

module.exports = startServer;
