/**
 * WhatsApp POS Tanzania - Express Application Configuration
 * Complete middleware setup with security, validation, and error handling
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const path = require('path');

const logger = require('./config/logger');
const { errorHandler } = require('./middleware/errorHandler');
const { httpsRedirect, securityHeaders, secureCookies, securityMonitoring } = require('./middleware/security');
// const { requestMonitoringMiddleware, Sentry } = require('./config/monitoring');
// const { securityValidation, sanitizeInput } = require('./middleware/validation');

// Import routes
const authRoutes = require('./routes/auth');
const businessRoutes = require('./routes/businesses');
const productRoutes = require('./routes/products');
const salesRoutes = require('./routes/sales');
const customerRoutes = require('./routes/customers');
const reportRoutes = require('./routes/reports');
const userRoutes = require('./routes/users');
const webhookRoutes = require('./routes/webhook');
// const backupRoutes = require('./routes/backup');
// const monitoringRoutes = require('./routes/monitoring');

const app = express();

// Trust proxy for accurate IP addresses (important for Railway/Heroku)
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"]
    }
  },
  crossOriginEmbedderPolicy: false
}));

// Compression middleware
app.use(compression());

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      process.env.FRONTEND_URL,
      process.env.DASHBOARD_URL,
      'http://localhost:3001',
      'http://localhost:3000'
    ].filter(Boolean);
    
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

// Sentry request handler (must be first)
// if (Sentry.getCurrentHub().getClient()) {
//   app.use(Sentry.Handlers.requestHandler());
//   app.use(Sentry.Handlers.tracingHandler());
// }

// Security middleware (must be early in the stack)
app.use(httpsRedirect);
app.use(securityHeaders);
app.use(secureCookies);
app.use(securityMonitoring);

// Performance monitoring
// app.use(requestMonitoringMiddleware);

// Enhanced input validation and sanitization
// app.use(sanitizeInput);
// app.use(securityValidation);

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    sw: 'Maombi mengi sana kutoka IP hii, jaribu tena baadaye.',
    en: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for webhook endpoints
    return req.path.startsWith('/webhook/');
  }
});

app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// Health check endpoint
app.get('/health', (req, res) => {
  const { checkSSLHealth } = require('./config/ssl');
  // const { getMonitoringStatus } = require('./config/monitoring');

  const sslStatus = checkSSLHealth();
  // const monitoringStatus = getMonitoringStatus();

  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: require('../package.json').version,
    uptime: process.uptime(),
    ssl: sslStatus,
    // monitoring: monitoringStatus,
    security: {
      https_enabled: process.env.NODE_ENV === 'production',
      secure_headers: true,
      cors_configured: true
    }
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/businesses', businessRoutes);
app.use('/api/products', productRoutes);
app.use('/api/sales', salesRoutes);
app.use('/api/customers', customerRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/users', userRoutes);
// app.use('/api/backup', backupRoutes);
// app.use('/api/monitoring', monitoringRoutes);

// Webhook routes (no auth required)
app.use('/webhook', webhookRoutes);

// Serve static files from dashboard
app.use(express.static(path.join(__dirname, '../dashboard/public')));

// Serve dashboard for all non-API routes
app.get('*', (req, res) => {
  if (!req.path.startsWith('/api/') && !req.path.startsWith('/webhook/')) {
    res.sendFile(path.join(__dirname, '../dashboard/public/index.html'));
  } else {
    res.status(404).json({
      error: 'Endpoint not found',
      sw: 'Njia haijapatikana',
      en: 'Endpoint not found'
    });
  }
});

// Sentry error handler (must be before other error handlers)
// if (Sentry.getCurrentHub().getClient()) {
//   app.use(Sentry.Handlers.errorHandler());
// }

// Error handling middleware (must be last)
app.use(errorHandler);

module.exports = app;
