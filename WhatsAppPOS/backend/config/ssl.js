/**
 * SSL/TLS Configuration for Production HTTPS
 * Handles certificate loading and HTTPS server setup
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const logger = require('./logger');

/**
 * Load SSL certificates
 */
function loadSSLCertificates() {
  try {
    const certPath = process.env.SSL_CERT_PATH || '/etc/ssl/certs/whatsapp-pos.crt';
    const keyPath = process.env.SSL_KEY_PATH || '/etc/ssl/private/whatsapp-pos.key';
    const caPath = process.env.SSL_CA_PATH; // Optional CA bundle

    // Check if certificate files exist
    if (!fs.existsSync(certPath) || !fs.existsSync(keyPath)) {
      logger.warn('SSL certificates not found, HTTPS will not be available', {
        certPath,
        keyPath
      });
      return null;
    }

    const sslOptions = {
      cert: fs.readFileSync(certPath, 'utf8'),
      key: fs.readFileSync(keyPath, 'utf8')
    };

    // Add CA bundle if provided
    if (caPath && fs.existsSync(caPath)) {
      sslOptions.ca = fs.readFileSync(caPath, 'utf8');
    }

    // SSL/TLS security options
    sslOptions.secureProtocol = 'TLSv1_2_method';
    sslOptions.ciphers = [
      'ECDHE-RSA-AES128-GCM-SHA256',
      'ECDHE-RSA-AES256-GCM-SHA384',
      'ECDHE-RSA-AES128-SHA256',
      'ECDHE-RSA-AES256-SHA384'
    ].join(':');
    sslOptions.honorCipherOrder = true;

    logger.info('SSL certificates loaded successfully', {
      certPath,
      keyPath,
      hasCa: !!sslOptions.ca
    });

    return sslOptions;

  } catch (error) {
    logger.error('Failed to load SSL certificates:', error);
    return null;
  }
}

/**
 * Create HTTPS server
 */
function createHTTPSServer(app) {
  const sslOptions = loadSSLCertificates();
  
  if (!sslOptions) {
    logger.warn('HTTPS server not created - SSL certificates not available');
    return null;
  }

  try {
    const httpsServer = https.createServer(sslOptions, app);
    
    httpsServer.on('error', (error) => {
      logger.error('HTTPS server error:', error);
    });

    httpsServer.on('listening', () => {
      const address = httpsServer.address();
      logger.info(`🔒 HTTPS server running on port ${address.port}`);
    });

    return httpsServer;

  } catch (error) {
    logger.error('Failed to create HTTPS server:', error);
    return null;
  }
}

/**
 * Generate self-signed certificate for development
 */
function generateSelfSignedCert() {
  const { execSync } = require('child_process');
  
  try {
    const certDir = path.join(__dirname, '../../ssl');
    
    // Create SSL directory if it doesn't exist
    if (!fs.existsSync(certDir)) {
      fs.mkdirSync(certDir, { recursive: true });
    }

    const certPath = path.join(certDir, 'localhost.crt');
    const keyPath = path.join(certDir, 'localhost.key');

    // Generate self-signed certificate
    const opensslCmd = `openssl req -x509 -newkey rsa:4096 -keyout ${keyPath} -out ${certPath} -days 365 -nodes -subj "/C=TZ/ST=Dar es Salaam/L=Dar es Salaam/O=WhatsApp POS Tanzania/CN=localhost"`;
    
    execSync(opensslCmd, { stdio: 'inherit' });

    logger.info('Self-signed certificate generated for development', {
      certPath,
      keyPath
    });

    return { certPath, keyPath };

  } catch (error) {
    logger.error('Failed to generate self-signed certificate:', error);
    return null;
  }
}

/**
 * Setup SSL configuration based on environment
 */
function setupSSL() {
  if (process.env.NODE_ENV === 'production') {
    // Production: Use real certificates
    return loadSSLCertificates();
  } else if (process.env.ENABLE_HTTPS_DEV === 'true') {
    // Development: Generate self-signed certificate if needed
    const sslOptions = loadSSLCertificates();
    if (!sslOptions) {
      const certInfo = generateSelfSignedCert();
      if (certInfo) {
        process.env.SSL_CERT_PATH = certInfo.certPath;
        process.env.SSL_KEY_PATH = certInfo.keyPath;
        return loadSSLCertificates();
      }
    }
    return sslOptions;
  }
  
  return null;
}

/**
 * SSL health check
 */
function checkSSLHealth() {
  const sslOptions = loadSSLCertificates();
  
  if (!sslOptions) {
    return {
      status: 'disabled',
      message: 'SSL certificates not configured'
    };
  }

  try {
    // Basic certificate validation
    const cert = sslOptions.cert;
    const certStart = cert.indexOf('-----BEGIN CERTIFICATE-----');
    const certEnd = cert.indexOf('-----END CERTIFICATE-----');
    
    if (certStart === -1 || certEnd === -1) {
      throw new Error('Invalid certificate format');
    }

    return {
      status: 'healthy',
      message: 'SSL certificates loaded and valid',
      hasCA: !!sslOptions.ca
    };

  } catch (error) {
    return {
      status: 'error',
      message: error.message
    };
  }
}

module.exports = {
  loadSSLCertificates,
  createHTTPSServer,
  generateSelfSignedCert,
  setupSSL,
  checkSSLHealth
};
