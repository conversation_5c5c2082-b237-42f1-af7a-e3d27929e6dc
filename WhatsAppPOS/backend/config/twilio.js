/**
 * Twilio WhatsApp Configuration
 * Production-ready setup with webhook verification and message handling
 */

const twilio = require('twilio');
const logger = require('./logger');

// Validate required environment variables
const requiredEnvVars = [
  'TWILIO_ACCOUNT_SID',
  'TWILIO_AUTH_TOKEN',
  'TWILIO_WHATSAPP_NUMBER'
];

// Check for required environment variables (optional in development)
const missingVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingVars.length > 0) {
  if (process.env.NODE_ENV === 'production') {
    logger.error(`Missing required environment variables: ${missingVars.join(', ')}`);
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  } else {
    logger.warn(`Missing Twilio environment variables (development mode): ${missingVars.join(', ')}`);
    logger.warn('Twilio functionality will be disabled');
  }
}

// Initialize Twilio client (only if credentials are available)
let client = null;
if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
  client = twilio(
    process.env.TWILIO_ACCOUNT_SID,
    process.env.TWILIO_AUTH_TOKEN
  );
  logger.info('Twilio WhatsApp configured');
} else {
  logger.warn('Twilio client not initialized - missing credentials');
}

// Twilio configuration
const twilioConfig = {
  accountSid: process.env.TWILIO_ACCOUNT_SID,
  authToken: process.env.TWILIO_AUTH_TOKEN,
  whatsappNumber: process.env.TWILIO_WHATSAPP_NUMBER,
  webhookUrl: process.env.WEBHOOK_URL
};

/**
 * Send WhatsApp message
 */
async function sendWhatsAppMessage(to, body, mediaUrl = null) {
  try {
    if (!client) {
      logger.warn('Twilio client not available - message not sent', { to, body });
      return {
        success: false,
        error: 'Twilio client not configured'
      };
    }

    // Ensure phone number is in correct format
    const formattedTo = to.startsWith('whatsapp:') ? to : `whatsapp:${to}`;

    const messageOptions = {
      from: twilioConfig.whatsappNumber,
      to: formattedTo,
      body: body
    };

    // Add media if provided
    if (mediaUrl) {
      messageOptions.mediaUrl = [mediaUrl];
    }

    const message = await client.messages.create(messageOptions);

    logger.whatsapp(`Message sent successfully`, {
      to: formattedTo,
      messageSid: message.sid,
      status: message.status,
      bodyLength: body.length
    });

    return {
      success: true,
      messageSid: message.sid,
      status: message.status
    };

  } catch (error) {
    // Enhanced error logging for production debugging
    logger.error('Failed to send WhatsApp message:', {
      to,
      error: error.message,
      code: error.code,
      status: error.status,
      moreInfo: error.moreInfo,
      details: error.details,
      stack: error.stack,
      twilioErrorCode: error.code,
      twilioErrorMessage: error.message
    });

    return {
      success: false,
      error: error.message,
      code: error.code
    };
  }
}

/**
 * Send WhatsApp message template (for notifications)
 */
async function sendWhatsAppTemplate(to, templateSid, parameters = {}) {
  try {
    const formattedTo = to.startsWith('whatsapp:') ? to : `whatsapp:${to}`;

    const message = await client.messages.create({
      from: twilioConfig.whatsappNumber,
      to: formattedTo,
      contentSid: templateSid,
      contentVariables: JSON.stringify(parameters)
    });

    logger.whatsapp(`Template message sent successfully`, {
      to: formattedTo,
      templateSid,
      messageSid: message.sid,
      status: message.status
    });

    return {
      success: true,
      messageSid: message.sid,
      status: message.status
    };

  } catch (error) {
    logger.error('Failed to send WhatsApp template:', {
      to,
      templateSid,
      error: error.message,
      code: error.code
    });

    return {
      success: false,
      error: error.message,
      code: error.code
    };
  }
}

/**
 * Verify webhook signature for security
 */
function verifyWebhookSignature(signature, url, body) {
  try {
    const authToken = twilioConfig.authToken;
    return twilio.validateRequest(authToken, signature, url, body);
  } catch (error) {
    logger.security('Webhook signature verification failed', {
      error: error.message,
      url
    });
    return false;
  }
}

/**
 * Format phone number for WhatsApp
 */
function formatPhoneNumber(phoneNumber) {
  // Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, '');

  // Add country code if missing (assume Tanzania +255)
  if (cleaned.length === 9 && cleaned.startsWith('7')) {
    cleaned = '255' + cleaned;
  } else if (cleaned.length === 10 && cleaned.startsWith('07')) {
    cleaned = '255' + cleaned.substring(1);
  } else if (cleaned.length === 12 && cleaned.startsWith('255')) {
    // Already has country code
  } else if (cleaned.length === 13 && cleaned.startsWith('2557')) {
    // Already formatted correctly
  } else {
    logger.warn('Unusual phone number format:', { phoneNumber, cleaned });
  }

  return `+${cleaned}`;
}

/**
 * Extract phone number from WhatsApp format
 */
function extractPhoneNumber(whatsappNumber) {
  if (whatsappNumber.startsWith('whatsapp:')) {
    return whatsappNumber.replace('whatsapp:', '');
  }
  return whatsappNumber;
}

/**
 * Check message delivery status
 */
async function getMessageStatus(messageSid) {
  try {
    const message = await client.messages(messageSid).fetch();

    logger.whatsapp(`Message status checked`, {
      messageSid,
      status: message.status,
      errorCode: message.errorCode,
      errorMessage: message.errorMessage
    });

    return {
      success: true,
      status: message.status,
      errorCode: message.errorCode,
      errorMessage: message.errorMessage,
      dateCreated: message.dateCreated,
      dateUpdated: message.dateUpdated
    };

  } catch (error) {
    logger.error('Failed to get message status:', {
      messageSid,
      error: error.message
    });

    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get account information
 */
async function getAccountInfo() {
  try {
    const account = await client.api.accounts(twilioConfig.accountSid).fetch();

    return {
      success: true,
      friendlyName: account.friendlyName,
      status: account.status,
      type: account.type
    };

  } catch (error) {
    logger.error('Failed to get account info:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Log Twilio configuration on startup
logger.info('Twilio WhatsApp configured', {
  accountSid: twilioConfig.accountSid.substring(0, 10) + '...',
  whatsappNumber: twilioConfig.whatsappNumber,
  webhookUrl: twilioConfig.webhookUrl
});

module.exports = {
  client,
  twilioConfig,
  sendWhatsAppMessage,
  sendWhatsAppTemplate,
  verifyWebhookSignature,
  formatPhoneNumber,
  extractPhoneNumber,
  getMessageStatus,
  getAccountInfo
};
