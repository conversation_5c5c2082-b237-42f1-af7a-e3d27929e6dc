/**
 * MongoDB Database Configuration
 * Optimized for production with proper error handling and connection pooling
 */

const mongoose = require('mongoose');
const logger = require('./logger');

// MongoDB connection options optimized for production WhatsApp POS
const mongoOptions = {
  // Connection pool settings - optimized for high-frequency WhatsApp messages
  maxPoolSize: 20, // Increased for concurrent WhatsApp requests
  minPoolSize: 5,  // Higher minimum for instant availability
  maxIdleTimeMS: 60000, // Longer idle time for WhatsApp session persistence

  // Timeout settings - optimized for fast WhatsApp responses
  serverSelectionTimeoutMS: 3000, // Faster server selection
  socketTimeoutMS: 10000, // Shorter socket timeout for quick failures
  connectTimeoutMS: 5000, // Faster connection timeout

  // Performance optimizations
  maxConnecting: 5, // Limit concurrent connection attempts
  heartbeatFrequencyMS: 10000, // More frequent heartbeats
  retryWrites: true, // Enable retry for write operations
  retryReads: true, // Enable retry for read operations

  // Production stability - these options are handled by Mongoose, not MongoDB driver
};

// Connection state tracking
let isConnected = false;

/**
 * Connect to MongoDB with retry logic
 */
async function connectDB() {
  try {
    if (isConnected) {
      logger.info('MongoDB already connected');
      return;
    }

    const mongoURI = process.env.NODE_ENV === 'test'
      ? process.env.MONGODB_TEST_URI
      : process.env.MONGODB_URI;

    if (!mongoURI) {
      throw new Error('MongoDB URI not provided in environment variables');
    }

    // Connect to MongoDB
    await mongoose.connect(mongoURI, mongoOptions);

    isConnected = true;
    logger.info(`MongoDB connected successfully to ${mongoose.connection.name}`);

    // Log connection details in development
    if (process.env.NODE_ENV === 'development') {
      logger.info(`MongoDB Host: ${mongoose.connection.host}`);
      logger.info(`MongoDB Port: ${mongoose.connection.port}`);
    }

  } catch (error) {
    logger.error('MongoDB connection error:', error);

    // Retry connection after 5 seconds
    setTimeout(() => {
      logger.info('Retrying MongoDB connection...');
      connectDB();
    }, 5000);

    throw error;
  }
}

/**
 * Disconnect from MongoDB
 */
async function disconnectDB() {
  try {
    if (!isConnected) {
      return;
    }

    await mongoose.disconnect();
    isConnected = false;
    logger.info('MongoDB disconnected successfully');
  } catch (error) {
    logger.error('Error disconnecting from MongoDB:', error);
    throw error;
  }
}

// Connection event handlers
mongoose.connection.on('connected', () => {
  isConnected = true;
  logger.info('Mongoose connected to MongoDB');
});

mongoose.connection.on('error', (err) => {
  isConnected = false;
  logger.error('Mongoose connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  isConnected = false;
  logger.warn('Mongoose disconnected from MongoDB');
});

// Handle application termination
process.on('SIGINT', async () => {
  try {
    await mongoose.connection.close();
    logger.info('MongoDB connection closed through app termination');
    process.exit(0);
  } catch (error) {
    logger.error('Error closing MongoDB connection:', error);
    process.exit(1);
  }
});

module.exports = {
  connectDB,
  disconnectDB,
  isConnected: () => isConnected
};
