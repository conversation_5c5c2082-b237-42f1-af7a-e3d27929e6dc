/**
 * Application Monitoring and Performance Tracking
 * Integrates Sentry for error tracking and custom performance monitoring
 */

const Sentry = require('@sentry/node');
const { ProfilingIntegration } = require('@sentry/profiling-node');
const logger = require('./logger');

/**
 * Initialize Sentry monitoring
 */
function initializeSentry() {
  if (!process.env.SENTRY_DSN) {
    logger.warn('Sentry DSN not configured - error tracking disabled');
    return;
  }

  try {
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV || 'development',
      
      // Performance monitoring
      tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
      profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
      
      // Integrations
      integrations: [
        new ProfilingIntegration(),
        new Sentry.Integrations.Http({ tracing: true }),
        new Sentry.Integrations.Express({ app: require('../app') }),
        new Sentry.Integrations.Mongo({
          useMongoose: true
        })
      ],
      
      // Release tracking
      release: process.env.npm_package_version || '1.0.0',
      
      // Error filtering
      beforeSend(event, hint) {
        // Filter out non-critical errors in development
        if (process.env.NODE_ENV === 'development') {
          const error = hint.originalException;
          if (error && error.code === 'ECONNREFUSED') {
            return null; // Don't send connection errors in dev
          }
        }
        
        // Add business context
        if (event.user) {
          event.tags = {
            ...event.tags,
            business_id: event.user.business_id,
            user_role: event.user.role
          };
        }
        
        return event;
      },
      
      // Performance filtering
      beforeSendTransaction(event) {
        // Filter out health check transactions
        if (event.transaction === 'GET /health') {
          return null;
        }
        
        return event;
      }
    });

    logger.info('Sentry monitoring initialized', {
      environment: process.env.NODE_ENV,
      dsn: process.env.SENTRY_DSN.substring(0, 20) + '...'
    });

  } catch (error) {
    logger.error('Failed to initialize Sentry:', error);
  }
}

/**
 * Custom performance monitoring
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.alerts = [];
  }

  /**
   * Start timing an operation
   */
  startTimer(operation) {
    const startTime = process.hrtime.bigint();
    return {
      operation,
      startTime,
      end: () => this.endTimer(operation, startTime)
    };
  }

  /**
   * End timing an operation
   */
  endTimer(operation, startTime) {
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    
    this.recordMetric(operation, duration);
    
    // Check for performance issues
    this.checkPerformanceThresholds(operation, duration);
    
    return duration;
  }

  /**
   * Record a metric
   */
  recordMetric(operation, value, unit = 'ms') {
    const key = `${operation}_${unit}`;
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, {
        operation,
        unit,
        values: [],
        count: 0,
        sum: 0,
        min: Infinity,
        max: -Infinity,
        avg: 0
      });
    }
    
    const metric = this.metrics.get(key);
    metric.values.push(value);
    metric.count++;
    metric.sum += value;
    metric.min = Math.min(metric.min, value);
    metric.max = Math.max(metric.max, value);
    metric.avg = metric.sum / metric.count;
    
    // Keep only last 100 values
    if (metric.values.length > 100) {
      metric.values.shift();
    }
    
    // Send to Sentry if configured
    if (Sentry.getCurrentHub().getClient()) {
      Sentry.addBreadcrumb({
        category: 'performance',
        message: `${operation}: ${value}${unit}`,
        level: 'info',
        data: { operation, value, unit }
      });
    }
  }

  /**
   * Check performance thresholds and create alerts
   */
  checkPerformanceThresholds(operation, duration) {
    const thresholds = {
      'database_query': 1000, // 1 second
      'api_request': 5000, // 5 seconds
      'whatsapp_send': 3000, // 3 seconds
      'backup_create': 30000, // 30 seconds
      'report_generate': 10000 // 10 seconds
    };
    
    const threshold = thresholds[operation];
    if (threshold && duration > threshold) {
      const alert = {
        type: 'performance',
        operation,
        duration,
        threshold,
        timestamp: new Date(),
        severity: duration > threshold * 2 ? 'high' : 'medium'
      };
      
      this.alerts.push(alert);
      
      // Log performance issue
      logger.warn('Performance threshold exceeded', alert);
      
      // Send to Sentry
      if (Sentry.getCurrentHub().getClient()) {
        Sentry.captureMessage(`Performance issue: ${operation} took ${duration}ms`, 'warning');
      }
      
      // Keep only last 50 alerts
      if (this.alerts.length > 50) {
        this.alerts.shift();
      }
    }
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    const result = {};
    
    for (const [key, metric] of this.metrics) {
      result[key] = {
        operation: metric.operation,
        unit: metric.unit,
        count: metric.count,
        avg: Math.round(metric.avg * 100) / 100,
        min: metric.min,
        max: metric.max,
        recent: metric.values.slice(-10) // Last 10 values
      };
    }
    
    return result;
  }

  /**
   * Get recent alerts
   */
  getAlerts() {
    return this.alerts.slice(-20); // Last 20 alerts
  }

  /**
   * Clear metrics and alerts
   */
  clear() {
    this.metrics.clear();
    this.alerts = [];
  }
}

// Global performance monitor instance
const performanceMonitor = new PerformanceMonitor();

/**
 * Express middleware for request monitoring
 */
function requestMonitoringMiddleware(req, res, next) {
  const timer = performanceMonitor.startTimer('api_request');
  
  // Add request context to Sentry
  if (Sentry.getCurrentHub().getClient()) {
    Sentry.configureScope(scope => {
      scope.setTag('method', req.method);
      scope.setTag('route', req.route?.path || req.path);
      scope.setContext('request', {
        method: req.method,
        url: req.originalUrl,
        headers: req.headers,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      if (req.user) {
        scope.setUser({
          id: req.user._id,
          business_id: req.user.business_id,
          role: req.user.role
        });
      }
    });
  }
  
  // Monitor response
  const originalSend = res.send;
  res.send = function(data) {
    const duration = timer.end();
    
    // Record additional metrics
    performanceMonitor.recordMetric('response_size', Buffer.byteLength(data || ''), 'bytes');
    performanceMonitor.recordMetric(`${req.method}_${res.statusCode}`, duration);
    
    // Log slow requests
    if (duration > 2000) {
      logger.warn('Slow request detected', {
        method: req.method,
        url: req.originalUrl,
        duration,
        statusCode: res.statusCode,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
    }
    
    return originalSend.call(this, data);
  };
  
  next();
}

/**
 * Database query monitoring wrapper
 */
function monitorDatabaseQuery(queryName, queryFunction) {
  return async (...args) => {
    const timer = performanceMonitor.startTimer('database_query');
    
    try {
      const result = await queryFunction(...args);
      timer.end();
      
      // Record query-specific metrics
      performanceMonitor.recordMetric(`db_${queryName}`, timer.end());
      
      return result;
    } catch (error) {
      timer.end();
      
      // Capture database errors
      if (Sentry.getCurrentHub().getClient()) {
        Sentry.captureException(error, {
          tags: {
            operation: 'database_query',
            query: queryName
          }
        });
      }
      
      throw error;
    }
  };
}

/**
 * WhatsApp message monitoring
 */
function monitorWhatsAppMessage(messageFunction) {
  return async (...args) => {
    const timer = performanceMonitor.startTimer('whatsapp_send');
    
    try {
      const result = await messageFunction(...args);
      timer.end();
      
      performanceMonitor.recordMetric('whatsapp_success', 1, 'count');
      
      return result;
    } catch (error) {
      timer.end();
      
      performanceMonitor.recordMetric('whatsapp_error', 1, 'count');
      
      // Capture WhatsApp errors with context
      if (Sentry.getCurrentHub().getClient()) {
        Sentry.captureException(error, {
          tags: {
            operation: 'whatsapp_send',
            service: 'twilio'
          },
          extra: {
            args: args.map(arg => typeof arg === 'string' ? arg : '[object]')
          }
        });
      }
      
      throw error;
    }
  };
}

/**
 * Get monitoring status
 */
function getMonitoringStatus() {
  return {
    sentry: {
      enabled: !!Sentry.getCurrentHub().getClient(),
      dsn: process.env.SENTRY_DSN ? 'configured' : 'not configured',
      environment: process.env.NODE_ENV
    },
    performance: {
      metrics_count: performanceMonitor.metrics.size,
      alerts_count: performanceMonitor.alerts.length,
      uptime: process.uptime()
    },
    memory: process.memoryUsage(),
    cpu: process.cpuUsage()
  };
}

module.exports = {
  initializeSentry,
  performanceMonitor,
  requestMonitoringMiddleware,
  monitorDatabaseQuery,
  monitorWhatsAppMessage,
  getMonitoringStatus,
  Sentry
};
