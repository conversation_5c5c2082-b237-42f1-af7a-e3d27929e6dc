/**
 * Environment Configuration Validation
 * Validates and normalizes environment variables for production readiness
 */

const logger = require('./logger');

/**
 * Required environment variables for production
 */
const REQUIRED_PRODUCTION_VARS = [
  'NODE_ENV',
  'MONGODB_URI',
  'JWT_SECRET',
  'JWT_REFRESH_SECRET',
  'TWILIO_ACCOUNT_SID',
  'TWILIO_AUTH_TOKEN',
  'TWILIO_WHATSAPP_NUMBER',
  'WEBHOOK_URL',
  'FRONTEND_URL'
];

/**
 * Optional environment variables with defaults
 */
const DEFAULT_VALUES = {
  PORT: '3000',
  HTTPS_PORT: '443',
  DEFAULT_LANGUAGE: 'sw',
  DEFAULT_TIMEZONE: 'Africa/Dar_es_Salaam',
  DEFAULT_CURRENCY: 'TZS',
  LOG_LEVEL: 'info',
  BCRYPT_ROUNDS: '12',
  RATE_LIMIT_WINDOW_MS: '900000',
  RATE_LIMIT_MAX_REQUESTS: '100',
  MAX_FILE_SIZE: '5242880',
  TRIAL_DURATION_DAYS: '14',
  CACHE_TTL: '300',
  CACHE_MAX_KEYS: '1000',
  DB_MIN_POOL_SIZE: '5',
  DB_MAX_POOL_SIZE: '20',
  DB_MAX_IDLE_TIME: '60000',
  CONNECTION_TIMEOUT: '30000',
  REQUEST_TIMEOUT: '30000',
  DATA_RETENTION_DAYS: '2555'
};

/**
 * Environment variable validation rules
 */
const VALIDATION_RULES = {
  PORT: (value) => {
    const port = parseInt(value);
    return port > 0 && port < 65536 ? null : 'Port must be between 1 and 65535';
  },
  
  HTTPS_PORT: (value) => {
    const port = parseInt(value);
    return port > 0 && port < 65536 ? null : 'HTTPS port must be between 1 and 65535';
  },
  
  MONGODB_URI: (value) => {
    return value.startsWith('mongodb://') || value.startsWith('mongodb+srv://') 
      ? null : 'MongoDB URI must start with mongodb:// or mongodb+srv://';
  },
  
  JWT_SECRET: (value) => {
    return value.length >= 32 ? null : 'JWT secret must be at least 32 characters long';
  },
  
  JWT_REFRESH_SECRET: (value) => {
    return value.length >= 32 ? null : 'JWT refresh secret must be at least 32 characters long';
  },
  
  TWILIO_ACCOUNT_SID: (value) => {
    return value.startsWith('AC') && value.length === 34 
      ? null : 'Twilio Account SID must start with AC and be 34 characters long';
  },
  
  TWILIO_WHATSAPP_NUMBER: (value) => {
    return value.startsWith('whatsapp:+') 
      ? null : 'Twilio WhatsApp number must start with whatsapp:+';
  },
  
  WEBHOOK_URL: (value) => {
    return value.startsWith('https://') 
      ? null : 'Webhook URL must use HTTPS in production';
  },
  
  FRONTEND_URL: (value) => {
    return value.startsWith('https://') || process.env.NODE_ENV !== 'production'
      ? null : 'Frontend URL must use HTTPS in production';
  },
  
  BCRYPT_ROUNDS: (value) => {
    const rounds = parseInt(value);
    return rounds >= 10 && rounds <= 15 
      ? null : 'BCrypt rounds must be between 10 and 15';
  },
  
  DEFAULT_LANGUAGE: (value) => {
    return ['sw', 'en'].includes(value) 
      ? null : 'Default language must be sw or en';
  },
  
  LOG_LEVEL: (value) => {
    return ['error', 'warn', 'info', 'debug'].includes(value)
      ? null : 'Log level must be error, warn, info, or debug';
  }
};

/**
 * Validate environment variables
 */
function validateEnvironment() {
  const errors = [];
  const warnings = [];
  
  // Check required variables for production
  if (process.env.NODE_ENV === 'production') {
    for (const varName of REQUIRED_PRODUCTION_VARS) {
      if (!process.env[varName]) {
        errors.push(`Missing required environment variable: ${varName}`);
      }
    }
  }
  
  // Apply default values
  for (const [varName, defaultValue] of Object.entries(DEFAULT_VALUES)) {
    if (!process.env[varName]) {
      process.env[varName] = defaultValue;
      if (process.env.NODE_ENV === 'production') {
        warnings.push(`Using default value for ${varName}: ${defaultValue}`);
      }
    }
  }
  
  // Validate specific variables
  for (const [varName, validator] of Object.entries(VALIDATION_RULES)) {
    const value = process.env[varName];
    if (value) {
      const error = validator(value);
      if (error) {
        errors.push(`${varName}: ${error}`);
      }
    }
  }
  
  // Check for placeholder values in production
  if (process.env.NODE_ENV === 'production') {
    const placeholderPatterns = [
      /REPLACE-WITH-/,
      /your-.*-here/,
      /localhost/,
      /example\.com/
    ];
    
    for (const [varName, value] of Object.entries(process.env)) {
      if (typeof value === 'string') {
        for (const pattern of placeholderPatterns) {
          if (pattern.test(value)) {
            errors.push(`${varName} contains placeholder value: ${value}`);
            break;
          }
        }
      }
    }
  }
  
  return { errors, warnings };
}

/**
 * Get environment configuration summary
 */
function getEnvironmentSummary() {
  return {
    environment: process.env.NODE_ENV,
    port: process.env.PORT,
    httpsPort: process.env.HTTPS_PORT,
    database: process.env.MONGODB_URI ? 'Configured' : 'Not configured',
    redis: process.env.REDIS_URL ? 'Configured' : 'Not configured',
    twilio: process.env.TWILIO_ACCOUNT_SID ? 'Configured' : 'Not configured',
    ssl: process.env.SSL_CERT_PATH ? 'Configured' : 'Not configured',
    monitoring: process.env.SENTRY_DSN ? 'Configured' : 'Not configured',
    backup: process.env.BACKUP_ENABLED === 'true' ? 'Enabled' : 'Disabled',
    cache: process.env.CACHE_ENABLED === 'true' ? 'Enabled' : 'Disabled',
    language: process.env.DEFAULT_LANGUAGE,
    timezone: process.env.DEFAULT_TIMEZONE,
    currency: process.env.DEFAULT_CURRENCY
  };
}

/**
 * Initialize and validate environment
 */
function initializeEnvironment() {
  logger.info('Initializing environment configuration...');
  
  const { errors, warnings } = validateEnvironment();
  
  // Log warnings
  if (warnings.length > 0) {
    warnings.forEach(warning => logger.warn(`Environment: ${warning}`));
  }
  
  // Handle errors
  if (errors.length > 0) {
    logger.error('Environment validation failed:');
    errors.forEach(error => logger.error(`  - ${error}`));
    
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Environment validation failed. Cannot start in production with invalid configuration.');
    } else {
      logger.warn('Environment validation failed, but continuing in development mode.');
    }
  }
  
  // Log summary
  const summary = getEnvironmentSummary();
  logger.info('Environment configuration summary:', summary);
  
  return { valid: errors.length === 0, errors, warnings, summary };
}

/**
 * Check if running in production
 */
function isProduction() {
  return process.env.NODE_ENV === 'production';
}

/**
 * Check if running in development
 */
function isDevelopment() {
  return process.env.NODE_ENV === 'development';
}

/**
 * Check if running in test
 */
function isTest() {
  return process.env.NODE_ENV === 'test';
}

module.exports = {
  validateEnvironment,
  getEnvironmentSummary,
  initializeEnvironment,
  isProduction,
  isDevelopment,
  isTest,
  REQUIRED_PRODUCTION_VARS,
  DEFAULT_VALUES
};
