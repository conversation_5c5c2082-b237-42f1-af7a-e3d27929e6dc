/**
 * Redis Configuration for Session Management and Caching
 * Optimized for WhatsApp conversation state management
 */

const redis = require('redis');
const logger = require('./logger');

let redisClient = null;
let isConnected = false;

// Redis configuration options
const redisOptions = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  password: process.env.REDIS_PASSWORD || undefined,

  // Performance optimizations for WhatsApp POS
  retryDelayOnFailover: 50, // Faster failover for WhatsApp responses
  enableReadyCheck: true,
  maxRetriesPerRequest: 2, // Fewer retries for faster failures
  lazyConnect: false, // Connect immediately for WhatsApp availability
  keepAlive: 30000,
  connectTimeout: 3000, // Faster connection timeout
  commandTimeout: 2000, // Faster command timeout for sessions
  retryDelayOnClusterDown: 100,
  enableOfflineQueue: false, // Disable queue for immediate failures

  // Connection pool for high concurrency
  family: 4, // Use IPv4
  db: 0, // Default database
};

/**
 * Connect to Redis with retry logic
 */
async function connectRedis() {
  try {
    if (isConnected && redisClient) {
      logger.info('Redis already connected');
      return redisClient;
    }

    // Create Redis client
    redisClient = redis.createClient(redisOptions);

    // Error handling
    redisClient.on('error', (err) => {
      isConnected = false;
      logger.error('Redis connection error:', err);
    });

    redisClient.on('connect', () => {
      logger.info('Redis client connected');
    });

    redisClient.on('ready', () => {
      isConnected = true;
      logger.info('Redis client ready for commands');
    });

    redisClient.on('end', () => {
      isConnected = false;
      logger.warn('Redis connection ended');
    });

    redisClient.on('reconnecting', () => {
      logger.info('Redis client reconnecting...');
    });

    // Connect to Redis
    await redisClient.connect();

    // Test connection
    await redisClient.ping();
    logger.info('Redis connected and responding to ping');

    return redisClient;

  } catch (error) {
    logger.error('Failed to connect to Redis:', error);

    // Retry connection after 5 seconds
    setTimeout(() => {
      logger.info('Retrying Redis connection...');
      connectRedis();
    }, 5000);

    throw error;
  }
}

/**
 * Disconnect from Redis
 */
async function disconnectRedis() {
  try {
    if (redisClient && isConnected) {
      await redisClient.quit();
      isConnected = false;
      logger.info('Redis disconnected successfully');
    }
  } catch (error) {
    logger.error('Error disconnecting from Redis:', error);
    throw error;
  }
}

/**
 * Get Redis client instance
 */
function getRedisClient() {
  if (!redisClient || !isConnected) {
    throw new Error('Redis client not connected. Call connectRedis() first.');
  }
  return redisClient;
}

/**
 * Session management utilities for WhatsApp conversations
 */
const sessionManager = {
  /**
   * Set user session data
   */
  async setSession(phoneNumber, sessionData, ttl = 1800) { // 30 minutes default
    try {
      const client = getRedisClient();
      const key = `session:${phoneNumber}`;
      await client.setEx(key, ttl, JSON.stringify(sessionData));
      logger.debug(`Session set for ${phoneNumber}`);
    } catch (error) {
      logger.error('Error setting session:', error);
      throw error;
    }
  },

  /**
   * Get user session data
   */
  async getSession(phoneNumber) {
    try {
      const client = getRedisClient();
      const key = `session:${phoneNumber}`;
      const sessionData = await client.get(key);

      if (sessionData) {
        return JSON.parse(sessionData);
      }
      return null;
    } catch (error) {
      logger.error('Error getting session:', error);
      return null;
    }
  },

  /**
   * Delete user session
   */
  async deleteSession(phoneNumber) {
    try {
      const client = getRedisClient();
      const key = `session:${phoneNumber}`;
      await client.del(key);
      logger.debug(`Session deleted for ${phoneNumber}`);
    } catch (error) {
      logger.error('Error deleting session:', error);
      throw error;
    }
  },

  /**
   * Extend session TTL
   */
  async extendSession(phoneNumber, ttl = 1800) {
    try {
      const client = getRedisClient();
      const key = `session:${phoneNumber}`;
      await client.expire(key, ttl);
      logger.debug(`Session extended for ${phoneNumber}`);
    } catch (error) {
      logger.error('Error extending session:', error);
      throw error;
    }
  }
};

/**
 * Cache utilities for frequently accessed data
 */
const cacheManager = {
  /**
   * Set cache data
   */
  async set(key, data, ttl = 3600) { // 1 hour default
    try {
      const client = getRedisClient();
      await client.setEx(`cache:${key}`, ttl, JSON.stringify(data));
    } catch (error) {
      logger.error('Error setting cache:', error);
      throw error;
    }
  },

  /**
   * Get cache data
   */
  async get(key) {
    try {
      const client = getRedisClient();
      const data = await client.get(`cache:${key}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error('Error getting cache:', error);
      return null;
    }
  },

  /**
   * Delete cache data
   */
  async delete(key) {
    try {
      const client = getRedisClient();
      await client.del(`cache:${key}`);
    } catch (error) {
      logger.error('Error deleting cache:', error);
      throw error;
    }
  }
};

// Handle application termination
process.on('SIGINT', async () => {
  try {
    await disconnectRedis();
    process.exit(0);
  } catch (error) {
    logger.error('Error closing Redis connection:', error);
    process.exit(1);
  }
});

module.exports = {
  connectRedis,
  disconnectRedis,
  getRedisClient,
  sessionManager,
  cacheManager,
  isConnected: () => isConnected
};
