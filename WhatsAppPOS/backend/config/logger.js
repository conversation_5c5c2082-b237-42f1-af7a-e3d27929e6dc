/**
 * <PERSON> Configuration
 * Comprehensive logging for production monitoring and debugging
 */

const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Enhanced format for structured logging
const structuredFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, service, userId, businessId, requestId, ...meta }) => {
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      service: service || 'whatsapp-pos',
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      pid: process.pid,
      hostname: require('os').hostname()
    };

    // Add user context if available
    if (userId) logEntry.userId = userId;
    if (businessId) logEntry.businessId = businessId;
    if (requestId) logEntry.requestId = requestId;

    // Add stack trace for errors
    if (stack) logEntry.stack = stack;

    // Add additional metadata
    if (Object.keys(meta).length > 0) {
      logEntry.meta = meta;
    }

    return JSON.stringify(logEntry);
  })
);

// Human-readable format for development
const humanFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, userId, businessId, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;

    // Add user context
    if (userId || businessId) {
      log += ` [User: ${userId || 'N/A'}, Business: ${businessId || 'N/A'}]`;
    }

    // Add stack trace for errors
    if (stack) {
      log += `\nStack: ${stack}`;
    }

    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      log += `\nMeta: ${JSON.stringify(meta, null, 2)}`;
    }

    return log;
  })
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, stack }) => {
    let log = `${timestamp} ${level}: ${message}`;
    if (stack && process.env.NODE_ENV === 'development') {
      log += `\n${stack}`;
    }
    return log;
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: {
    service: 'whatsapp-pos-tanzania',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    // Error log file
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),
    
    // Combined log file
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),
    
    // WhatsApp specific logs
    new winston.transports.File({
      filename: path.join(logsDir, 'whatsapp.log'),
      level: 'info',
      maxsize: 5242880, // 5MB
      maxFiles: 3,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
        winston.format((info) => {
          // Only log WhatsApp related messages
          if (info.message && (
            info.message.includes('WhatsApp') ||
            info.message.includes('Twilio') ||
            info.type === 'whatsapp'
          )) {
            return info;
          }
          return false;
        })()
      )
    })
  ],
  
  // Handle exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log'),
      maxsize: 5242880,
      maxFiles: 3
    })
  ],
  
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log'),
      maxsize: 5242880,
      maxFiles: 3
    })
  ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug'
  }));
}

// Add console transport for production with limited output
if (process.env.NODE_ENV === 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.simple()
    ),
    level: 'warn' // Only show warnings and errors in production console
  }));
}

// Custom logging methods for specific use cases
logger.whatsapp = (message, meta = {}) => {
  logger.info(message, { ...meta, type: 'whatsapp' });
};

logger.business = (message, businessId, meta = {}) => {
  logger.info(message, { ...meta, businessId, type: 'business' });
};

logger.security = (message, meta = {}) => {
  logger.warn(message, { ...meta, type: 'security' });
};

logger.performance = (message, duration, meta = {}) => {
  logger.info(message, { ...meta, duration, type: 'performance' });
};

logger.audit = (message, meta = {}) => {
  logger.info(message, { ...meta, type: 'audit', category: 'audit_trail' });
};

logger.api = (message, meta = {}) => {
  logger.info(message, { ...meta, type: 'api', category: 'api_request' });
};

logger.database = (message, meta = {}) => {
  logger.info(message, { ...meta, type: 'database', category: 'database_operation' });
};

logger.backup = (message, meta = {}) => {
  logger.info(message, { ...meta, type: 'backup', category: 'backup_operation' });
};

// Context-aware logging with user and business information
logger.withContext = (userId, businessId) => {
  return {
    info: (message, meta = {}) => logger.info(message, { ...meta, userId, businessId }),
    warn: (message, meta = {}) => logger.warn(message, { ...meta, userId, businessId }),
    error: (message, meta = {}) => logger.error(message, { ...meta, userId, businessId }),
    debug: (message, meta = {}) => logger.debug(message, { ...meta, userId, businessId }),
    business: (message, meta = {}) => logger.business(message, businessId, { ...meta }),
    whatsapp: (message, meta = {}) => logger.whatsapp(message, { ...meta, userId, businessId }),
    security: (message, meta = {}) => logger.security(message, { ...meta, userId, businessId }),
    performance: (message, duration, meta = {}) => logger.performance(message, duration, { ...meta, userId, businessId }),
    audit: (message, meta = {}) => logger.audit(message, { ...meta, userId, businessId }),
    api: (message, meta = {}) => logger.api(message, { ...meta, userId, businessId }),
    database: (message, meta = {}) => logger.database(message, { ...meta, userId, businessId }),
    backup: (message, meta = {}) => logger.backup(message, { ...meta, userId, businessId })
  };
};

// Request-specific logger
logger.forRequest = (req) => {
  const requestId = req.headers['x-request-id'] || require('crypto').randomUUID();
  const userId = req.user?._id;
  const businessId = req.user?.business_id || req.business?._id;

  return {
    info: (message, meta = {}) => logger.info(message, { ...meta, requestId, userId, businessId, method: req.method, url: req.originalUrl }),
    warn: (message, meta = {}) => logger.warn(message, { ...meta, requestId, userId, businessId, method: req.method, url: req.originalUrl }),
    error: (message, meta = {}) => logger.error(message, { ...meta, requestId, userId, businessId, method: req.method, url: req.originalUrl }),
    debug: (message, meta = {}) => logger.debug(message, { ...meta, requestId, userId, businessId, method: req.method, url: req.originalUrl }),
    business: (message, meta = {}) => logger.business(message, businessId, { ...meta, requestId }),
    whatsapp: (message, meta = {}) => logger.whatsapp(message, { ...meta, requestId, userId, businessId }),
    security: (message, meta = {}) => logger.security(message, { ...meta, requestId, userId, businessId }),
    performance: (message, duration, meta = {}) => logger.performance(message, duration, { ...meta, requestId, userId, businessId }),
    audit: (message, meta = {}) => logger.audit(message, { ...meta, requestId, userId, businessId }),
    api: (message, meta = {}) => logger.api(message, { ...meta, requestId, userId, businessId })
  };
};

// Log aggregation helper for external services
logger.getLogStats = () => {
  return {
    logDirectory: logsDir,
    level: logger.level,
    environment: process.env.NODE_ENV,
    transports: logger.transports.length,
    uptime: process.uptime(),
    memoryUsage: process.memoryUsage()
  };
};

// Log startup information
logger.info('Enhanced logger initialized', {
  level: logger.level,
  environment: process.env.NODE_ENV,
  logDirectory: logsDir,
  features: ['structured_logging', 'context_aware', 'performance_tracking', 'security_events']
});

module.exports = logger;
