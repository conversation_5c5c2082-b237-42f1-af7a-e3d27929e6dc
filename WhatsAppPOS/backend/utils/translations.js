/**
 * Translations Utility
 * Comprehensive Swahili and English translations for the WhatsApp POS system
 */

/**
 * Core system messages
 */
const messages = {
  // Welcome and help messages
  welcome: {
    sw: "Karibu kwenye mfumo wa POS! 🏪\n\nAndika 'msaada' kupata maelekezo kamili.",
    en: "Welcome to the POS system! 🏪\n\nType 'help' for complete instructions."
  },

  // Language switching messages
  language: {
    switched_to_english: {
      sw: "Lugha imebadilishwa kuwa Kiingereza ✅",
      en: "Language switched to English ✅"
    },
    switched_to_swahili: {
      sw: "Lugha imebadilishwa kuwa Kiswahili ✅",
      en: "Language switched to Swahili ✅"
    }
  },

  help: {
    sw: `📋 *MAELEKEZO YA MFUMO WA POS*

🏪 *BIASHARA*
• *sajili* - <PERSON><PERSON><PERSON> biashara mpya
• *maelezo* - Ona maelezo ya biashara

📦 *BIDHAA*
• *ongeza bidhaa* - Ongeza bidhaa mpya
• *bidhaa* - Ona bidhaa zote
• *tafuta [jina]* - <PERSON><PERSON><PERSON> bidhaa
• *hisa* - <PERSON><PERSON> hisa za bidhaa

💰 *MAUZO*
• *uza* - <PERSON><PERSON><PERSON> mauzo
• *mauzo ya leo* - Ripoti ya leo
• *mauzo ya wiki* - Ripoti ya wiki

👥 *WATEJA*
• *ongeza mteja* - Ongeza mteja mpya
• *wateja* - Ona wateja wote

⚙️ *MIPANGILIO*
• *lugha* - Badilisha lugha
• *msaada* - Ona maelekezo haya

Andika neno kuu au nambari ya chaguo.`,
    en: `📋 *POS SYSTEM INSTRUCTIONS*

🏪 *BUSINESS*
• *register* - Register new business
• *info* - View business information

📦 *PRODUCTS*
• *add product* - Add new product
• *products* - View all products
• *search [name]* - Search products
• *stock* - Check stock levels

💰 *SALES*
• *sell* - Record sale
• *today sales* - Today's report
• *weekly sales* - Weekly report

👥 *CUSTOMERS*
• *add customer* - Add new customer
• *customers* - View all customers

⚙️ *SETTINGS*
• *language* - Change language
• *help* - View these instructions

Type a keyword or option number.`
  },

  // Business registration
  business_registration: {
    start: {
      sw: "🏪 *USAJILI WA BIASHARA*\n\nTuanze kwa jina la biashara yako:",
      en: "🏪 *BUSINESS REGISTRATION*\n\nLet's start with your business name:"
    },
    name_received: {
      sw: "Vizuri! Sasa andika aina ya biashara yako:\n\n1. Duka\n2. Salon\n3. Mgahawa\n4. Duka la dawa\n5. Electronics\n6. Nguo\n7. Hardware\n8. Huduma\n9. Nyingine",
      en: "Great! Now select your business type:\n\n1. Shop\n2. Salon\n3. Restaurant\n4. Pharmacy\n5. Electronics\n6. Clothing\n7. Hardware\n8. Services\n9. Other"
    },
    type_received: {
      sw: "Sawa! Sasa andika jina la mmiliki wa biashara:",
      en: "Perfect! Now enter the business owner's name:"
    },
    success: {
      sw: "🎉 *Hongera!* Biashara yako imesajiliwa kikamilifu!\n\n📱 Nambari: {phone}\n🏪 Jina: {name}\n👤 Mmiliki: {owner}\n📅 Kipindi cha jaribio: siku 14\n\nAndika 'msaada' kuanza kutumia mfumo.",
      en: "🎉 *Congratulations!* Your business has been registered successfully!\n\n📱 Phone: {phone}\n🏪 Name: {name}\n👤 Owner: {owner}\n📅 Trial period: 14 days\n\nType 'help' to start using the system."
    }
  },

  // Product management
  products: {
    add_start: {
      sw: "📦 *ONGEZA BIDHAA MPYA*\n\nAndika jina la bidhaa:",
      en: "📦 *ADD NEW PRODUCT*\n\nEnter product name:"
    },
    name_received: {
      sw: "Sawa! Sasa andika bei ya bidhaa (TSh):",
      en: "Good! Now enter the product price (TSh):"
    },
    price_received: {
      sw: "Vizuri! Sasa andika idadi ya bidhaa (hisa):",
      en: "Great! Now enter the product quantity (stock):"
    },
    category_prompt: {
      sw: "Chagua aina ya bidhaa:\n\n1. Chakula\n2. Vinywaji\n3. Electronics\n4. Nguo\n5. Uzuri\n6. Afya\n7. Nyumbani\n8. Vifaa vya ofisi\n9. Huduma\n10. Nyingine",
      en: "Select product category:\n\n1. Food\n2. Drinks\n3. Electronics\n4. Clothing\n5. Beauty\n6. Health\n7. Household\n8. Stationery\n9. Services\n10. Other"
    },
    success: {
      sw: "✅ *Bidhaa imeongezwa!*\n\n📦 Jina: {name}\n💰 Bei: TSh {price}\n📊 Hisa: {stock}\n🏷️ Aina: {category}\n\nAndika 'bidhaa' kuona bidhaa zote.",
      en: "✅ *Product added!*\n\n📦 Name: {name}\n💰 Price: TSh {price}\n📊 Stock: {stock}\n🏷️ Category: {category}\n\nType 'products' to view all products."
    },
    list_header: {
      sw: "📦 *BIDHAA ZAKO*\n\n",
      en: "📦 *YOUR PRODUCTS*\n\n"
    },
    list_item: {
      sw: "{index}. *{name}*\n   💰 TSh {price} | 📊 {stock} zimesalia\n",
      en: "{index}. *{name}*\n   💰 TSh {price} | 📊 {stock} remaining\n"
    },
    list_empty: {
      sw: "Hakuna bidhaa bado. Andika 'ongeza bidhaa' kuanza.",
      en: "No products yet. Type 'add product' to start."
    }
  },

  // Sales management
  sales: {
    start: {
      sw: "💰 *REKODI MAUZO*\n\nChagua bidhaa kutoka orodha:\n\n",
      en: "💰 *RECORD SALE*\n\nSelect product from list:\n\n"
    },
    product_selected: {
      sw: "Umechagua: *{product}*\n\nAndika idadi unayouza:",
      en: "You selected: *{product}*\n\nEnter quantity to sell:"
    },
    quantity_received: {
      sw: "Sawa! Chagua njia ya malipo:\n\n1. 💵 Fedha taslimu\n2. 📱 M-Pesa\n3. 📱 Tigo Pesa\n4. 📱 Airtel Money\n5. 🏦 Benki",
      en: "Good! Select payment method:\n\n1. 💵 Cash\n2. 📱 M-Pesa\n3. 📱 Tigo Pesa\n4. 📱 Airtel Money\n5. 🏦 Bank"
    },
    success: {
      sw: "✅ *Mauzo yamerekodiwa!*\n\n🧾 *RISITI #{receipt}*\n📦 Bidhaa: {product}\n📊 Idadi: {quantity}\n💰 Jumla: TSh {total}\n💳 Malipo: {payment}\n📅 Tarehe: {date}\n\n🙏 Asante kwa biashara!",
      en: "✅ *Sale recorded!*\n\n🧾 *RECEIPT #{receipt}*\n📦 Product: {product}\n📊 Quantity: {quantity}\n💰 Total: TSh {total}\n💳 Payment: {payment}\n📅 Date: {date}\n\n🙏 Thank you for your business!"
    },
    daily_report: {
      sw: "📊 *RIPOTI YA LEO*\n📅 {date}\n\n💰 Mapato: TSh {revenue}\n📦 Bidhaa zilizouzwa: {items}\n🧾 Mauzo: {sales}\n👥 Wateja: {customers}\n\n📈 Bidhaa zinazouzwa zaidi:\n{top_products}",
      en: "📊 *TODAY'S REPORT*\n📅 {date}\n\n💰 Revenue: TSh {revenue}\n📦 Items sold: {items}\n🧾 Sales: {sales}\n👥 Customers: {customers}\n\n📈 Top selling products:\n{top_products}"
    }
  },

  // Customer management
  customers: {
    add_start: {
      sw: "👥 *ONGEZA MTEJA MPYA*\n\nAndika jina la mteja:",
      en: "👥 *ADD NEW CUSTOMER*\n\nEnter customer name:"
    },
    name_received: {
      sw: "Sawa! Andika nambari ya simu ya mteja (si lazima):",
      en: "Good! Enter customer phone number (optional):"
    },
    success: {
      sw: "✅ *Mteja ameongezwa!*\n\n👤 Jina: {name}\n📱 Simu: {phone}\n💰 Jumla ya ununuzi: TSh 0\n\nAndika 'wateja' kuona wateja wote.",
      en: "✅ *Customer added!*\n\n👤 Name: {name}\n📱 Phone: {phone}\n💰 Total purchases: TSh 0\n\nType 'customers' to view all customers."
    }
  },

  // Error messages
  errors: {
    invalid_command: {
      sw: "❌ Amri haikueleweka. Andika 'msaada' kupata maelekezo.",
      en: "❌ Command not recognized. Type 'help' for instructions."
    },
    missing_permission: {
      sw: "❌ Huna ruhusa ya kufanya kitendo hiki.",
      en: "❌ You don't have permission for this action."
    },
    product_not_found: {
      sw: "❌ Bidhaa haijapatikana.",
      en: "❌ Product not found."
    },
    insufficient_stock: {
      sw: "❌ Hisa haitoshi. Zimesalia {available} tu.",
      en: "❌ Insufficient stock. Only {available} available."
    },
    invalid_price: {
      sw: "❌ Bei si sahihi. Ingiza nambari tu.\n\n💡 Mfano: 5000 au 15000.50\n\nJaribu tena:",
      en: "❌ Invalid price. Please enter numbers only.\n\n💡 Example: 5000 or 15000.50\n\nTry again:"
    },
    invalid_quantity: {
      sw: "❌ Idadi si sahihi. Ingiza nambari tu.\n\n💡 Mfano: 1, 2, 5, 10\n\nJaribu tena:",
      en: "❌ Invalid quantity. Please enter numbers only.\n\n💡 Example: 1, 2, 5, 10\n\nTry again:"
    },
    network_error: {
      sw: "❌ Tatizo la mtandao limejitokeza.\n\n🔄 Jaribu tena au\n📞 Wasiliana na msaada\n\n💡 Andika 'menu' kurudi menyuni kuu",
      en: "❌ A network error occurred.\n\n🔄 Please try again or\n📞 Contact support\n\n💡 Type 'menu' to return to main menu"
    },
    business_not_found: {
      sw: "❌ Biashara haijapatikana. Sajili kwanza.",
      en: "❌ Business not found. Please register first."
    },
    subscription_expired: {
      sw: "❌ Usajili wako umekwisha. Lipia kuendelea kutumia huduma.",
      en: "❌ Your subscription has expired. Please pay to continue using the service."
    }
  },

  // Success messages
  success: {
    operation_completed: {
      sw: "✅ Shughuli imekamilika kikamilifu!",
      en: "✅ Operation completed successfully!"
    },
    data_saved: {
      sw: "✅ Data imehifadhiwa kikamilifu!",
      en: "✅ Data saved successfully!"
    },
    settings_updated: {
      sw: "✅ Mipangilio imesasishwa!",
      en: "✅ Settings updated!"
    }
  },

  // Notifications
  notifications: {
    low_stock: {
      sw: "⚠️ *ONYO LA HISA*\n\nBidhaa '{product}' imepungua!\n📊 Zimesalia: {quantity}\n🔄 Ongeza hisa haraka.",
      en: "⚠️ *LOW STOCK ALERT*\n\nProduct '{product}' is running low!\n📊 Remaining: {quantity}\n🔄 Please restock soon."
    },
    daily_summary: {
      sw: "📊 *MUHTASARI WA LEO*\n\n💰 Mapato: TSh {revenue}\n📦 Bidhaa: {items}\n🧾 Mauzo: {sales}\n\nVizuri! 👏",
      en: "📊 *DAILY SUMMARY*\n\n💰 Revenue: TSh {revenue}\n📦 Items: {items}\n🧾 Sales: {sales}\n\nWell done! 👏"
    },
    subscription_reminder: {
      sw: "⏰ *UKUMBUSHO WA USAJILI*\n\nUsajili wako utakwisha baada ya siku {days}.\n💳 Lipia kuendelea kupata huduma.",
      en: "⏰ *SUBSCRIPTION REMINDER*\n\nYour subscription expires in {days} days.\n💳 Please pay to continue service."
    }
  },

  // Common phrases
  common: {
    yes: { sw: "Ndio", en: "Yes" },
    no: { sw: "Hapana", en: "No" },
    cancel: { sw: "Ghairi", en: "Cancel" },
    continue: { sw: "Endelea", en: "Continue" },
    back: { sw: "Rudi", en: "Back" },
    next: { sw: "Ifuatayo", en: "Next" },
    finish: { sw: "Maliza", en: "Finish" },
    save: { sw: "Hifadhi", en: "Save" },
    edit: { sw: "Hariri", en: "Edit" },
    delete: { sw: "Futa", en: "Delete" },
    search: { sw: "Tafuta", en: "Search" },
    total: { sw: "Jumla", en: "Total" },
    price: { sw: "Bei", en: "Price" },
    quantity: { sw: "Idadi", en: "Quantity" },
    date: { sw: "Tarehe", en: "Date" },
    time: { sw: "Muda", en: "Time" },
    name: { sw: "Jina", en: "Name" },
    phone: { sw: "Simu", en: "Phone" },
    email: { sw: "Barua pepe", en: "Email" },
    address: { sw: "Anwani", en: "Address" }
  }
};

/**
 * Command mappings for different languages
 */
const commands = {
  // Help and navigation
  help: ['msaada', 'help', 'usaidizi', 'maelekezo'],
  start: ['anza', 'start', 'mwanzo'],
  menu: ['menyu', 'menu', 'orodha'],
  back: ['rudi', 'back', 'nyuma'],
  cancel: ['ghairi', 'cancel', 'acha'],

  // Business management
  register: ['sajili', 'register', 'usajili'],
  business_info: ['maelezo', 'info', 'business info', 'habari'],

  // Product management
  add_product: ['ongeza bidhaa', 'add product', 'bidhaa mpya', 'new product'],
  list_products: ['bidhaa', 'products', 'orodha bidhaa', 'list products'],
  search_product: ['tafuta', 'search', 'pata'],
  stock_check: ['hisa', 'stock', 'angalia hisa', 'check stock'],

  // Sales management
  record_sale: ['uza', 'sell', 'rekodi mauzo', 'record sale', 'mauzo'],
  daily_report: ['ripoti ya leo', 'today report', 'leo', 'today'],
  weekly_report: ['ripoti ya wiki', 'weekly report', 'wiki', 'weekly'],
  monthly_report: ['ripoti ya mwezi', 'monthly report', 'mwezi', 'monthly'],

  // Customer management
  add_customer: ['ongeza mteja', 'add customer', 'mteja mpya', 'new customer'],
  list_customers: ['wateja', 'customers', 'orodha wateja', 'list customers'],

  // Settings
  settings: ['mipangilio', 'settings', 'marekebisho'],
  language: ['lugha', 'language', 'badilisha lugha', 'change language'],

  // Payment methods
  cash: ['fedha taslimu', 'cash', 'taslimu'],
  mpesa: ['mpesa', 'm-pesa', 'em pesa'],
  tigo: ['tigo pesa', 'tigo', 'tigopesa'],
  airtel: ['airtel money', 'airtel', 'airtelmoney'],
  bank: ['benki', 'bank', 'akaunti']
};

/**
 * Get translated message
 */
function getMessage(key, language = 'sw', params = {}) {
  const keys = key.split('.');
  let message = messages;

  for (const k of keys) {
    if (message[k]) {
      message = message[k];
    } else {
      return `Translation not found: ${key}`;
    }
  }

  if (typeof message === 'object' && message[language]) {
    message = message[language];
  } else if (typeof message === 'object') {
    message = message.sw || message.en || 'Translation not found';
  }

  // Replace parameters
  if (typeof message === 'string' && params) {
    for (const [param, value] of Object.entries(params)) {
      message = message.replace(new RegExp(`{${param}}`, 'g'), value);
    }
  }

  return message;
}

/**
 * Detect command from user input
 */
function detectCommand(input, language = 'sw') {
  const normalizedInput = input.toLowerCase().trim();

  for (const [command, variations] of Object.entries(commands)) {
    for (const variation of variations) {
      if (normalizedInput.includes(variation.toLowerCase())) {
        return command;
      }
    }
  }

  return null;
}

/**
 * Get user's preferred language from various sources
 */
function detectLanguage(req) {
  // Check user preferences
  if (req.user?.preferences?.language) {
    return req.user.preferences.language;
  }

  // Check business settings
  if (req.business?.settings?.language) {
    return req.business.settings.language;
  }

  // Check Accept-Language header
  const acceptLanguage = req.headers['accept-language'];
  if (acceptLanguage && acceptLanguage.includes('sw')) {
    return 'sw';
  }

  // Default to Swahili
  return 'sw';
}

/**
 * Format currency in Tanzanian Shillings
 */
function formatCurrency(amount, language = 'sw') {
  const formatted = new Intl.NumberFormat('en-US').format(amount);
  return `TSh ${formatted}`;
}

/**
 * Format date in local format
 */
function formatDate(date, language = 'sw') {
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'Africa/Dar_es_Salaam'
  };

  if (language === 'sw') {
    // Swahili month names
    const swahiliMonths = [
      'Januari', 'Februari', 'Machi', 'Aprili', 'Mei', 'Juni',
      'Julai', 'Agosti', 'Septemba', 'Oktoba', 'Novemba', 'Desemba'
    ];

    const d = new Date(date);
    const day = d.getDate();
    const month = swahiliMonths[d.getMonth()];
    const year = d.getFullYear();

    return `${day} ${month} ${year}`;
  }

  return new Intl.DateTimeFormat('en-US', options).format(new Date(date));
}

module.exports = {
  messages,
  commands,
  getMessage,
  detectCommand,
  detectLanguage,
  formatCurrency,
  formatDate
};
