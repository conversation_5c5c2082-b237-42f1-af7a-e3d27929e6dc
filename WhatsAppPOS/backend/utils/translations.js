/**
 * Translations Utility
 * Comprehensive Swahili and English translations for the WhatsApp POS system
 */

/**
 * Core system messages
 */
const messages = {
  // Welcome and help messages
  welcome: {
    sw: "Karibu kwenye mfumo wa POS! 🏪\n\nAndika 'msaada' kupata maelekezo kamili.",
    en: "Welcome to the POS system! 🏪\n\nType 'help' for complete instructions."
  },

  // Language switching messages
  language: {
    switched_to_english: {
      sw: "Lugha imebadilishwa kuwa Kiingereza ✅",
      en: "Language switched to English ✅"
    },
    switched_to_swahili: {
      sw: "Lugha imebadilishwa kuwa Kiswahili ✅",
      en: "Language switched to Swahili ✅"
    }
  },

  help: {
    sw: `📋 *MAELEKEZO YA MFUMO WA POS*

🏪 *BIASHARA*
• *sajili* - <PERSON><PERSON><PERSON> biashara mpya
• *maelezo* - Ona maelezo ya biashara

📦 *BIDHAA*
• *ongeza bidhaa* - Ongeza bidhaa mpya
• *bidhaa* - Ona bidhaa zote
• *tafuta [jina]* - <PERSON><PERSON><PERSON> bidhaa
• *hisa* - <PERSON><PERSON> hisa za bidhaa

💰 *MAUZO*
• *uza* - <PERSON><PERSON><PERSON> mauzo
• *mauzo ya leo* - Ripoti ya leo
• *mauzo ya wiki* - Ripoti ya wiki

👥 *WATEJA*
• *ongeza mteja* - Ongeza mteja mpya
• *wateja* - Ona wateja wote

⚙️ *MIPANGILIO*
• *lugha* - Badilisha lugha
• *msaada* - Ona maelekezo haya

Andika neno kuu au nambari ya chaguo.`,
    en: `📋 *POS SYSTEM INSTRUCTIONS*

🏪 *BUSINESS*
• *register* - Register new business
• *info* - View business information

📦 *PRODUCTS*
• *add product* - Add new product
• *products* - View all products
• *search [name]* - Search products
• *stock* - Check stock levels

💰 *SALES*
• *sell* - Record sale
• *today sales* - Today's report
• *weekly sales* - Weekly report

👥 *CUSTOMERS*
• *add customer* - Add new customer
• *customers* - View all customers

⚙️ *SETTINGS*
• *language* - Change language
• *help* - View these instructions

Type a keyword or option number.`
  },

  // Business registration
  business_registration: {
    start: {
      sw: "🏪 *USAJILI WA BIASHARA*\n\nTuanze kwa jina la biashara yako:",
      en: "🏪 *BUSINESS REGISTRATION*\n\nLet's start with your business name:"
    },
    name_received: {
      sw: "Vizuri! Sasa andika aina ya biashara yako:\n\n1. Duka\n2. Salon\n3. Mgahawa\n4. Duka la dawa\n5. Electronics\n6. Nguo\n7. Hardware\n8. Huduma\n9. Nyingine",
      en: "Great! Now select your business type:\n\n1. Shop\n2. Salon\n3. Restaurant\n4. Pharmacy\n5. Electronics\n6. Clothing\n7. Hardware\n8. Services\n9. Other"
    },
    type_received: {
      sw: "Sawa! Sasa andika jina la mmiliki wa biashara:",
      en: "Perfect! Now enter the business owner's name:"
    },
    success: {
      sw: "🎉 *Hongera!* Biashara yako imesajiliwa kikamilifu!\n\n📱 Nambari: {phone}\n🏪 Jina: {name}\n👤 Mmiliki: {owner}\n📅 Kipindi cha jaribio: siku 14\n\nAndika 'msaada' kuanza kutumia mfumo.",
      en: "🎉 *Congratulations!* Your business has been registered successfully!\n\n📱 Phone: {phone}\n🏪 Name: {name}\n👤 Owner: {owner}\n📅 Trial period: 14 days\n\nType 'help' to start using the system."
    }
  },

  // Product management
  products: {
    add_start: {
      sw: "📦 *ONGEZA BIDHAA MPYA*\n\nAndika jina la bidhaa:",
      en: "📦 *ADD NEW PRODUCT*\n\nEnter product name:"
    },
    name_received: {
      sw: "Sawa! Sasa andika bei ya bidhaa (TSh):",
      en: "Good! Now enter the product price (TSh):"
    },
    price_received: {
      sw: "Vizuri! Sasa andika idadi ya bidhaa (hisa):",
      en: "Great! Now enter the product quantity (stock):"
    },
    category_prompt: {
      sw: "Chagua aina ya bidhaa:\n\n1. Chakula\n2. Vinywaji\n3. Electronics\n4. Nguo\n5. Uzuri\n6. Afya\n7. Nyumbani\n8. Vifaa vya ofisi\n9. Huduma\n10. Nyingine",
      en: "Select product category:\n\n1. Food\n2. Drinks\n3. Electronics\n4. Clothing\n5. Beauty\n6. Health\n7. Household\n8. Stationery\n9. Services\n10. Other"
    },
    success: {
      sw: "✅ *Bidhaa imeongezwa!*\n\n📦 Jina: {name}\n💰 Bei: TSh {price}\n📊 Hisa: {stock}\n🏷️ Aina: {category}\n\nAndika 'bidhaa' kuona bidhaa zote.",
      en: "✅ *Product added!*\n\n📦 Name: {name}\n💰 Price: TSh {price}\n📊 Stock: {stock}\n🏷️ Category: {category}\n\nType 'products' to view all products."
    },
    edit_select: {
      sw: "✏️ *HARIRI BIDHAA*\n\nChagua bidhaa unayotaka kuhariri:",
      en: "✏️ *EDIT PRODUCT*\n\nSelect the product you want to edit:"
    },
    edit_instructions: {
      sw: "Andika nambari ya bidhaa au 'cancel' kuacha.",
      en: "Type the product number or 'cancel' to quit."
    },
    edit_field_select: {
      sw: "✏️ *HARIRI: {name}*\n\nChagua kile unachotaka kubadilisha:\n\n1. Jina\n2. Bei\n3. Hisa\n4. Aina\n\nAndika nambari:",
      en: "✏️ *EDIT: {name}*\n\nSelect what you want to change:\n\n1. Name\n2. Price\n3. Stock\n4. Category\n\nType number:"
    },
    edit_name_prompt: {
      sw: "Andika jina jipya la bidhaa:\n\n📝 Jina la sasa: {current}",
      en: "Enter new product name:\n\n📝 Current name: {current}"
    },
    edit_price_prompt: {
      sw: "Andika bei mpya ya bidhaa (TSh):\n\n💰 Bei ya sasa: TSh {current}",
      en: "Enter new product price (TSh):\n\n💰 Current price: TSh {current}"
    },
    edit_stock_prompt: {
      sw: "Andika hisa mpya ya bidhaa:\n\n📊 Hisa ya sasa: {current}",
      en: "Enter new product stock:\n\n📊 Current stock: {current}"
    },
    edit_category_prompt: {
      sw: "Chagua aina mpya ya bidhaa:\n\n🏷️ Aina ya sasa: {current}\n\n1. Chakula\n2. Vinywaji\n3. Electronics\n4. Nguo\n5. Uzuri\n6. Afya\n7. Nyumbani\n8. Vifaa vya ofisi\n9. Huduma\n10. Nyingine",
      en: "Select new product category:\n\n🏷️ Current category: {current}\n\n1. Food\n2. Drinks\n3. Electronics\n4. Clothing\n5. Beauty\n6. Health\n7. Household\n8. Stationery\n9. Services\n10. Other"
    },
    edit_success: {
      sw: "✅ *Bidhaa imebadilishwa!*\n\n📦 Bidhaa: {name}\n✏️ Kimebadilishwa: {field}\n🆕 Thamani mpya: {value}",
      en: "✅ *Product updated!*\n\n📦 Product: {name}\n✏️ Changed: {field}\n🆕 New value: {value}"
    },
    delete_select: {
      sw: "🗑️ *FUTA BIDHAA*\n\nChagua bidhaa unayotaka kufuta:",
      en: "🗑️ *DELETE PRODUCT*\n\nSelect the product you want to delete:"
    },
    delete_warning: {
      sw: "⚠️ *ONYO:* Bidhaa itafutwa kabisa!\n\nAndika nambari ya bidhaa au 'cancel' kuacha.",
      en: "⚠️ *WARNING:* Product will be permanently deleted!\n\nType the product number or 'cancel' to quit."
    },
    delete_confirm: {
      sw: "⚠️ *THIBITISHA KUFUTA*\n\n📦 Bidhaa: {name}\n\nJe, una uhakika unataka kufuta bidhaa hii?\n\nAndika 'NDIO' kuthibitisha au 'HAPANA' kuacha.",
      en: "⚠️ *CONFIRM DELETION*\n\n📦 Product: {name}\n\nAre you sure you want to delete this product?\n\nType 'YES' to confirm or 'NO' to cancel."
    },
    delete_confirm_again: {
      sw: "Tafadhali andika 'NDIO' kuthibitisha au 'HAPANA' kuacha.",
      en: "Please type 'YES' to confirm or 'NO' to cancel."
    },
    delete_success: {
      sw: "✅ *Bidhaa imefutwa!*\n\n📦 {name} imefutwa kutoka kwenye mfumo.",
      en: "✅ *Product deleted!*\n\n📦 {name} has been removed from the system."
    },
    delete_cancelled: {
      sw: "❌ Kufuta kumeghairiwa. Bidhaa haikufutwa.",
      en: "❌ Deletion cancelled. Product was not deleted."
    },
    list_header: {
      sw: "📦 *BIDHAA ZAKO*\n\n",
      en: "📦 *YOUR PRODUCTS*\n\n"
    },
    list_item: {
      sw: "{index}. *{name}*\n   💰 TSh {price} | 📊 {stock} zimesalia\n",
      en: "{index}. *{name}*\n   💰 TSh {price} | 📊 {stock} remaining\n"
    },
    list_empty: {
      sw: "Hakuna bidhaa bado. Andika 'ongeza bidhaa' kuanza.",
      en: "No products yet. Type 'add product' to start."
    }
  },

  // Sales management
  sales: {
    start: {
      sw: "💰 *REKODI MAUZO*\n\nChagua bidhaa kutoka orodha:\n\n",
      en: "💰 *RECORD SALE*\n\nSelect product from list:\n\n"
    },
    product_selected: {
      sw: "Umechagua: *{product}*\n\nAndika idadi unayouza:",
      en: "You selected: *{product}*\n\nEnter quantity to sell:"
    },
    quantity_received: {
      sw: "Sawa! Chagua njia ya malipo:\n\n1. 💵 Fedha taslimu\n2. 📱 M-Pesa\n3. 📱 Tigo Pesa\n4. 📱 Airtel Money\n5. 🏦 Benki",
      en: "Good! Select payment method:\n\n1. 💵 Cash\n2. 📱 M-Pesa\n3. 📱 Tigo Pesa\n4. 📱 Airtel Money\n5. 🏦 Bank"
    },
    success: {
      sw: "✅ *Mauzo yamerekodiwa!*\n\n🧾 *RISITI #{receipt}*\n📦 Bidhaa: {product}\n📊 Idadi: {quantity}\n💰 Jumla: TSh {total}\n💳 Malipo: {payment}\n📅 Tarehe: {date}\n\n🙏 Asante kwa biashara!",
      en: "✅ *Sale recorded!*\n\n🧾 *RECEIPT #{receipt}*\n📦 Product: {product}\n📊 Quantity: {quantity}\n💰 Total: TSh {total}\n💳 Payment: {payment}\n📅 Date: {date}\n\n🙏 Thank you for your business!"
    },
    cart_empty: {
      sw: "🛒 Kart yako ni tupu. Ongeza bidhaa kwanza.",
      en: "🛒 Your cart is empty. Add products first."
    },
    cart_cleared: {
      sw: "🗑️ Kart imefutwa. Anza upya.",
      en: "🗑️ Cart cleared. Start over."
    },
    product_selected_quantity: {
      sw: "✅ *Bidhaa imechaguliwa:* {product}\n💰 *Bei:* {price}\n📊 *Hisa:* {stock}\n\nAndika idadi unayotaka:",
      en: "✅ *Product selected:* {product}\n💰 *Price:* {price}\n📊 *Stock:* {stock}\n\nEnter quantity needed:"
    },
    try_product_search: {
      sw: "💡 Unaweza pia kutafuta bidhaa kwa jina. Mfano: 'Coca Cola'",
      en: "💡 You can also search products by name. Example: 'Coca Cola'"
    },
    no_products_found: {
      sw: "❌ Hakuna bidhaa zilizopatikana kwa '{search}'. Jaribu jina lingine.",
      en: "❌ No products found for '{search}'. Try a different name."
    },
    search_results: {
      sw: "🔍 *Matokeo ya utafutaji wa '{search}':*",
      en: "🔍 *Search results for '{search}':*"
    },
    select_from_search: {
      sw: "Chagua nambari ya bidhaa au jaribu utafutaji mwingine:",
      en: "Select product number or try another search:"
    },
    item_added_to_cart: {
      sw: "✅ *Imeongezwa kwenye kart!*\n\n📦 {product} x{quantity}\n💰 Jumla: {total}",
      en: "✅ *Added to cart!*\n\n📦 {product} x{quantity}\n💰 Total: {total}"
    },
    item_added_next_actions: {
      sw: "🛒 *VITENDO VINAVYOFUATA:*\n• Andika nambari ya bidhaa nyingine kuongeza\n• Andika 'maliza' kukamilisha mauzo\n• Andika 'cart' kuona muhtasari\n• Andika 'ondoa [nambari]' kuondoa bidhaa",
      en: "🛒 *NEXT ACTIONS:*\n• Type another product number to add\n• Type 'finish' to complete sale\n• Type 'cart' to view summary\n• Type 'remove [number]' to remove item"
    },
    cart_summary_header: {
      sw: "🛒 *MUHTASARI WA KART:*",
      en: "🛒 *CART SUMMARY:*"
    },
    cart_total: {
      sw: "💰 *JUMLA:* {total} ({items} bidhaa)",
      en: "💰 *TOTAL:* {total} ({items} items)"
    },
    item_removed_from_cart: {
      sw: "🗑️ {product} imeondolewa kutoka kwenye kart.",
      en: "🗑️ {product} removed from cart."
    },
    customer_selection_header: {
      sw: "👥 *CHAGUA MTEJA:*",
      en: "👥 *SELECT CUSTOMER:*"
    },
    no_customer_option: {
      sw: "Hakuna mteja (Mauzo ya jumla)",
      en: "No customer (General sale)"
    },
    customer_selection_instructions: {
      sw: "Chagua nambari ya mteja au '0' kwa mauzo ya jumla:",
      en: "Select customer number or '0' for general sale:"
    },
    customer_selected: {
      sw: "👤 *Mteja amechaguliwa:* {customer}",
      en: "👤 *Customer selected:* {customer}"
    },
    payment_method_selection: {
      sw: "💳 *CHAGUA NJIA YA MALIPO:*\n\n1. Pesa taslimu\n2. Pesa za simu (M-Pesa, etc.)\n3. Uhamisho wa benki\n4. Mkopo\n\nAndika nambari au jina:",
      en: "💳 *SELECT PAYMENT METHOD:*\n\n1. Cash\n2. Mobile money (M-Pesa, etc.)\n3. Bank transfer\n4. Credit\n\nType number or name:"
    },
    payment_amount_prompt: {
      sw: "💰 *Jumla ya malipo:* {total}\n💳 *Njia:* {method}\n\nAndika kiasi kilicholipwa:",
      en: "💰 *Total amount:* {total}\n💳 *Method:* {method}\n\nEnter amount paid:"
    },
    receipt_header: {
      sw: "🧾 *RISITI YA MAUZO*\n\n🏪 {business}\n📅 {date}\n🔢 Mauzo #{sale_id}",
      en: "🧾 *SALES RECEIPT*\n\n🏪 {business}\n📅 {date}\n🔢 Sale #{sale_id}"
    },
    receipt_items_header: {
      sw: "📦 *BIDHAA:*",
      en: "📦 *ITEMS:*"
    },
    receipt_total: {
      sw: "💰 JUMLA:",
      en: "💰 TOTAL:"
    },
    receipt_paid: {
      sw: "💳 IMELIPWA:",
      en: "💳 PAID:"
    },
    receipt_change: {
      sw: "💵 CHENJI:",
      en: "💵 CHANGE:"
    },
    receipt_payment_method: {
      sw: "📊 NJIA:",
      en: "📊 METHOD:"
    },
    receipt_customer: {
      sw: "👤 MTEJA:",
      en: "👤 CUSTOMER:"
    },
    receipt_footer: {
      sw: "🙏 Asante kwa kununua!\n📱 WhatsApp POS Tanzania",
      en: "🙏 Thank you for your purchase!\n📱 WhatsApp POS Tanzania"
    },
    post_sale_actions: {
      sw: "🎯 *VITENDO VINAVYOFUATA:*\n• Andika '1' kurekodi mauzo mengine\n• Andika '4' kuongeza taarifa za mteja\n• Andika '6' kuona ripoti ya leo\n• Andika 'menu' kurudi menyuni",
      en: "🎯 *NEXT ACTIONS:*\n• Type '1' to record another sale\n• Type '4' to add customer info\n• Type '6' to view daily report\n• Type 'menu' to return to menu"
    },
    quick_sale_header: {
      sw: "⚡ *MAUZO YA HARAKA*\n\nBidhaa zinazouzwa zaidi:",
      en: "⚡ *QUICK SALE*\n\nTop selling products:"
    },
    quick_sale_instructions: {
      sw: "Chagua nambari ya bidhaa kwa mauzo ya haraka:",
      en: "Select product number for quick sale:"
    },
    quick_sale_quantity: {
      sw: "⚡ *MAUZO YA HARAKA*\n\n📦 {product}\n💰 {price}\n\nAndika idadi:",
      en: "⚡ *QUICK SALE*\n\n📦 {product}\n💰 {price}\n\nEnter quantity:"
    },
    quick_sale_payment: {
      sw: "⚡ *MAUZO YA HARAKA*\n\n📦 {product} x{quantity}\n💰 Jumla: {total}\n\nChagua malipo:\n1. Pesa taslimu\n2. Pesa za simu\n3. Mkopo",
      en: "⚡ *QUICK SALE*\n\n📦 {product} x{quantity}\n💰 Total: {total}\n\nSelect payment:\n1. Cash\n2. Mobile money\n3. Credit"
    },
    quick_sale_success: {
      sw: "✅ *MAUZO YA HARAKA YAMEKAMILIKA!*\n\n📦 {product} x{quantity}\n💰 Jumla: {total}\n💳 Malipo: {payment}\n\n🙏 Asante!",
      en: "✅ *QUICK SALE COMPLETED!*\n\n📦 {product} x{quantity}\n💰 Total: {total}\n💳 Payment: {payment}\n\n🙏 Thank you!"
    },
    no_previous_sales: {
      sw: "❌ Hakuna mauzo ya awali yaliyopatikana.",
      en: "❌ No previous sales found."
    },
    repeat_sale_header: {
      sw: "🔄 *RUDIA MAUZO YA MWISHO:*",
      en: "🔄 *REPEAT LAST SALE:*"
    },
    repeat_sale_total: {
      sw: "💰 Jumla: {total}",
      en: "💰 Total: {total}"
    },
    repeat_sale_confirm: {
      sw: "Je, unataka kurudia mauzo haya? Andika 'NDIO' au 'HAPANA':",
      en: "Do you want to repeat this sale? Type 'YES' or 'NO':"
    },
    top_products_header: {
      sw: "🏆 *BIDHAA ZINAZOUZWA ZAIDI:*",
      en: "🏆 *TOP SELLING PRODUCTS:*"
    },
    top_products_instructions: {
      sw: "Chagua nambari ya bidhaa kuanza mauzo:",
      en: "Select product number to start sale:"
    },
    times_sold: {
      sw: "mara",
      en: "times sold"
    },
    daily_report: {
      sw: "📊 *RIPOTI YA LEO*\n📅 {date}\n\n💰 Mapato: TSh {revenue}\n📦 Bidhaa zilizouzwa: {items}\n🧾 Mauzo: {sales}\n👥 Wateja: {customers}\n\n📈 Bidhaa zinazouzwa zaidi:\n{top_products}",
      en: "📊 *TODAY'S REPORT*\n📅 {date}\n\n💰 Revenue: TSh {revenue}\n📦 Items sold: {items}\n🧾 Sales: {sales}\n👥 Customers: {customers}\n\n📈 Top selling products:\n{top_products}"
    }
  },

  // Customer management
  customers: {
    add_start: {
      sw: "👥 *ONGEZA MTEJA MPYA*\n\nAndika jina la mteja:",
      en: "👥 *ADD NEW CUSTOMER*\n\nEnter customer name:"
    },
    name_received: {
      sw: "Sawa! Andika nambari ya simu ya mteja (si lazima):",
      en: "Good! Enter customer phone number (optional):"
    },
    success: {
      sw: "✅ *Mteja ameongezwa!*\n\n👤 Jina: {name}\n📱 Simu: {phone}\n💰 Jumla ya ununuzi: TSh 0\n\nAndika 'wateja' kuona wateja wote.",
      en: "✅ *Customer added!*\n\n👤 Name: {name}\n📱 Phone: {phone}\n💰 Total purchases: TSh 0\n\nType 'customers' to view all customers."
    }
  },

  // Error messages
  errors: {
    invalid_command: {
      sw: "❌ Amri haikueleweka. Andika 'msaada' kupata maelekezo.",
      en: "❌ Command not recognized. Type 'help' for instructions."
    },
    missing_permission: {
      sw: "❌ Huna ruhusa ya kufanya kitendo hiki.",
      en: "❌ You don't have permission for this action."
    },
    product_not_found: {
      sw: "❌ Bidhaa haijapatikana.",
      en: "❌ Product not found."
    },
    insufficient_stock: {
      sw: "❌ Hisa haitoshi. Zimesalia {available} tu.",
      en: "❌ Insufficient stock. Only {available} available."
    },
    invalid_price: {
      sw: "❌ Bei si sahihi. Ingiza nambari tu.\n\n💡 Mfano: 5000 au 15000.50\n\nJaribu tena:",
      en: "❌ Invalid price. Please enter numbers only.\n\n💡 Example: 5000 or 15000.50\n\nTry again:"
    },
    invalid_quantity: {
      sw: "❌ Idadi si sahihi. Ingiza nambari tu.\n\n💡 Mfano: 1, 2, 5, 10\n\nJaribu tena:",
      en: "❌ Invalid quantity. Please enter numbers only.\n\n💡 Example: 1, 2, 5, 10\n\nTry again:"
    },
    network_error: {
      sw: "❌ Tatizo la mtandao limejitokeza.\n\n🔄 Jaribu tena au\n📞 Wasiliana na msaada\n\n💡 Andika 'menu' kurudi menyuni kuu",
      en: "❌ A network error occurred.\n\n🔄 Please try again or\n📞 Contact support\n\n💡 Type 'menu' to return to main menu"
    },
    business_not_found: {
      sw: "❌ Biashara haijapatikana. Sajili kwanza.",
      en: "❌ Business not found. Please register first."
    },
    invalid_selection: {
      sw: "❌ Chaguo si sahihi. Tafadhali chagua nambari iliyo kwenye orodha.",
      en: "❌ Invalid selection. Please choose a number from the list."
    },
    out_of_stock: {
      sw: "❌ {product} haipatikani. Hisa imekwisha.",
      en: "❌ {product} is out of stock. No inventory available."
    },
    invalid_payment_method: {
      sw: "❌ Njia ya malipo si sahihi. Chagua kutoka kwenye orodha.",
      en: "❌ Invalid payment method. Please select from the list."
    },
    invalid_amount: {
      sw: "❌ Kiasi si sahihi. Ingiza nambari tu.",
      en: "❌ Invalid amount. Please enter numbers only."
    },
    sale_failed: {
      sw: "❌ Mauzo yameshindwa kuhifadhiwa. Jaribu tena.",
      en: "❌ Sale failed to save. Please try again."
    },
    subscription_expired: {
      sw: "❌ Usajili wako umekwisha. Lipia kuendelea kutumia huduma.",
      en: "❌ Your subscription has expired. Please pay to continue using the service."
    }
  },

  // Success messages
  success: {
    operation_completed: {
      sw: "✅ Shughuli imekamilika kikamilifu!",
      en: "✅ Operation completed successfully!"
    },
    data_saved: {
      sw: "✅ Data imehifadhiwa kikamilifu!",
      en: "✅ Data saved successfully!"
    },
    settings_updated: {
      sw: "✅ Mipangilio imesasishwa!",
      en: "✅ Settings updated!"
    }
  },

  // Notifications
  notifications: {
    low_stock: {
      sw: "⚠️ *ONYO LA HISA*\n\nBidhaa '{product}' imepungua!\n📊 Zimesalia: {quantity}\n🔄 Ongeza hisa haraka.",
      en: "⚠️ *LOW STOCK ALERT*\n\nProduct '{product}' is running low!\n📊 Remaining: {quantity}\n🔄 Please restock soon."
    },
    daily_summary: {
      sw: "📊 *MUHTASARI WA LEO*\n\n💰 Mapato: TSh {revenue}\n📦 Bidhaa: {items}\n🧾 Mauzo: {sales}\n\nVizuri! 👏",
      en: "📊 *DAILY SUMMARY*\n\n💰 Revenue: TSh {revenue}\n📦 Items: {items}\n🧾 Sales: {sales}\n\nWell done! 👏"
    },
    subscription_reminder: {
      sw: "⏰ *UKUMBUSHO WA USAJILI*\n\nUsajili wako utakwisha baada ya siku {days}.\n💳 Lipia kuendelea kupata huduma.",
      en: "⏰ *SUBSCRIPTION REMINDER*\n\nYour subscription expires in {days} days.\n💳 Please pay to continue service."
    }
  },

  // Common phrases
  common: {
    yes: { sw: "Ndio", en: "Yes" },
    no: { sw: "Hapana", en: "No" },
    cancel: { sw: "Ghairi", en: "Cancel" },
    continue: { sw: "Endelea", en: "Continue" },
    back: { sw: "Rudi", en: "Back" },
    next: { sw: "Ifuatayo", en: "Next" },
    finish: { sw: "Maliza", en: "Finish" },
    save: { sw: "Hifadhi", en: "Save" },
    edit: { sw: "Hariri", en: "Edit" },
    delete: { sw: "Futa", en: "Delete" },
    search: { sw: "Tafuta", en: "Search" },
    total: { sw: "Jumla", en: "Total" },
    price: { sw: "Bei", en: "Price" },
    quantity: { sw: "Idadi", en: "Quantity" },
    date: { sw: "Tarehe", en: "Date" },
    time: { sw: "Muda", en: "Time" },
    name: { sw: "Jina", en: "Name" },
    phone: { sw: "Simu", en: "Phone" },
    email: { sw: "Barua pepe", en: "Email" },
    address: { sw: "Anwani", en: "Address" },
    retry: { sw: "Jaribu tena", en: "Try again" },
    cancelled: {
      sw: "❌ Imeghairishwa. Andika 'menu' kurudi menyuni kuu.",
      en: "❌ Cancelled. Type 'menu' to return to main menu."
    },
    operation_cancelled: {
      sw: "❌ Kitendo kimeghairiwa. Kurudi menyuni kuu...",
      en: "❌ Operation cancelled. Returning to main menu..."
    },
    unlimited: { sw: "Bila kikomo", en: "Unlimited" },
    service: { sw: "Huduma", en: "Service" },
    in_stock: { sw: "zinapatikana", en: "in stock" }
  },

  // Payment methods
  payment_methods: {
    cash: { sw: "Pesa taslimu", en: "Cash" },
    mobile_money: { sw: "Pesa za simu", en: "Mobile money" },
    bank_transfer: { sw: "Uhamisho wa benki", en: "Bank transfer" },
    credit: { sw: "Mkopo", en: "Credit" }
  },

  // Enhanced reports
  reports: {
    daily_header: {
      sw: "📊 *RIPOTI YA LEO*\n🏪 {business}\n📅 {date}",
      en: "📊 *TODAY'S REPORT*\n🏪 {business}\n📅 {date}"
    },
    daily_summary: {
      sw: "💰 *MAPATO:* {revenue}\n🧾 *MAUZO:* {transactions}\n📦 *BIDHAA:* {items}\n👥 *WATEJA:* {customers}\n📊 *WASTANI:* {avg_transaction}",
      en: "💰 *REVENUE:* {revenue}\n🧾 *SALES:* {transactions}\n📦 *ITEMS:* {items}\n👥 *CUSTOMERS:* {customers}\n📊 *AVERAGE:* {avg_transaction}"
    },
    daily_comparison: {
      sw: "{icon} *MABADILIKO:* {change}% kuliko jana ({yesterday})",
      en: "{icon} *CHANGE:* {change}% vs yesterday ({yesterday})"
    },
    top_products_section: {
      sw: "🏆 *BIDHAA BORA ZA LEO:*",
      en: "🏆 *TODAY'S TOP PRODUCTS:*"
    },
    contextual_actions_header: {
      sw: "🎯 *MAPENDEKEZO YA VITENDO:*",
      en: "🎯 *SUGGESTED ACTIONS:*"
    },
    action_record_first_sale: {
      sw: "Rekodi mauzo ya kwanza ya leo",
      en: "Record your first sale today"
    },
    action_add_products: {
      sw: "Ongeza bidhaa zaidi",
      en: "Add more products"
    },
    action_quick_sale: {
      sw: "Tumia 'mauzo haraka' kwa haraka",
      en: "Use 'quick sale' for speed"
    },
    action_add_customers: {
      sw: "Ongeza taarifa za wateja",
      en: "Add customer information"
    },
    action_view_products: {
      sw: "Angalia orodha ya bidhaa",
      en: "View product list"
    },
    action_record_more_sales: {
      sw: "Endelea kurekodi mauzo",
      en: "Continue recording sales"
    },
    action_check_stock: {
      sw: "Angalia hisa za bidhaa",
      en: "Check product stock"
    },
    action_weekly_report: {
      sw: "Ona ripoti ya wiki",
      en: "View weekly report"
    }
  }
};

/**
 * Command mappings for different languages
 */
const commands = {
  // Help and navigation
  help: ['msaada', 'help', 'usaidizi', 'maelekezo'],
  start: ['anza', 'start', 'mwanzo'],
  menu: ['menyu', 'menu', 'orodha', 'main menu'],
  back: ['rudi', 'back', 'nyuma', 'kurudi'],
  cancel: ['ghairi', 'cancel', 'acha', 'sitisha'],
  retry: ['jaribu tena', 'retry', 'rudia', 'try again'],

  // Business management
  register: ['sajili', 'register', 'usajili'],
  business_info: ['maelezo', 'info', 'business info', 'habari'],

  // Product management
  add_product: ['ongeza bidhaa', 'add product', 'bidhaa mpya', 'new product'],
  list_products: ['bidhaa', 'products', 'orodha bidhaa', 'list products'],
  edit_product: ['hariri bidhaa', 'edit product', 'badilisha bidhaa', 'update product'],
  delete_product: ['futa bidhaa', 'delete product', 'ondoa bidhaa', 'remove product'],
  search_product: ['tafuta', 'search', 'pata'],
  stock_check: ['hisa', 'stock', 'angalia hisa', 'check stock'],

  // Sales management
  record_sale: ['uza', 'sell', 'rekodi mauzo', 'record sale', 'mauzo'],
  quick_sale: ['mauzo haraka', 'quick sale', 'haraka', 'quick'],
  repeat_sale: ['rudia mauzo', 'repeat sale', 'rudia', 'repeat'],
  top_products: ['bidhaa bora', 'top products', 'bora', 'top'],
  daily_report: ['ripoti ya leo', 'today report', 'leo', 'today'],
  weekly_report: ['ripoti ya wiki', 'weekly report', 'wiki', 'weekly'],
  monthly_report: ['ripoti ya mwezi', 'monthly report', 'mwezi', 'monthly'],

  // Customer management
  add_customer: ['ongeza mteja', 'add customer', 'mteja mpya', 'new customer'],
  list_customers: ['wateja', 'customers', 'orodha wateja', 'list customers'],

  // Settings
  settings: ['mipangilio', 'settings', 'marekebisho'],
  language: ['lugha', 'language', 'badilisha lugha', 'change language'],

  // Payment methods
  cash: ['fedha taslimu', 'cash', 'taslimu'],
  mpesa: ['mpesa', 'm-pesa', 'em pesa'],
  tigo: ['tigo pesa', 'tigo', 'tigopesa'],
  airtel: ['airtel money', 'airtel', 'airtelmoney'],
  bank: ['benki', 'bank', 'akaunti']
};

/**
 * Get translated message
 */
function getMessage(key, language = 'sw', params = {}) {
  const keys = key.split('.');
  let message = messages;

  for (const k of keys) {
    if (message[k]) {
      message = message[k];
    } else {
      return `Translation not found: ${key}`;
    }
  }

  if (typeof message === 'object' && message[language]) {
    message = message[language];
  } else if (typeof message === 'object') {
    message = message.sw || message.en || 'Translation not found';
  }

  // Replace parameters
  if (typeof message === 'string' && params) {
    for (const [param, value] of Object.entries(params)) {
      message = message.replace(new RegExp(`{${param}}`, 'g'), value);
    }
  }

  return message;
}

/**
 * Detect command from user input
 */
function detectCommand(input, language = 'sw') {
  const normalizedInput = input.toLowerCase().trim();

  for (const [command, variations] of Object.entries(commands)) {
    for (const variation of variations) {
      if (normalizedInput.includes(variation.toLowerCase())) {
        return command;
      }
    }
  }

  return null;
}

/**
 * Get user's preferred language from various sources
 */
function detectLanguage(req) {
  // Check user preferences
  if (req.user?.preferences?.language) {
    return req.user.preferences.language;
  }

  // Check business settings
  if (req.business?.settings?.language) {
    return req.business.settings.language;
  }

  // Check Accept-Language header
  const acceptLanguage = req.headers['accept-language'];
  if (acceptLanguage && acceptLanguage.includes('sw')) {
    return 'sw';
  }

  // Default to Swahili
  return 'sw';
}

/**
 * Format currency in Tanzanian Shillings
 */
function formatCurrency(amount, language = 'sw') {
  const formatted = new Intl.NumberFormat('en-US').format(amount);
  return `TSh ${formatted}`;
}

/**
 * Format date in local format
 */
function formatDate(date, language = 'sw') {
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'Africa/Dar_es_Salaam'
  };

  if (language === 'sw') {
    // Swahili month names
    const swahiliMonths = [
      'Januari', 'Februari', 'Machi', 'Aprili', 'Mei', 'Juni',
      'Julai', 'Agosti', 'Septemba', 'Oktoba', 'Novemba', 'Desemba'
    ];

    const d = new Date(date);
    const day = d.getDate();
    const month = swahiliMonths[d.getMonth()];
    const year = d.getFullYear();

    return `${day} ${month} ${year}`;
  }

  return new Intl.DateTimeFormat('en-US', options).format(new Date(date));
}

module.exports = {
  messages,
  commands,
  getMessage,
  detectCommand,
  detectLanguage,
  formatCurrency,
  formatDate
};
