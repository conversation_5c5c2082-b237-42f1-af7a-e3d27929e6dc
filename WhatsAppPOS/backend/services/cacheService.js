/**
 * Cache Service
 * Redis-based caching for frequently accessed data and API responses
 */

const redis = require('redis');
const logger = require('../config/logger');

class CacheService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.defaultTTL = parseInt(process.env.CACHE_TTL) || 300; // 5 minutes
    this.maxKeys = parseInt(process.env.CACHE_MAX_KEYS) || 1000;
    this.enabled = process.env.CACHE_ENABLED === 'true';
    
    if (this.enabled) {
      this.initialize();
    }
  }

  /**
   * Initialize Redis connection
   */
  async initialize() {
    try {
      if (!process.env.REDIS_URL) {
        logger.warn('Redis URL not configured - caching disabled');
        this.enabled = false;
        return;
      }

      this.client = redis.createClient({
        url: process.env.REDIS_URL,
        password: process.env.REDIS_PASSWORD,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true
      });

      this.client.on('connect', () => {
        logger.info('Cache service connecting to Redis...');
      });

      this.client.on('ready', () => {
        this.isConnected = true;
        logger.info('Cache service connected to Redis successfully');
      });

      this.client.on('error', (error) => {
        this.isConnected = false;
        logger.error('Cache service Redis error:', error);
      });

      this.client.on('end', () => {
        this.isConnected = false;
        logger.warn('Cache service Redis connection ended');
      });

      await this.client.connect();

    } catch (error) {
      this.isConnected = false;
      this.enabled = false;
      logger.error('Failed to initialize cache service:', error);
    }
  }

  /**
   * Check if cache is available
   */
  isAvailable() {
    return this.enabled && this.isConnected && this.client;
  }

  /**
   * Generate cache key with business isolation
   */
  generateKey(businessId, key) {
    return `whatsapp_pos:${businessId}:${key}`;
  }

  /**
   * Set cache value
   */
  async set(businessId, key, value, ttl = null) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const cacheKey = this.generateKey(businessId, key);
      const serializedValue = JSON.stringify({
        data: value,
        timestamp: Date.now(),
        businessId
      });

      const expiry = ttl || this.defaultTTL;
      await this.client.setEx(cacheKey, expiry, serializedValue);

      logger.debug('Cache set', { businessId, key, ttl: expiry });
      return true;

    } catch (error) {
      logger.error('Cache set failed:', { businessId, key, error: error.message });
      return false;
    }
  }

  /**
   * Get cache value
   */
  async get(businessId, key) {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const cacheKey = this.generateKey(businessId, key);
      const cachedValue = await this.client.get(cacheKey);

      if (!cachedValue) {
        logger.debug('Cache miss', { businessId, key });
        return null;
      }

      const parsed = JSON.parse(cachedValue);
      
      // Verify business isolation
      if (parsed.businessId !== businessId) {
        logger.warn('Cache business mismatch', { 
          businessId, 
          cachedBusinessId: parsed.businessId, 
          key 
        });
        await this.delete(businessId, key);
        return null;
      }

      logger.debug('Cache hit', { businessId, key });
      return parsed.data;

    } catch (error) {
      logger.error('Cache get failed:', { businessId, key, error: error.message });
      return null;
    }
  }

  /**
   * Delete cache value
   */
  async delete(businessId, key) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const cacheKey = this.generateKey(businessId, key);
      const result = await this.client.del(cacheKey);
      
      logger.debug('Cache delete', { businessId, key, deleted: result > 0 });
      return result > 0;

    } catch (error) {
      logger.error('Cache delete failed:', { businessId, key, error: error.message });
      return false;
    }
  }

  /**
   * Delete multiple cache keys by pattern
   */
  async deletePattern(businessId, pattern) {
    if (!this.isAvailable()) {
      return 0;
    }

    try {
      const searchPattern = this.generateKey(businessId, pattern);
      const keys = await this.client.keys(searchPattern);
      
      if (keys.length === 0) {
        return 0;
      }

      const result = await this.client.del(keys);
      logger.debug('Cache pattern delete', { businessId, pattern, deleted: result });
      return result;

    } catch (error) {
      logger.error('Cache pattern delete failed:', { businessId, pattern, error: error.message });
      return 0;
    }
  }

  /**
   * Clear all cache for a business
   */
  async clearBusiness(businessId) {
    return await this.deletePattern(businessId, '*');
  }

  /**
   * Get or set cache value (cache-aside pattern)
   */
  async getOrSet(businessId, key, fetchFunction, ttl = null) {
    // Try to get from cache first
    const cached = await this.get(businessId, key);
    if (cached !== null) {
      return cached;
    }

    // Cache miss - fetch data
    try {
      const data = await fetchFunction();
      
      // Store in cache for next time
      await this.set(businessId, key, data, ttl);
      
      return data;
    } catch (error) {
      logger.error('Cache getOrSet fetch failed:', { businessId, key, error: error.message });
      throw error;
    }
  }

  /**
   * Increment counter in cache
   */
  async increment(businessId, key, amount = 1, ttl = null) {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const cacheKey = this.generateKey(businessId, key);
      const result = await this.client.incrBy(cacheKey, amount);
      
      // Set expiry if this is a new key
      if (result === amount && ttl) {
        await this.client.expire(cacheKey, ttl);
      }

      return result;

    } catch (error) {
      logger.error('Cache increment failed:', { businessId, key, error: error.message });
      return null;
    }
  }

  /**
   * Get cache statistics
   */
  async getStats() {
    if (!this.isAvailable()) {
      return {
        enabled: false,
        connected: false,
        message: 'Cache service not available'
      };
    }

    try {
      const info = await this.client.info('memory');
      const keyCount = await this.client.dbSize();
      
      return {
        enabled: this.enabled,
        connected: this.isConnected,
        keyCount,
        maxKeys: this.maxKeys,
        defaultTTL: this.defaultTTL,
        memoryInfo: this.parseRedisInfo(info)
      };

    } catch (error) {
      logger.error('Failed to get cache stats:', error);
      return {
        enabled: this.enabled,
        connected: false,
        error: error.message
      };
    }
  }

  /**
   * Parse Redis INFO output
   */
  parseRedisInfo(info) {
    const lines = info.split('\r\n');
    const result = {};
    
    lines.forEach(line => {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = value;
      }
    });
    
    return result;
  }

  /**
   * Health check
   */
  async healthCheck() {
    if (!this.isAvailable()) {
      return {
        status: 'unhealthy',
        message: 'Cache service not available'
      };
    }

    try {
      const testKey = 'health_check_' + Date.now();
      await this.client.setEx(testKey, 10, 'test');
      const result = await this.client.get(testKey);
      await this.client.del(testKey);
      
      return {
        status: result === 'test' ? 'healthy' : 'unhealthy',
        message: 'Cache service operational',
        latency: Date.now()
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: error.message
      };
    }
  }

  /**
   * Close connection
   */
  async close() {
    if (this.client) {
      await this.client.quit();
      this.isConnected = false;
      logger.info('Cache service connection closed');
    }
  }
}

// Create singleton instance
const cacheService = new CacheService();

module.exports = cacheService;
