/**
 * Log Aggregation Service
 * Provides log analysis, search, and aggregation capabilities for production monitoring
 */

const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');
const logger = require('../config/logger');

class LogAggregationService {
  constructor() {
    this.logsDir = path.join(__dirname, '../../logs');
    this.logFiles = {
      app: 'app.log',
      error: 'error.log',
      security: 'security.log',
      performance: 'performance.log',
      whatsapp: 'whatsapp.log',
      exceptions: 'exceptions.log',
      rejections: 'rejections.log'
    };
  }

  /**
   * Search logs by criteria
   */
  async searchLogs(criteria = {}) {
    const {
      logType = 'app',
      level,
      startDate,
      endDate,
      userId,
      businessId,
      message,
      limit = 100,
      offset = 0
    } = criteria;

    try {
      const logFile = path.join(this.logsDir, this.logFiles[logType]);
      const results = [];
      
      // Check if log file exists
      try {
        await fs.access(logFile);
      } catch (error) {
        return { results: [], total: 0, message: `Log file ${logType} not found` };
      }

      const fileStream = require('fs').createReadStream(logFile);
      const rl = readline.createInterface({
        input: fileStream,
        crlfDelay: Infinity
      });

      let lineCount = 0;
      let matchCount = 0;

      for await (const line of rl) {
        try {
          const logEntry = JSON.parse(line);
          
          // Apply filters
          if (level && logEntry.level !== level.toUpperCase()) continue;
          if (userId && logEntry.userId !== userId) continue;
          if (businessId && logEntry.businessId !== businessId) continue;
          if (message && !logEntry.message.toLowerCase().includes(message.toLowerCase())) continue;
          
          // Date filtering
          if (startDate || endDate) {
            const logDate = new Date(logEntry.timestamp);
            if (startDate && logDate < new Date(startDate)) continue;
            if (endDate && logDate > new Date(endDate)) continue;
          }

          matchCount++;
          
          // Apply pagination
          if (matchCount > offset && results.length < limit) {
            results.push(logEntry);
          }

          lineCount++;
        } catch (parseError) {
          // Skip invalid JSON lines
          continue;
        }
      }

      return {
        results,
        total: matchCount,
        scanned: lineCount,
        logType,
        criteria
      };

    } catch (error) {
      logger.error('Log search failed:', error);
      throw error;
    }
  }

  /**
   * Get log statistics
   */
  async getLogStatistics(timeRange = '24h') {
    try {
      const stats = {
        timeRange,
        timestamp: new Date().toISOString(),
        files: {},
        summary: {
          totalEntries: 0,
          errorCount: 0,
          warningCount: 0,
          infoCount: 0,
          debugCount: 0,
          uniqueUsers: new Set(),
          uniqueBusinesses: new Set(),
          topErrors: {},
          performanceMetrics: {
            slowRequests: 0,
            avgResponseTime: 0,
            totalRequests: 0
          }
        }
      };

      // Calculate time threshold
      const timeThreshold = this.getTimeThreshold(timeRange);

      // Process each log file
      for (const [type, filename] of Object.entries(this.logFiles)) {
        const filePath = path.join(this.logsDir, filename);
        
        try {
          await fs.access(filePath);
          const fileStats = await this.analyzeLogFile(filePath, timeThreshold);
          stats.files[type] = fileStats;
          
          // Aggregate summary statistics
          stats.summary.totalEntries += fileStats.entryCount;
          stats.summary.errorCount += fileStats.levels.ERROR || 0;
          stats.summary.warningCount += fileStats.levels.WARN || 0;
          stats.summary.infoCount += fileStats.levels.INFO || 0;
          stats.summary.debugCount += fileStats.levels.DEBUG || 0;
          
          // Collect unique users and businesses
          fileStats.uniqueUsers.forEach(user => stats.summary.uniqueUsers.add(user));
          fileStats.uniqueBusinesses.forEach(business => stats.summary.uniqueBusinesses.add(business));
          
          // Aggregate top errors
          Object.entries(fileStats.topErrors).forEach(([error, count]) => {
            stats.summary.topErrors[error] = (stats.summary.topErrors[error] || 0) + count;
          });

        } catch (error) {
          stats.files[type] = { error: 'File not accessible', message: error.message };
        }
      }

      // Convert Sets to counts
      stats.summary.uniqueUsers = stats.summary.uniqueUsers.size;
      stats.summary.uniqueBusinesses = stats.summary.uniqueBusinesses.size;

      return stats;

    } catch (error) {
      logger.error('Failed to get log statistics:', error);
      throw error;
    }
  }

  /**
   * Analyze a single log file
   */
  async analyzeLogFile(filePath, timeThreshold) {
    const fileStream = require('fs').createReadStream(filePath);
    const rl = readline.createInterface({
      input: fileStream,
      crlfDelay: Infinity
    });

    const analysis = {
      entryCount: 0,
      levels: {},
      uniqueUsers: new Set(),
      uniqueBusinesses: new Set(),
      topErrors: {},
      timeRange: {
        oldest: null,
        newest: null
      },
      categories: {},
      responseTimeSum: 0,
      responseTimeCount: 0,
      slowRequestCount: 0
    };

    for await (const line of rl) {
      try {
        const logEntry = JSON.parse(line);
        const logDate = new Date(logEntry.timestamp);
        
        // Skip entries outside time range
        if (timeThreshold && logDate < timeThreshold) continue;

        analysis.entryCount++;
        
        // Track levels
        analysis.levels[logEntry.level] = (analysis.levels[logEntry.level] || 0) + 1;
        
        // Track users and businesses
        if (logEntry.userId) analysis.uniqueUsers.add(logEntry.userId);
        if (logEntry.businessId) analysis.uniqueBusinesses.add(logEntry.businessId);
        
        // Track categories
        if (logEntry.category) {
          analysis.categories[logEntry.category] = (analysis.categories[logEntry.category] || 0) + 1;
        }
        
        // Track time range
        if (!analysis.timeRange.oldest || logDate < analysis.timeRange.oldest) {
          analysis.timeRange.oldest = logDate;
        }
        if (!analysis.timeRange.newest || logDate > analysis.timeRange.newest) {
          analysis.timeRange.newest = logDate;
        }
        
        // Track errors
        if (logEntry.level === 'ERROR' && logEntry.message) {
          const errorKey = logEntry.message.substring(0, 100); // First 100 chars
          analysis.topErrors[errorKey] = (analysis.topErrors[errorKey] || 0) + 1;
        }
        
        // Track performance metrics
        if (logEntry.meta && logEntry.meta.duration) {
          analysis.responseTimeSum += logEntry.meta.duration;
          analysis.responseTimeCount++;
          
          if (logEntry.meta.duration > 2000) { // Slow requests > 2s
            analysis.slowRequestCount++;
          }
        }

      } catch (parseError) {
        // Skip invalid JSON lines
        continue;
      }
    }

    // Calculate averages
    if (analysis.responseTimeCount > 0) {
      analysis.avgResponseTime = analysis.responseTimeSum / analysis.responseTimeCount;
    }

    return analysis;
  }

  /**
   * Get time threshold based on range
   */
  getTimeThreshold(timeRange) {
    const now = new Date();
    
    switch (timeRange) {
      case '1h':
        return new Date(now.getTime() - 60 * 60 * 1000);
      case '6h':
        return new Date(now.getTime() - 6 * 60 * 60 * 1000);
      case '24h':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      default:
        return null; // No time filtering
    }
  }

  /**
   * Get recent errors
   */
  async getRecentErrors(limit = 50) {
    try {
      const errorLogs = await this.searchLogs({
        logType: 'error',
        level: 'ERROR',
        limit
      });

      return errorLogs.results.map(log => ({
        timestamp: log.timestamp,
        message: log.message,
        stack: log.stack,
        userId: log.userId,
        businessId: log.businessId,
        requestId: log.requestId,
        meta: log.meta
      }));

    } catch (error) {
      logger.error('Failed to get recent errors:', error);
      throw error;
    }
  }

  /**
   * Get performance insights
   */
  async getPerformanceInsights(timeRange = '24h') {
    try {
      const performanceLogs = await this.searchLogs({
        logType: 'performance',
        limit: 1000
      });

      const insights = {
        timeRange,
        totalRequests: performanceLogs.total,
        slowRequests: 0,
        avgResponseTime: 0,
        responseTimeDistribution: {
          fast: 0,    // < 500ms
          medium: 0,  // 500ms - 2s
          slow: 0,    // 2s - 5s
          verySlow: 0 // > 5s
        },
        topSlowEndpoints: {},
        errorRate: 0
      };

      let totalDuration = 0;
      let requestCount = 0;

      performanceLogs.results.forEach(log => {
        if (log.meta && log.meta.duration) {
          const duration = log.meta.duration;
          totalDuration += duration;
          requestCount++;

          if (duration > 2000) insights.slowRequests++;

          // Categorize response times
          if (duration < 500) {
            insights.responseTimeDistribution.fast++;
          } else if (duration < 2000) {
            insights.responseTimeDistribution.medium++;
          } else if (duration < 5000) {
            insights.responseTimeDistribution.slow++;
          } else {
            insights.responseTimeDistribution.verySlow++;
          }

          // Track slow endpoints
          if (duration > 2000 && log.meta.url) {
            const endpoint = log.meta.url;
            insights.topSlowEndpoints[endpoint] = (insights.topSlowEndpoints[endpoint] || 0) + 1;
          }
        }
      });

      if (requestCount > 0) {
        insights.avgResponseTime = totalDuration / requestCount;
      }

      return insights;

    } catch (error) {
      logger.error('Failed to get performance insights:', error);
      throw error;
    }
  }

  /**
   * Export logs for external analysis
   */
  async exportLogs(criteria = {}, format = 'json') {
    try {
      const logs = await this.searchLogs({ ...criteria, limit: 10000 });
      
      if (format === 'csv') {
        const csvHeader = 'timestamp,level,message,userId,businessId,type,category\n';
        const csvRows = logs.results.map(log => 
          `"${log.timestamp}","${log.level}","${log.message.replace(/"/g, '""')}","${log.userId || ''}","${log.businessId || ''}","${log.type || ''}","${log.category || ''}"`
        ).join('\n');
        
        return csvHeader + csvRows;
      }
      
      return JSON.stringify(logs, null, 2);

    } catch (error) {
      logger.error('Failed to export logs:', error);
      throw error;
    }
  }
}

module.exports = LogAggregationService;
