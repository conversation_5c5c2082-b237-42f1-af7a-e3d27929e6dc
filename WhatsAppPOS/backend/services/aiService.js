/**
 * AI Service for WhatsApp POS Tanzania
 * Integrates OpenRouter API for natural language understanding and intent recognition
 */

const axios = require('axios');
const logger = require('../config/logger');

class AIService {
  constructor() {
    this.openRouterApiKey = process.env.OPENROUTER_API_KEY;
    this.baseUrl = 'https://openrouter.ai/api/v1';
    this.defaultModel = 'anthropic/claude-3-haiku';
    this.fallbackModel = 'openai/gpt-3.5-turbo';
    this.maxRetries = 2;
    this.requestTimeout = 5000; // 5 seconds
    
    // Cache for frequent queries
    this.intentCache = new Map();
    this.cacheExpiry = 300000; // 5 minutes
    
    // Cost optimization settings
    this.maxTokens = 150;
    this.temperature = 0.3;
    
    this.initializeService();
  }

  /**
   * Initialize AI service and validate configuration
   */
  initializeService() {
    if (!this.openRouterApiKey) {
      logger.warn('OpenRouter API key not configured - AI features disabled');
      this.enabled = false;
      return;
    }
    
    this.enabled = true;
    logger.info('AI Service initialized with OpenRouter integration');
  }

  /**
   * Main entry point for AI-enhanced message processing
   */
  async enhanceMessageProcessing(messageBody, context = {}) {
    if (!this.enabled) {
      return { enhanced: false, originalMessage: messageBody };
    }

    try {
      const startTime = Date.now();
      
      // Check cache first
      const cacheKey = this.generateCacheKey(messageBody, context);
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        logger.debug('AI response served from cache');
        return cached;
      }

      // Process with AI
      const result = await this.processWithAI(messageBody, context);
      
      // Cache successful results
      if (result.enhanced) {
        this.setCache(cacheKey, result);
      }
      
      const processingTime = Date.now() - startTime;
      logger.info(`AI processing completed in ${processingTime}ms`);
      
      return result;
      
    } catch (error) {
      logger.error('AI processing error:', error);
      return { enhanced: false, originalMessage: messageBody, error: error.message };
    }
  }

  /**
   * Process message with AI for intent recognition and enhancement
   */
  async processWithAI(messageBody, context) {
    const prompt = this.buildPrompt(messageBody, context);
    
    try {
      // Try primary model first
      const response = await this.callOpenRouter(prompt, this.defaultModel);
      return this.parseAIResponse(response, messageBody);
      
    } catch (error) {
      logger.warn(`Primary model failed, trying fallback: ${error.message}`);
      
      try {
        // Fallback to secondary model
        const response = await this.callOpenRouter(prompt, this.fallbackModel);
        return this.parseAIResponse(response, messageBody);
        
      } catch (fallbackError) {
        logger.error('Both AI models failed:', fallbackError);
        throw fallbackError;
      }
    }
  }

  /**
   * Build AI prompt based on message and context
   */
  buildPrompt(messageBody, context) {
    const { currentStep, language = 'sw', businessContext = {} } = context;
    
    return `You are an AI assistant for a WhatsApp-based POS system in Tanzania. 
Your task is to understand user intent and correct/enhance their messages.

CONTEXT:
- Current conversation step: ${currentStep || 'welcome'}
- Language: ${language} (sw=Swahili, en=English)
- User message: "${messageBody}"
- Business context: ${JSON.stringify(businessContext)}

AVAILABLE COMMANDS:
Sales: record_sale, quick_sale, repeat_sale, top_products
Products: add_product, list_products, edit_product, delete_product, stock_check
Customers: add_customer, list_customers
Reports: daily_report, weekly_report, business_info
Navigation: help, menu, back, cancel

TASK:
1. Identify the user's intent from their message
2. Correct any typos or grammatical errors
3. Handle Swahili-English code-switching
4. Extract relevant data (quantities, prices, names)
5. Provide the most likely command or action

Respond in JSON format:
{
  "intent": "detected_command_or_action",
  "confidence": 0.0-1.0,
  "correctedMessage": "cleaned up message",
  "extractedData": {
    "quantity": number_or_null,
    "price": number_or_null,
    "productName": "string_or_null",
    "customerName": "string_or_null"
  },
  "suggestions": ["alternative1", "alternative2"],
  "needsClarification": boolean,
  "clarificationQuestion": "question_if_needed"
}`;
  }

  /**
   * Call OpenRouter API with specified model
   */
  async callOpenRouter(prompt, model) {
    const requestData = {
      model: model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: this.maxTokens,
      temperature: this.temperature,
      top_p: 0.9
    };

    const response = await axios.post(
      `${this.baseUrl}/chat/completions`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${this.openRouterApiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://whatsapp-pos-tanzania.com',
          'X-Title': 'WhatsApp POS Tanzania'
        },
        timeout: this.requestTimeout
      }
    );

    return response.data.choices[0].message.content;
  }

  /**
   * Parse AI response and structure result
   */
  parseAIResponse(aiResponse, originalMessage) {
    try {
      const parsed = JSON.parse(aiResponse);
      
      return {
        enhanced: true,
        originalMessage,
        intent: parsed.intent,
        confidence: parsed.confidence || 0.5,
        correctedMessage: parsed.correctedMessage || originalMessage,
        extractedData: parsed.extractedData || {},
        suggestions: parsed.suggestions || [],
        needsClarification: parsed.needsClarification || false,
        clarificationQuestion: parsed.clarificationQuestion || null
      };
      
    } catch (parseError) {
      logger.error('Failed to parse AI response:', parseError);
      
      // Fallback: try to extract intent from raw response
      const intent = this.extractIntentFromText(aiResponse);
      
      return {
        enhanced: !!intent,
        originalMessage,
        intent: intent || null,
        confidence: intent ? 0.3 : 0,
        correctedMessage: originalMessage,
        extractedData: {},
        suggestions: [],
        needsClarification: false
      };
    }
  }

  /**
   * Extract intent from unstructured AI response (fallback)
   */
  extractIntentFromText(text) {
    const commands = [
      'record_sale', 'quick_sale', 'repeat_sale', 'top_products',
      'add_product', 'list_products', 'edit_product', 'delete_product',
      'add_customer', 'list_customers', 'daily_report', 'weekly_report',
      'help', 'menu', 'back', 'cancel'
    ];
    
    const lowerText = text.toLowerCase();
    return commands.find(cmd => lowerText.includes(cmd.replace('_', ' ')));
  }

  /**
   * Cache management
   */
  generateCacheKey(message, context) {
    return `${message.toLowerCase().trim()}_${context.currentStep || 'welcome'}_${context.language || 'sw'}`;
  }

  getFromCache(key) {
    const cached = this.intentCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    this.intentCache.delete(key);
    return null;
  }

  setCache(key, data) {
    this.intentCache.set(key, {
      data,
      timestamp: Date.now()
    });
    
    // Clean old cache entries
    if (this.intentCache.size > 1000) {
      const oldestKeys = Array.from(this.intentCache.keys()).slice(0, 100);
      oldestKeys.forEach(key => this.intentCache.delete(key));
    }
  }

  /**
   * Smart product search with fuzzy matching
   */
  async smartProductSearch(query, products, language = 'sw') {
    if (!this.enabled || !products.length) {
      return this.fallbackProductSearch(query, products);
    }

    try {
      const prompt = `Find the best matching product from this list for the query: "${query}"

Products available:
${products.map((p, i) => `${i + 1}. ${p.name} - ${p.price} TZS`).join('\n')}

Respond with JSON:
{
  "matches": [
    {
      "productIndex": number,
      "confidence": 0.0-1.0,
      "reason": "why this matches"
    }
  ],
  "noMatch": boolean
}`;

      const response = await this.callOpenRouter(prompt, this.defaultModel);
      const parsed = JSON.parse(response);
      
      if (parsed.noMatch || !parsed.matches.length) {
        return { found: false, suggestions: [] };
      }
      
      const bestMatch = parsed.matches[0];
      if (bestMatch.confidence > 0.6) {
        return {
          found: true,
          product: products[bestMatch.productIndex],
          confidence: bestMatch.confidence
        };
      }
      
      return {
        found: false,
        suggestions: parsed.matches
          .filter(m => m.confidence > 0.3)
          .map(m => products[m.productIndex])
          .slice(0, 3)
      };
      
    } catch (error) {
      logger.error('AI product search failed:', error);
      return this.fallbackProductSearch(query, products);
    }
  }

  /**
   * Fallback product search without AI
   */
  fallbackProductSearch(query, products) {
    const lowerQuery = query.toLowerCase();
    
    // Exact match
    const exactMatch = products.find(p => 
      p.name.toLowerCase() === lowerQuery
    );
    if (exactMatch) {
      return { found: true, product: exactMatch, confidence: 1.0 };
    }
    
    // Partial match
    const partialMatches = products.filter(p =>
      p.name.toLowerCase().includes(lowerQuery) ||
      lowerQuery.includes(p.name.toLowerCase())
    );
    
    if (partialMatches.length === 1) {
      return { found: true, product: partialMatches[0], confidence: 0.8 };
    }
    
    return {
      found: false,
      suggestions: partialMatches.slice(0, 3)
    };
  }

  /**
   * Extract quantity and price from natural language
   */
  async extractQuantityAndPrice(message, language = 'sw') {
    if (!this.enabled) {
      return this.fallbackExtractNumbers(message);
    }

    try {
      const prompt = `Extract quantity and price from this message: "${message}"
Language: ${language}

Common patterns:
- "miwili" = 2, "mitatu" = 3, "minne" = 4, "mitano" = 5 (Swahili)
- "shilingi" = shillings, "elfu" = thousand

Respond with JSON:
{
  "quantity": number_or_null,
  "price": number_or_null,
  "currency": "TZS_or_other",
  "confidence": 0.0-1.0
}`;

      const response = await this.callOpenRouter(prompt, this.defaultModel);
      return JSON.parse(response);
      
    } catch (error) {
      logger.error('AI number extraction failed:', error);
      return this.fallbackExtractNumbers(message);
    }
  }

  /**
   * Fallback number extraction without AI
   */
  fallbackExtractNumbers(message) {
    const numbers = message.match(/\d+/g);
    if (!numbers) return { quantity: null, price: null, confidence: 0 };
    
    const nums = numbers.map(n => parseInt(n));
    
    // Heuristic: smaller number is likely quantity, larger is price
    if (nums.length === 1) {
      const num = nums[0];
      return {
        quantity: num < 100 ? num : null,
        price: num >= 100 ? num : null,
        confidence: 0.6
      };
    }
    
    if (nums.length >= 2) {
      const sorted = nums.sort((a, b) => a - b);
      return {
        quantity: sorted[0] < 100 ? sorted[0] : null,
        price: sorted[sorted.length - 1] >= 100 ? sorted[sorted.length - 1] : null,
        confidence: 0.7
      };
    }
    
    return { quantity: null, price: null, confidence: 0 };
  }

  /**
   * Get service health status
   */
  getHealthStatus() {
    return {
      enabled: this.enabled,
      cacheSize: this.intentCache.size,
      models: {
        primary: this.defaultModel,
        fallback: this.fallbackModel
      }
    };
  }
}

module.exports = new AIService();
