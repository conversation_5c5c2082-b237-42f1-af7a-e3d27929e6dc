/**
 * Automated Backup Service for MongoDB
 * Handles database backups, retention, and restoration
 */

const { exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const cron = require('node-cron');
const AWS = require('aws-sdk');
const logger = require('../config/logger');

class BackupService {
  constructor() {
    this.backupDir = process.env.BACKUP_DIR || path.join(__dirname, '../../backups');
    this.retentionDays = parseInt(process.env.BACKUP_RETENTION_DAYS) || 30;
    this.schedule = process.env.BACKUP_SCHEDULE || '0 2 * * *'; // Daily at 2 AM
    this.enabled = process.env.BACKUP_ENABLED === 'true';
    
    // AWS S3 configuration for remote backups
    this.s3 = null;
    if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
      this.s3 = new AWS.S3({
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        region: process.env.AWS_REGION || 'us-east-1'
      });
    }
    
    this.s3Bucket = process.env.BACKUP_S3_BUCKET;
  }

  /**
   * Initialize backup service
   */
  async initialize() {
    if (!this.enabled) {
      logger.info('Backup service is disabled');
      return;
    }

    try {
      // Create backup directory
      await this.ensureBackupDirectory();
      
      // Schedule automatic backups
      this.scheduleBackups();
      
      // Clean old backups on startup
      await this.cleanOldBackups();
      
      logger.info('Backup service initialized successfully', {
        schedule: this.schedule,
        retentionDays: this.retentionDays,
        s3Enabled: !!this.s3
      });

    } catch (error) {
      logger.error('Failed to initialize backup service:', error);
      throw error;
    }
  }

  /**
   * Ensure backup directory exists
   */
  async ensureBackupDirectory() {
    try {
      await fs.access(this.backupDir);
    } catch (error) {
      await fs.mkdir(this.backupDir, { recursive: true });
      logger.info(`Created backup directory: ${this.backupDir}`);
    }
  }

  /**
   * Create database backup
   */
  async createBackup(options = {}) {
    const startTime = Date.now();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `whatsapp-pos-backup-${timestamp}`;
    const backupPath = path.join(this.backupDir, backupName);

    try {
      logger.info('Starting database backup...', { backupName });

      // Create MongoDB dump
      await this.createMongoDump(backupPath);
      
      // Compress backup
      const compressedPath = await this.compressBackup(backupPath);
      
      // Upload to S3 if configured
      if (this.s3 && this.s3Bucket) {
        await this.uploadToS3(compressedPath, backupName);
      }
      
      // Clean up uncompressed backup
      await this.removeDirectory(backupPath);
      
      const duration = Date.now() - startTime;
      const stats = await fs.stat(compressedPath);
      
      logger.info('Backup completed successfully', {
        backupName,
        duration,
        size: stats.size,
        path: compressedPath
      });

      return {
        success: true,
        backupName,
        path: compressedPath,
        size: stats.size,
        duration
      };

    } catch (error) {
      logger.error('Backup failed:', error);
      
      // Clean up failed backup
      try {
        await this.removeDirectory(backupPath);
      } catch (cleanupError) {
        logger.error('Failed to clean up failed backup:', cleanupError);
      }
      
      throw error;
    }
  }

  /**
   * Create MongoDB dump
   */
  async createMongoDump(backupPath) {
    return new Promise((resolve, reject) => {
      const mongoUri = process.env.MONGODB_URI;
      const cmd = `mongodump --uri="${mongoUri}" --out="${backupPath}"`;
      
      exec(cmd, (error, stdout, stderr) => {
        if (error) {
          logger.error('MongoDB dump failed:', { error, stderr });
          reject(error);
        } else {
          logger.info('MongoDB dump completed', { stdout });
          resolve();
        }
      });
    });
  }

  /**
   * Compress backup directory
   */
  async compressBackup(backupPath) {
    return new Promise((resolve, reject) => {
      const compressedPath = `${backupPath}.tar.gz`;
      const cmd = `tar -czf "${compressedPath}" -C "${path.dirname(backupPath)}" "${path.basename(backupPath)}"`;
      
      exec(cmd, (error, stdout, stderr) => {
        if (error) {
          logger.error('Backup compression failed:', { error, stderr });
          reject(error);
        } else {
          logger.info('Backup compressed successfully', { compressedPath });
          resolve(compressedPath);
        }
      });
    });
  }

  /**
   * Upload backup to S3
   */
  async uploadToS3(filePath, backupName) {
    if (!this.s3 || !this.s3Bucket) {
      throw new Error('S3 not configured');
    }

    try {
      const fileContent = await fs.readFile(filePath);
      const key = `backups/${backupName}.tar.gz`;
      
      const params = {
        Bucket: this.s3Bucket,
        Key: key,
        Body: fileContent,
        StorageClass: 'STANDARD_IA', // Infrequent Access for cost optimization
        ServerSideEncryption: 'AES256'
      };

      const result = await this.s3.upload(params).promise();
      
      logger.info('Backup uploaded to S3', {
        bucket: this.s3Bucket,
        key,
        location: result.Location
      });

      return result;

    } catch (error) {
      logger.error('S3 upload failed:', error);
      throw error;
    }
  }

  /**
   * Schedule automatic backups
   */
  scheduleBackups() {
    if (!cron.validate(this.schedule)) {
      throw new Error(`Invalid cron schedule: ${this.schedule}`);
    }

    cron.schedule(this.schedule, async () => {
      try {
        logger.info('Starting scheduled backup...');
        await this.createBackup();
      } catch (error) {
        logger.error('Scheduled backup failed:', error);
      }
    });

    logger.info(`Backup scheduled: ${this.schedule}`);
  }

  /**
   * Clean old backups
   */
  async cleanOldBackups() {
    try {
      const files = await fs.readdir(this.backupDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);

      let deletedCount = 0;

      for (const file of files) {
        if (file.endsWith('.tar.gz')) {
          const filePath = path.join(this.backupDir, file);
          const stats = await fs.stat(filePath);
          
          if (stats.mtime < cutoffDate) {
            await fs.unlink(filePath);
            deletedCount++;
            logger.info(`Deleted old backup: ${file}`);
          }
        }
      }

      // Clean old S3 backups
      if (this.s3 && this.s3Bucket) {
        await this.cleanOldS3Backups(cutoffDate);
      }

      logger.info(`Cleanup completed: ${deletedCount} local backups deleted`);

    } catch (error) {
      logger.error('Backup cleanup failed:', error);
    }
  }

  /**
   * Clean old S3 backups
   */
  async cleanOldS3Backups(cutoffDate) {
    try {
      const params = {
        Bucket: this.s3Bucket,
        Prefix: 'backups/'
      };

      const objects = await this.s3.listObjectsV2(params).promise();
      const toDelete = objects.Contents.filter(obj => obj.LastModified < cutoffDate);

      if (toDelete.length > 0) {
        const deleteParams = {
          Bucket: this.s3Bucket,
          Delete: {
            Objects: toDelete.map(obj => ({ Key: obj.Key }))
          }
        };

        await this.s3.deleteObjects(deleteParams).promise();
        logger.info(`Deleted ${toDelete.length} old S3 backups`);
      }

    } catch (error) {
      logger.error('S3 cleanup failed:', error);
    }
  }

  /**
   * List available backups
   */
  async listBackups() {
    try {
      const localBackups = await this.listLocalBackups();
      const s3Backups = this.s3 ? await this.listS3Backups() : [];

      return {
        local: localBackups,
        s3: s3Backups
      };

    } catch (error) {
      logger.error('Failed to list backups:', error);
      throw error;
    }
  }

  /**
   * List local backups
   */
  async listLocalBackups() {
    const files = await fs.readdir(this.backupDir);
    const backups = [];

    for (const file of files) {
      if (file.endsWith('.tar.gz')) {
        const filePath = path.join(this.backupDir, file);
        const stats = await fs.stat(filePath);
        
        backups.push({
          name: file,
          path: filePath,
          size: stats.size,
          created: stats.mtime,
          type: 'local'
        });
      }
    }

    return backups.sort((a, b) => b.created - a.created);
  }

  /**
   * List S3 backups
   */
  async listS3Backups() {
    if (!this.s3 || !this.s3Bucket) {
      return [];
    }

    try {
      const params = {
        Bucket: this.s3Bucket,
        Prefix: 'backups/'
      };

      const objects = await this.s3.listObjectsV2(params).promise();
      
      return objects.Contents.map(obj => ({
        name: path.basename(obj.Key),
        key: obj.Key,
        size: obj.Size,
        created: obj.LastModified,
        type: 's3'
      })).sort((a, b) => b.created - a.created);

    } catch (error) {
      logger.error('Failed to list S3 backups:', error);
      return [];
    }
  }

  /**
   * Remove directory recursively
   */
  async removeDirectory(dirPath) {
    try {
      await fs.rm(dirPath, { recursive: true, force: true });
    } catch (error) {
      logger.error(`Failed to remove directory ${dirPath}:`, error);
    }
  }

  /**
   * Get backup service status
   */
  getStatus() {
    return {
      enabled: this.enabled,
      schedule: this.schedule,
      retentionDays: this.retentionDays,
      backupDir: this.backupDir,
      s3Configured: !!this.s3,
      s3Bucket: this.s3Bucket
    };
  }
}

module.exports = BackupService;
