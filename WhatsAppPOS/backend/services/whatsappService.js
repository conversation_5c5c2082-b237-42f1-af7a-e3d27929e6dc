/**
 * WhatsApp Service
 * Comprehensive WhatsApp bot functionality with conversation flow management
 */

const { sendWhatsAppMessage } = require('../config/twilio');
const { sessionManager } = require('../config/redis');
const { getMessage, detectCommand, detectLanguage, formatCurrency, formatDate } = require('../utils/translations');
const { Business, User, Product, Sale, Customer } = require('../models');
const logger = require('../config/logger');
const { monitorWhatsAppMessage, monitorDatabaseQuery, performanceMonitor } = require('../config/monitoring');
const aiService = require('./aiService');

/**
 * Send WhatsApp message with optimized retry logic for production performance
 */
async function sendWhatsAppMessageWithRetry(phoneNumber, message, maxRetries = 2) {
  const isDebugMode = process.env.WHATSAPP_DEBUG_MODE === 'true';
  const startTime = Date.now();

  // Fast-fail errors that shouldn't be retried
  const nonRetryableErrors = [
    'exceeded the 9 daily messages limit',
    'invalid phone number',
    'message too long',
    'invalid account'
  ];

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await sendWhatsAppMessage(phoneNumber, message);

      if (result.success) {
        if (attempt > 1) {
          logger.info(`Message sent on attempt ${attempt} (${Date.now() - startTime}ms)`, { phoneNumber });
        }
        return result;
      }

      // Handle daily limit with production-ready fallback
      if (result.error && result.error.includes('exceeded the 9 daily messages limit')) {
        logger.warn(`Twilio daily limit reached (${Date.now() - startTime}ms)`, {
          phoneNumber,
          messageLength: message.length,
          error: result.error
        });

        if (isDebugMode) {
          // Debug mode: Simulate successful sending for testing
          logger.info('Debug mode: Simulating successful send due to daily limit');
          return {
            success: true,
            messageSid: 'DEBUG_MODE_' + Date.now(),
            status: 'sent',
            debugMode: true
          };
        } else {
          // Production mode: Log for monitoring and return failure
          logger.error('PRODUCTION ALERT: Twilio daily limit reached - upgrade account required', {
            phoneNumber,
            timestamp: new Date().toISOString(),
            messageLength: message.length
          });

          // In production, you could:
          // 1. Store message in queue for later delivery
          // 2. Send email notification to admin
          // 3. Switch to backup messaging service
          // 4. Return user-friendly error message

          return {
            success: false,
            error: 'Service temporarily unavailable. Please try again later.',
            errorCode: 'DAILY_LIMIT_EXCEEDED',
            fastFail: true
          };
        }
      }

      // Fast-fail for other non-retryable errors
      const isNonRetryable = nonRetryableErrors.some(err =>
        result.error && result.error.toLowerCase().includes(err.toLowerCase())
      );

      if (isNonRetryable) {
        logger.warn(`Fast-fail for non-retryable error (${Date.now() - startTime}ms):`, {
          phoneNumber,
          error: result.error
        });
        return {
          success: false,
          error: result.error,
          fastFail: true
        };
      }

      // Only retry for network/temporary errors
      if (attempt < maxRetries) {
        // Reduced delay: 200ms, 500ms instead of 1s, 2s, 4s
        const delay = attempt === 1 ? 200 : 500;
        logger.warn(`Retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries}):`, {
          phoneNumber,
          error: result.error
        });
        await new Promise(resolve => setTimeout(resolve, delay));
      }

    } catch (error) {
      // Fast-fail for connection errors in debug mode
      if (isDebugMode && error.message.includes('getaddrinfo ENOTFOUND')) {
        logger.info(`Debug mode: Network error fast-fail (${Date.now() - startTime}ms)`, {
          phoneNumber,
          error: error.message
        });
        return {
          success: true,
          messageSid: 'DEBUG_MODE_NETWORK_' + Date.now(),
          status: 'sent',
          debugMode: true
        };
      }

      logger.error(`Attempt ${attempt} error (${Date.now() - startTime}ms):`, {
        phoneNumber,
        error: error.message
      });

      if (attempt === maxRetries) {
        return {
          success: false,
          error: `Failed after ${maxRetries} attempts: ${error.message}`
        };
      }

      // Quick retry for network errors
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }
  }

  return {
    success: false,
    error: `Failed after ${maxRetries} attempts`
  };
}

/**
 * Main WhatsApp message handler
 */
async function handleWhatsAppMessage(req, res) {
  const startTime = Date.now();

  try {
    const { From, Body, MessageSid } = req.body;
    let phoneNumber = From.replace('whatsapp:', '').trim();

    // Ensure phone number has + prefix for international format
    if (!phoneNumber.startsWith('+')) {
      phoneNumber = '+' + phoneNumber;
    }

    const messageBody = Body.trim();

    logger.whatsapp('Received WhatsApp message', {
      from: phoneNumber,
      body: messageBody,
      messageSid: MessageSid
    });

    // Track message received
    logger.whatsapp('Message received', {
      from: phoneNumber,
      body: messageBody,
      messageSid: MessageSid
    });

    // Get or create session
    let session = await sessionManager.getSession(phoneNumber);
    if (!session) {
      session = {
        phone: phoneNumber,
        step: 'welcome',
        language: 'sw',
        data: {}
      };
    }

    // Detect language preference from message content or use session language
    let language = session.language;

    // Check if user is explicitly switching language
    if (messageBody.toLowerCase().includes('english') || messageBody.toLowerCase().includes('en')) {
      language = 'en';
    } else if (messageBody.toLowerCase().includes('swahili') || messageBody.toLowerCase().includes('sw')) {
      language = 'sw';
    }

    session.language = language;

    // Process the message
    const response = await processMessage(phoneNumber, messageBody, session);

    // Send response with retry logic
    if (response) {
      const result = await sendWhatsAppMessageWithRetry(phoneNumber, response);

      // Track message sending
      logger.whatsapp('Message sent', {
        to: phoneNumber,
        duration: Date.now() - startTime,
        success: result.success
      });

      if (!result.success) {
        logger.error('Failed to send WhatsApp message after retries:', {
          phone: phoneNumber,
          error: result.error,
          response: response.substring(0, 100) + '...'
        });

        // Check if it's a daily limit error
        if (result.error && result.error.includes('exceeded the 9 daily messages limit')) {
          logger.warn('Twilio daily message limit reached - system in degraded mode', {
            phone: phoneNumber,
            timestamp: new Date().toISOString()
          });

          // In production, you might want to:
          // 1. Store the response in database for later delivery
          // 2. Send email notification to user
          // 3. Queue message for next day delivery
          // 4. Switch to backup messaging service
        }
      }
    }

    // Update session (optimized)
    await sessionManager.setSession(phoneNumber, session, 1800); // 30 minutes

    // Track total processing time and memory usage
    const processingTime = Date.now() - startTime;
    const memoryUsage = process.memoryUsage();

    if (processingTime > 1000) { // Reduced threshold from 2000ms to 1000ms
      logger.warn('Slow WhatsApp message processing', {
        phoneNumber,
        processingTime,
        messageLength: messageBody.length,
        memoryUsageMB: Math.round(memoryUsage.heapUsed / 1024 / 1024)
      });
    }

    // Memory leak detection
    if (memoryUsage.heapUsed > 100 * 1024 * 1024) { // 100MB threshold
      logger.warn('High memory usage detected', {
        heapUsedMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotalMB: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        rssMB: Math.round(memoryUsage.rss / 1024 / 1024)
      });

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
        logger.info('Forced garbage collection executed');
      }
    }

    res.status(200).send('OK');

  } catch (error) {
    logger.error('WhatsApp message handling error:', error);
    res.status(500).send('Error processing message');
  }
}

/**
 * Process incoming message based on current session state
 */
async function processMessage(phoneNumber, messageBody, session) {
  const language = session.language;

  logger.info(`Processing message: "${messageBody}" from ${phoneNumber}`);

  // AI-Enhanced Message Processing
  const aiContext = {
    currentStep: session.step,
    language: language,
    businessContext: session.data
  };

  const aiResult = await aiService.enhanceMessageProcessing(messageBody, aiContext);

  // Use AI-enhanced message if available
  const processedMessage = aiResult.enhanced ? aiResult.correctedMessage : messageBody;
  const detectedIntent = aiResult.enhanced ? aiResult.intent : null;

  // Log AI enhancement results
  if (aiResult.enhanced) {
    logger.info(`AI enhanced: "${messageBody}" → "${processedMessage}", Intent: ${detectedIntent}, Confidence: ${aiResult.confidence}`);

    // Store extracted data in session for later use
    if (aiResult.extractedData) {
      session.aiData = aiResult.extractedData;
    }
  }

  // Handle clarification requests from AI
  if (aiResult.needsClarification && aiResult.clarificationQuestion) {
    let clarificationResponse = aiResult.clarificationQuestion;

    if (aiResult.suggestions.length > 0) {
      clarificationResponse += '\n\n' + getMessage('ai.suggestions', language) + '\n';
      clarificationResponse += aiResult.suggestions.map((s, i) => `${i + 1}. ${s}`).join('\n');
    }

    return clarificationResponse;
  }

  // Detect command from processed message or AI intent
  const globalCommand = detectedIntent || detectCommand(processedMessage, language);
  logger.info(`Detected command: "${globalCommand}", Language: ${language}, AI Enhanced: ${aiResult.enhanced}`);

  // Handle global commands first (work in any context)
  if (globalCommand === 'help' || globalCommand === 'msaada') {
    logger.info('Returning help message');
    session.step = 'welcome';
    session.data = {};
    return getHelpMessage(language);
  }

  if (globalCommand === 'cancel' || globalCommand === 'ghairi') {
    logger.info('Cancelling current operation');
    session.step = 'welcome';
    session.data = {};
    return getMessage('common.cancel', language) + '\n\n' + getMainMenu(language);
  }

  if (globalCommand === 'back' || globalCommand === 'rudi') {
    logger.info('Going back to previous step');
    return handleBackCommand(session, language);
  }

  if (globalCommand === 'menu' || globalCommand === 'menyu') {
    logger.info('Returning to main menu');
    session.step = 'welcome';
    session.data = {};
    return getMainMenu(language);
  }

  // Handle language switching
  if (messageBody.toLowerCase() === 'english' || messageBody === '🇬🇧' || messageBody === 'en') {
    session.language = 'en';
    session.step = 'welcome'; // Reset to welcome
    return getMessage('language.switched_to_english', 'en') + '\n\n' + getMainMenu('en');
  }

  if (messageBody.toLowerCase() === 'swahili' || messageBody === '🇹🇿' || messageBody === 'sw') {
    session.language = 'sw';
    session.step = 'welcome'; // Reset to welcome
    return getMessage('language.switched_to_swahili', 'sw') + '\n\n' + getMainMenu('sw');
  }

  // Handle numbered menu options contextually
  const input = messageBody.trim();

  // Only process global menu options if we're in welcome step or no active flow
  const isInActiveFlow = ['add_product', 'record_sale', 'add_customer', 'registration'].includes(session.step);

  let globalMenuCommand = null;
  if (!isInActiveFlow && /^[0-9]$/.test(input)) {
    const menuOptions = {
      '1': 'record_sale',
      '2': 'add_product',
      '3': 'list_products',
      '4': 'add_customer',
      '5': 'list_customers',
      '6': 'daily_report',
      '7': 'weekly_report',
      '8': 'stock_check',
      '9': 'business_info',
      '0': 'help'
    };

    globalMenuCommand = menuOptions[input];
    if (globalMenuCommand) {
      logger.info(`Detected global menu option: ${input} -> ${globalMenuCommand}`);
      // Don't reset session.step here - let the welcome handler manage it
    }
  }

  // Check if user has a registered business (with optimized timeout handling)
  let business = null;
  const dbStartTime = Date.now();

  try {
    business = await Promise.race([
      Business.findByPhone(phoneNumber),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database timeout')), 2000) // Reduced from 5s to 2s
      )
    ]);

    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Business lookup completed', { duration: dbQueryTime, success: true });

    logger.info(`Business lookup: ${business ? 'Found ' + business.name : 'No business'} (${dbQueryTime}ms)`);
  } catch (error) {
    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Business lookup failed', { duration: dbQueryTime, success: false, error: error.message });

    logger.warn(`Database lookup failed (${dbQueryTime}ms):`, error.message);
    // Continue without business - user can still access basic features
  }

  // Allow certain commands without business registration
  const allowedWithoutBusiness = ['help', 'menu', 'register', 'business_info'];

  if (!business && session.step !== 'registration' && !allowedWithoutBusiness.includes(globalCommand)) {
    // Check if it's a numbered option that requires business
    const businessRequiredNumbers = ['1', '2', '3', '4', '5', '6', '7', '8'];
    if (businessRequiredNumbers.includes(messageBody.trim())) {
      return getRegistrationPrompt(language);
    }
  }

  // Route to appropriate handler based on current step
  logger.info(`Processing step: ${session.step}`);

  let result;
  switch (session.step) {
    case 'welcome':
      logger.info('Handling welcome step');
      result = await handleWelcomeStep(messageBody, session, business, language);
      break;

    case 'registration':
      logger.info('Handling registration flow');
      result = await handleRegistrationFlow(messageBody, session, language);
      break;

    case 'add_product':
      logger.info('Handling add product flow');
      result = await handleAddProductFlow(messageBody, session, business, language);
      break;

    case 'record_sale':
      logger.info('Handling record sale flow');
      result = await handleRecordSaleFlow(messageBody, session, business, language);
      break;

    case 'add_customer':
      logger.info('Handling add customer flow');
      result = await handleAddCustomerFlow(messageBody, session, business, language);
      break;

    case 'edit_product':
      logger.info('Handling edit product flow');
      result = await handleEditProductFlow(messageBody, session, business, language);
      break;

    case 'delete_product':
      logger.info('Handling delete product flow');
      result = await handleDeleteProductFlow(messageBody, session, business, language);
      break;

    case 'quick_sale':
      logger.info('Handling quick sale flow');
      result = await handleQuickSaleFlow(messageBody, session, business, language);
      break;

    default:
      logger.info('Handling default welcome step');
      result = await handleWelcomeStep(messageBody, session, business, language);
      break;
  }

  logger.info(`Result from step processing: ${result ? 'Got response (' + result.length + ' chars)' : 'No response'}`);
  return result;
}

/**
 * Handle back command - context-aware navigation
 */
function handleBackCommand(session, language) {
  const { step, data } = session;

  switch (step) {
    case 'add_product':
      if (data.name && !data.price) {
        // Go back to name input
        delete data.name;
        return getMessage('products.add_start', language);
      } else if (data.price && !data.stock) {
        // Go back to price input
        delete data.price;
        return getMessage('products.name_received', language);
      } else if (data.stock && !data.category) {
        // Go back to stock input
        delete data.stock;
        return getMessage('products.price_received', language);
      } else {
        // Go back to main menu
        session.step = 'welcome';
        session.data = {};
        return getMainMenu(language);
      }

    case 'record_sale':
      if (data.items && data.items.length > 0) {
        // Remove last item
        data.items.pop();
        return getMessage('sales.item_removed', language) + '\n\n' +
               getMessage('sales.continue_or_finish', language);
      } else {
        // Go back to main menu
        session.step = 'welcome';
        session.data = {};
        return getMainMenu(language);
      }

    case 'add_customer':
      if (data.name && !data.phone) {
        // Go back to name input
        delete data.name;
        return getMessage('customers.add_start', language);
      } else {
        // Go back to main menu
        session.step = 'welcome';
        session.data = {};
        return getMainMenu(language);
      }

    case 'registration':
      if (data.name && !data.type) {
        // Go back to name input
        delete data.name;
        return getBusinessRegistrationStart(language);
      } else if (data.type && !data.owner_name) {
        // Go back to type selection
        delete data.type;
        return getBusinessTypeMessage(language);
      } else {
        // Go back to main menu
        session.step = 'welcome';
        session.data = {};
        return getMainMenu(language);
      }

    default:
      // Default: go to main menu
      session.step = 'welcome';
      session.data = {};
      return getMainMenu(language);
  }
}

/**
 * Handle welcome step and main commands
 */
async function handleWelcomeStep(messageBody, session, business, language) {
  const input = messageBody.trim();
  const detectedCommand = detectCommand(messageBody, language);

  // Handle numbered menu options
  const menuOptions = {
    '1': 'record_sale',
    '2': 'add_product',
    '3': 'list_products',
    '4': 'add_customer',
    '5': 'list_customers',
    '6': 'daily_report',
    '7': 'weekly_report',
    '8': 'stock_check',
    '9': 'business_info',
    '0': 'help'
  };

  // Handle registration commands and menu options
  let selectedCommand;
  if (input.toLowerCase().includes('register') || input.toLowerCase().includes('sajili')) {
    selectedCommand = 'register';
  } else if (/^[0-9]$/.test(input)) {
    // Numbered menu option
    selectedCommand = menuOptions[input];
  } else {
    // Text command
    selectedCommand = detectedCommand;
  }

  // Handle commands that require business registration
  if (!business && ['record_sale', 'add_product', 'list_products', 'daily_report', 'weekly_report', 'add_customer', 'list_customers', 'stock_check'].includes(selectedCommand)) {
    return getRegistrationPrompt(language);
  }

  switch (selectedCommand) {
    case 'record_sale':
      session.step = 'record_sale';
      session.data = {};
      return await startSaleFlow(business, language);

    case 'add_product':
      session.step = 'add_product';
      session.data = {};
      return getMessage('products.add_start', language);

    case 'list_products':
      return await listProducts(business, language);

    case 'edit_product':
      session.step = 'edit_product';
      session.data = {};
      return await listProductsForEdit(business, language);

    case 'delete_product':
      session.step = 'delete_product';
      session.data = {};
      return await listProductsForDelete(business, language);

    case 'daily_report':
      return await generateDailyReport(business, language);

    case 'weekly_report':
      return await generateWeeklyReport(business, language);

    case 'add_customer':
      session.step = 'add_customer';
      session.data = {};
      return getMessage('customers.add_start', language);

    case 'list_customers':
      return await listCustomers(business, language);

    case 'stock_check':
      return await checkLowStock(business, language);

    case 'business_info':
      if (business) {
        return await getBusinessInfo(business, language);
      } else {
        return getSystemInfo(language);
      }

    case 'help':
      return getHelpMessage(language);

    case 'register':
      session.step = 'registration';
      session.data = {};
      return getBusinessRegistrationStart(language);

    case 'quick_sale':
      session.step = 'quick_sale';
      session.data = {};
      return await getQuickSaleOptions(business, language);

    case 'repeat_sale':
      return await getLastSaleForRepeat(business, session, language);

    case 'top_products':
      return await getTopProductsQuickSelect(business, language);

    default:
      return getMainMenu(language);
  }
}

/**
 * Handle business registration flow
 */
async function handleRegistrationFlow(messageBody, session, language) {
  const { data } = session;

  if (!data.name) {
    // Step 1: Get business name
    data.name = messageBody.trim();
    return getBusinessTypeMessage(language);
  }

  if (!data.type) {
    // Step 2: Get business type
    const typeMap = {
      '1': 'duka', '2': 'salon', '3': 'restaurant', '4': 'pharmacy',
      '5': 'electronics', '6': 'clothing', '7': 'hardware', '8': 'services', '9': 'other'
    };

    data.type = typeMap[messageBody.trim()] || 'other';
    return getOwnerNameMessage(language);
  }

  if (!data.owner_name) {
    // Step 3: Get owner name
    data.owner_name = messageBody.trim();

    // Create business
    try {
      const business = new Business({
        name: data.name,
        phone: session.phone,
        owner_name: data.owner_name,
        business_type: data.type,
        settings: { language }
      });

      await business.save();

      // Create owner user
      const owner = new User({
        business_id: business._id,
        phone: session.phone,
        name: data.owner_name,
        role: 'owner',
        created_by: business._id
      });

      await owner.save();

      // Reset session
      session.step = 'welcome';
      session.data = {};

      const successMessage = getMessage('business_registration.success', language, {
        phone: session.phone,
        name: data.name,
        owner: data.owner_name
      });

      const nextSteps = getMessage('business_registration.next_steps', language);

      return successMessage + '\n\n' + nextSteps;

    } catch (error) {
      logger.error('Business registration error:', error);
      return getMessage('errors.network_error', language);
    }
  }
}

/**
 * Handle add product flow
 */
async function handleAddProductFlow(messageBody, session, business, language) {
  const { data } = session;

  if (!data.name) {
    data.name = messageBody.trim();
    return getMessage('products.name_received', language);
  }

  if (!data.price) {
    const price = parseFloat(messageBody.replace(/[^\d.]/g, ''));
    if (isNaN(price) || price <= 0) {
      return getMessage('errors.invalid_price', language);
    }
    data.price = price;
    return getMessage('products.price_received', language);
  }

  if (!data.stock) {
    const stock = parseInt(messageBody.replace(/[^\d]/g, ''));
    if (isNaN(stock) || stock < 0) {
      return getMessage('errors.invalid_quantity', language);
    }
    data.stock = stock;
    return getMessage('products.category_prompt', language);
  }

  if (!data.category) {
    const categoryMap = {
      '1': 'food', '2': 'drinks', '3': 'electronics', '4': 'clothing',
      '5': 'beauty', '6': 'health', '7': 'household', '8': 'stationery',
      '9': 'services', '10': 'other'
    };

    data.category = categoryMap[messageBody.trim()] || 'other';

    // Create product
    try {
      const owner = await User.findOne({ business_id: business._id, role: 'owner' });

      const product = new Product({
        business_id: business._id,
        name: data.name,
        price: data.price,
        stock: data.stock,
        category: data.category,
        is_service: data.category === 'services',
        created_by: owner._id
      });

      await product.save();

      // Update business stats
      await business.updateStats();

      // Reset session
      session.step = 'welcome';
      session.data = {};

      return getMessage('products.success', language, {
        name: data.name,
        price: formatCurrency(data.price, language),
        stock: data.stock,
        category: data.category
      });

    } catch (error) {
      logger.error('Product creation error:', error);
      return getMessage('errors.network_error', language);
    }
  }
}

/**
 * Handle advanced multi-product sale flow with cart system
 */
async function handleRecordSaleFlow(messageBody, session, business, language) {
  const { data } = session;

  // Initialize cart if not exists
  if (!data.cart) {
    data.cart = [];
    data.cart_total = 0;
    data.step = 'product_selection';
  }

  const input = messageBody.trim().toLowerCase();

  // Handle cart management commands
  if (input === 'finish' || input === 'maliza') {
    if (data.cart.length === 0) {
      return getMessage('sales.cart_empty', language);
    }
    data.step = 'customer_selection';
    return await showCustomerSelectionForSale(business, data, language);
  }

  if (input === 'cart' || input === 'cart summary' || input === 'muhtasari') {
    return getCartSummary(data, language);
  }

  if (input.startsWith('remove ') || input.startsWith('ondoa ')) {
    const itemIndex = parseInt(input.split(' ')[1]) - 1;
    return removeFromCart(data, itemIndex, language);
  }

  if (input === 'clear cart' || input === 'futa cart') {
    data.cart = [];
    data.cart_total = 0;
    return getMessage('sales.cart_cleared', language);
  }

  // Handle different steps of the sale process
  switch (data.step) {
    case 'product_selection':
      return await handleProductSelection(messageBody, data, business, language);
    case 'quantity_input':
      return await handleQuantityInput(messageBody, data, language);
    case 'customer_selection':
      return await handleCustomerSelection(messageBody, data, business, language);
    case 'payment_method':
      return await handlePaymentMethod(messageBody, data, business, language);
    case 'payment_amount':
      return await handlePaymentAmount(messageBody, data, business, language);
    default:
      return await handleProductSelection(messageBody, data, business, language);
  }
}

/**
 * Handle add customer flow
 */
async function handleAddCustomerFlow(messageBody, session, business, language) {
  const { data } = session;

  if (!data.name) {
    data.name = messageBody.trim();
    return getMessage('customers.name_received', language);
  }

  if (!data.phone_processed) {
    const phone = messageBody.trim();
    data.phone = phone === '' ? null : phone;
    data.phone_processed = true;

    // Create customer
    try {
      const owner = await User.findOne({ business_id: business._id, role: 'owner' });

      const customer = new Customer({
        business_id: business._id,
        name: data.name,
        phone: data.phone,
        created_by: owner._id
      });

      await customer.save();

      // Update business stats
      await business.updateStats();

      // Reset session
      session.step = 'welcome';
      session.data = {};

      return getMessage('customers.success', language, {
        name: data.name,
        phone: data.phone || (language === 'sw' ? 'Hakuna' : 'None')
      });

    } catch (error) {
      logger.error('Customer creation error:', error);
      return getMessage('errors.network_error', language);
    }
  }
}

/**
 * List products (optimized with monitoring)
 */
async function listProducts(business, language) {
  const dbStartTime = Date.now();

  try {
    // Get products with sales data for enhanced display
    const products = await Product.find({
      business_id: business._id,
      is_active: true
    }).select('name price stock category is_service sales_count')
      .sort({ sales_count: -1 })
      .limit(20)
      .lean();

    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Enhanced products list query completed', { duration: dbQueryTime, success: true });

    if (products.length === 0) {
      return getMessage('products.list_empty', language) + '\n\n' +
             getMessage('products.list_empty_actions', language);
    }

    let response = getMessage('products.list_header', language, { count: products.length });

    products.forEach((product, index) => {
      const stockInfo = product.is_service ?
        getMessage('common.service', language) :
        `${product.stock} ${getMessage('common.in_stock', language)}`;

      const salesInfo = product.sales_count ?
        ` | 🏆 ${product.sales_count} ${getMessage('sales.times_sold', language)}` : '';

      const stockIcon = product.is_service ? '🔧' :
                       (product.stock > 10 ? '✅' :
                        product.stock > 0 ? '⚠️' : '❌');

      response += `\n${index + 1}. ${stockIcon} *${product.name}*\n   💰 ${formatCurrency(product.price, language)} | 📊 ${stockInfo}${salesInfo}`;
    });

    response += '\n\n' + getMessage('products.list_actions', language);

    return response;

  } catch (error) {
    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Products list query failed', { duration: dbQueryTime, success: false, error: error.message });

    logger.error(`Enhanced list products error (${dbQueryTime}ms):`, error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Start sale flow (optimized with monitoring)
 */
async function startSaleFlow(business, language) {
  const dbStartTime = Date.now();

  try {
    // Use lean() and select only needed fields for performance
    const products = await Product.find({
      business_id: business._id,
      is_active: true,
      $or: [
        { is_service: true },
        { stock: { $gt: 0 } }
      ]
    }).select('name price stock is_service').limit(20).lean();

    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Sale flow started', { duration: dbQueryTime, success: true });

    if (products.length === 0) {
      return getMessage('products.list_empty', language);
    }

    let response = getMessage('sales.start', language);

    products.forEach((product, index) => {
      response += `${index + 1}. *${product.name}*\n   💰 ${formatCurrency(product.price, language)}`;
      if (!product.is_service) {
        response += ` | 📊 ${product.stock} ${language === 'sw' ? 'zimesalia' : 'available'}`;
      }
      response += '\n';
    });

    return response;

  } catch (error) {
    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Sale flow failed', { duration: dbQueryTime, success: false, error: error.message });

    logger.error(`Start sale flow error (${dbQueryTime}ms):`, error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Generate daily report (optimized with monitoring)
 */
async function generateDailyReport(business, language) {
  const dbStartTime = Date.now();

  try {
    const today = new Date();
    const startOfDay = new Date(today);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    // Get yesterday for comparison
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const startOfYesterday = new Date(yesterday);
    startOfYesterday.setHours(0, 0, 0, 0);
    const endOfYesterday = new Date(yesterday);
    endOfYesterday.setHours(23, 59, 59, 999);

    // Get today's sales
    const todaySales = await Sale.find({
      business_id: business._id,
      sale_date: { $gte: startOfDay, $lte: endOfDay },
      status: 'completed'
    }).select('total_amount items customer_id payment_method sale_date').lean();

    // Get yesterday's sales for comparison
    const yesterdaySales = await Sale.find({
      business_id: business._id,
      sale_date: { $gte: startOfYesterday, $lte: endOfYesterday },
      status: 'completed'
    }).select('total_amount').lean();

    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Enhanced daily report query completed', { duration: dbQueryTime, success: true });

    // Calculate today's metrics
    const todayRevenue = todaySales.reduce((total, sale) => total + sale.total_amount, 0);
    const todayItemsSold = todaySales.reduce((total, sale) => {
      return total + (sale.items ? sale.items.reduce((sum, item) => sum + item.quantity, 0) : 0);
    }, 0);
    const todayTransactions = todaySales.length;
    const uniqueCustomers = new Set(todaySales.map(sale => sale.customer_id).filter(Boolean)).size;

    // Calculate yesterday's metrics for comparison
    const yesterdayRevenue = yesterdaySales.reduce((total, sale) => total + sale.total_amount, 0);
    const revenueChange = yesterdayRevenue > 0 ?
      ((todayRevenue - yesterdayRevenue) / yesterdayRevenue * 100).toFixed(1) : 0;

    // Get top products with detailed analytics
    const productSales = {};
    todaySales.forEach(sale => {
      if (sale.items) {
        sale.items.forEach(item => {
          if (!productSales[item.product_name]) {
            productSales[item.product_name] = { quantity: 0, revenue: 0 };
          }
          productSales[item.product_name].quantity += item.quantity;
          productSales[item.product_name].revenue += item.total_price;
        });
      }
    });

    const topProducts = Object.entries(productSales)
      .sort((a, b) => b[1].revenue - a[1].revenue)
      .slice(0, 3)
      .map(([name, data]) => `• ${name}: ${data.quantity} (${formatCurrency(data.revenue, language)})`)
      .join('\n');

    // Calculate average transaction value
    const avgTransactionValue = todayTransactions > 0 ? todayRevenue / todayTransactions : 0;

    // Build enhanced report
    let report = getMessage('reports.daily_header', language, {
      business: business.name,
      date: formatDate(today, language)
    });

    report += '\n\n' + getMessage('reports.daily_summary', language, {
      revenue: formatCurrency(todayRevenue, language),
      transactions: todayTransactions,
      items: todayItemsSold,
      customers: uniqueCustomers,
      avg_transaction: formatCurrency(avgTransactionValue, language)
    });

    // Add comparison with yesterday
    if (yesterdayRevenue > 0) {
      const changeIcon = revenueChange >= 0 ? '📈' : '📉';
      report += '\n\n' + getMessage('reports.daily_comparison', language, {
        icon: changeIcon,
        change: Math.abs(revenueChange),
        yesterday: formatCurrency(yesterdayRevenue, language)
      });
    }

    // Add top products section
    if (topProducts) {
      report += '\n\n' + getMessage('reports.top_products_section', language);
      report += '\n' + topProducts;
    }

    // Add contextual next actions
    report += '\n\n' + getReportContextualActions(todayRevenue, todayTransactions, language);

    return report;

  } catch (error) {
    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Daily report query failed', { duration: dbQueryTime, success: false, error: error.message });

    logger.error(`Daily report error (${dbQueryTime}ms):`, error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Generate weekly report
 */
async function generateWeeklyReport(business, language) {
  // Similar to daily report but for the past 7 days
  // Implementation would be similar to generateDailyReport
  return getMessage('errors.network_error', language); // Placeholder
}

/**
 * List customers
 */
async function listCustomers(business, language) {
  try {
    const customers = await Customer.find({
      business_id: business._id,
      is_active: true
    }).select('name phone purchase_history')
      .sort({ 'purchase_history.total_spent': -1 })
      .limit(20)
      .lean();

    if (customers.length === 0) {
      return getMessage('customers.list_empty', language) + '\n\n' +
             getMessage('customers.list_empty_actions', language);
    }

    let response = getMessage('customers.list_header', language, { count: customers.length });

    customers.forEach((customer, index) => {
      const totalSpent = customer.purchase_history?.total_spent || 0;
      const lastPurchase = customer.purchase_history?.last_purchase_date;

      const loyaltyIcon = totalSpent > 100000 ? '🏆' :
                         totalSpent > 50000 ? '⭐' :
                         totalSpent > 10000 ? '💎' : '👤';

      response += `\n${index + 1}. ${loyaltyIcon} *${customer.name}*`;

      if (customer.phone) {
        response += `\n   📱 ${customer.phone}`;
      }

      response += `\n   💰 ${getMessage('customers.total_spent', language)}: ${formatCurrency(totalSpent, language)}`;

      if (lastPurchase) {
        response += `\n   📅 ${getMessage('customers.last_purchase', language)}: ${formatDate(lastPurchase, language)}`;
      }

      response += '\n';
    });

    response += '\n' + getMessage('customers.list_actions', language);

    return response;

  } catch (error) {
    logger.error('Enhanced list customers error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Check low stock
 */
async function checkLowStock(business, language) {
  try {
    const lowStockProducts = await Product.find({
      business_id: business._id,
      is_active: true,
      is_service: false,
      $expr: { $lte: ['$stock', '$low_stock_alert'] }
    });

    if (lowStockProducts.length === 0) {
      return language === 'sw' ?
        '✅ Bidhaa zote zina hisa ya kutosha!' :
        '✅ All products have sufficient stock!';
    }

    let response = language === 'sw' ?
      '⚠️ *BIDHAA ZILIZOPUNGUA*\n\n' :
      '⚠️ *LOW STOCK PRODUCTS*\n\n';

    lowStockProducts.forEach(product => {
      response += `• *${product.name}*: ${product.stock} ${language === 'sw' ? 'zimesalia' : 'remaining'}\n`;
    });

    return response;

  } catch (error) {
    logger.error('Check low stock error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Get business information
 */
async function getBusinessInfo(business, language) {
  const daysRemaining = business.days_remaining;
  const status = business.subscription_active ?
    (language === 'sw' ? 'Inatumika' : 'Active') :
    (language === 'sw' ? 'Imekwisha' : 'Expired');

  return language === 'sw' ?
    `🏪 *MAELEZO YA BIASHARA*\n\n` +
    `📛 Jina: ${business.name}\n` +
    `👤 Mmiliki: ${business.owner_name}\n` +
    `📱 Simu: ${business.phone}\n` +
    `🏷️ Aina: ${business.business_type}\n` +
    `📅 Usajili: ${status}\n` +
    `⏰ Siku zilizosalia: ${daysRemaining}\n` +
    `📊 Bidhaa: ${business.stats.total_products}\n` +
    `💰 Mapato: ${formatCurrency(business.stats.total_revenue, language)}` :

    `🏪 *BUSINESS INFORMATION*\n\n` +
    `📛 Name: ${business.name}\n` +
    `👤 Owner: ${business.owner_name}\n` +
    `📱 Phone: ${business.phone}\n` +
    `🏷️ Type: ${business.business_type}\n` +
    `📅 Subscription: ${status}\n` +
    `⏰ Days remaining: ${daysRemaining}\n` +
    `📊 Products: ${business.stats.total_products}\n` +
    `💰 Revenue: ${formatCurrency(business.stats.total_revenue, language)}`;
}

/**
 * Get business registration start message
 */
function getBusinessRegistrationStart(language) {
  if (language === 'sw') {
    return `🏪 *KARIBU KWENYE USAJILI WA BIASHARA*

Tutakusaidia kusajili biashara yako hatua kwa hatua.

📝 *HATUA YA KWANZA:*
Andika jina la biashara yako

💡 *Mfano:* Duka la Mama Fatuma, Salon Beauty, Mkahawa Kilimanjaro

Andika jina la biashara yako:`;
  } else {
    return `🏪 *WELCOME TO BUSINESS REGISTRATION*

We'll help you register your business step by step.

📝 *FIRST STEP:*
Enter your business name

💡 *Example:* Mama Fatuma Shop, Beauty Salon, Kilimanjaro Restaurant

Enter your business name:`;
  }
}

/**
 * Get system info for unregistered users
 */
function getSystemInfo(language) {
  if (language === 'sw') {
    return `ℹ️ *KUHUSU WHATSAPP POS TANZANIA*

🏪 *NI NINI:*
Mfumo wa kisasa wa mauzo kupitia WhatsApp

✨ *FAIDA:*
• Rekodi mauzo haraka
• Fuatilia hisa za bidhaa
• Pata ripoti za kila siku
• Ongeza wateja na bidhaa
• Huduma ya Kiswahili

🚀 *JINSI YA KUANZA:*
1. Sajili biashara yako
2. Ongeza bidhaa zako
3. Anza kurekodi mauzo

📞 *MSAADA:* Andika "msaada" wakati wowote`;
  } else {
    return `ℹ️ *ABOUT WHATSAPP POS TANZANIA*

🏪 *WHAT IT IS:*
Modern sales system via WhatsApp

✨ *BENEFITS:*
• Record sales quickly
• Track product inventory
• Get daily reports
• Add customers and products
• Swahili language support

🚀 *HOW TO START:*
1. Register your business
2. Add your products
3. Start recording sales

📞 *HELP:* Type "help" anytime`;
  }
}

/**
 * Get registration prompt for users without business
 */
function getRegistrationPrompt(language) {
  if (language === 'sw') {
    return `🏪 *SAJILI BIASHARA YAKO KWANZA*

Ili kutumia huduma za mauzo, unahitaji kusajili biashara yako kwanza.

📝 *JINSI YA KUSAJILI:*
• Andika "sajili" au "register"
• Fuata maelekezo

💡 *AU CHAGUA:*
0️⃣ Msaada - Ona maelekezo kamili
9️⃣ Maelezo - Jifunze kuhusu mfumo

Andika chaguo lako:`;
  } else {
    return `🏪 *REGISTER YOUR BUSINESS FIRST*

To use sales features, you need to register your business first.

📝 *HOW TO REGISTER:*
• Type "register" or "sajili"
• Follow the instructions

💡 *OR CHOOSE:*
0️⃣ Help - View complete instructions
9️⃣ Info - Learn about the system

Type your choice:`;
  }
}

/**
 * Get business type selection message
 */
function getBusinessTypeMessage(language) {
  if (language === 'sw') {
    return `🏪 *CHAGUA AINA YA BIASHARA:*

1️⃣ Duka la Jumla
2️⃣ Salon/Urembo
3️⃣ Mkahawa/Hoteli
4️⃣ Duka la Dawa
5️⃣ Vifaa vya Umeme
6️⃣ Nguo na Mavazi
7️⃣ Vifaa vya Ujenzi
8️⃣ Huduma
9️⃣ Nyingine

💡 Andika nambari ya chaguo lako`;
  } else {
    return `🏪 *SELECT BUSINESS TYPE:*

1️⃣ General Store
2️⃣ Salon/Beauty
3️⃣ Restaurant/Hotel
4️⃣ Pharmacy
5️⃣ Electronics
6️⃣ Clothing
7️⃣ Hardware
8️⃣ Services
9️⃣ Other

💡 Type the number of your choice`;
  }
}

/**
 * Get owner name message
 */
function getOwnerNameMessage(language) {
  if (language === 'sw') {
    return `👤 *ANDIKA JINA LA MMILIKI:*

💡 Andika jina kamili la mmiliki wa biashara`;
  } else {
    return `👤 *ENTER OWNER NAME:*

💡 Type the full name of the business owner`;
  }
}

/**
 * Get payment options message
 */
function getPaymentOptionsMessage(language) {
  if (language === 'sw') {
    return `💳 *CHAGUA NJIA YA MALIPO:*

1️⃣ Fedha Taslimu (Cash)
2️⃣ M-Pesa
3️⃣ Tigo Pesa
4️⃣ Airtel Money
5️⃣ Benki (Bank Transfer)

💡 Andika nambari ya chaguo lako`;
  } else {
    return `💳 *SELECT PAYMENT METHOD:*

1️⃣ Cash
2️⃣ M-Pesa
3️⃣ Tigo Pesa
4️⃣ Airtel Money
5️⃣ Bank Transfer

💡 Type the number of your choice`;
  }
}

/**
 * Get main menu with numbered options
 */
function getMainMenu(language) {
  if (language === 'sw') {
    return `🏪 *KARIBU WHATSAPP POS TANZANIA* 🇹🇿

📋 *MENYU KAVU:*
1️⃣ Rekodi Mauzo
2️⃣ Ongeza Bidhaa
3️⃣ Orodha ya Bidhaa
4️⃣ Ongeza Mteja
5️⃣ Orodha ya Wateja
6️⃣ Ripoti ya Leo
7️⃣ Ripoti ya Wiki
8️⃣ Angalia Hisa
9️⃣ Taarifa za Biashara
0️⃣ Msaada

⚡ *NJIA ZA HARAKA:*
• "mauzo haraka" - Mauzo ya bidhaa maarufu
• "rudia mauzo" - Rudia mauzo ya mwisho
• "bidhaa bora" - Bidhaa zinazouzwa zaidi

📝 *AMRI ZA MAANDISHI:*
• "hariri bidhaa" - Badilisha bidhaa
• "futa bidhaa" - Futa bidhaa
• "menu" - Kurudi menyuni
• "cancel" - Ghairi kitendo
• "back" - Rudi nyuma

🌐 *LUGHA:* Andika "English" kubadili lugha
💡 *VIDOKEZO:* Andika nambari, neno la amri, au njia ya haraka`;
  } else {
    return `🏪 *WELCOME TO WHATSAPP POS TANZANIA* 🇹🇿

📋 *MAIN MENU:*
1️⃣ Record Sale
2️⃣ Add Product
3️⃣ List Products
4️⃣ Add Customer
5️⃣ List Customers
6️⃣ Daily Report
7️⃣ Weekly Report
8️⃣ Check Stock
9️⃣ Business Info
0️⃣ Help

⚡ *QUICK SHORTCUTS:*
• "quick sale" - Fast sale with top products
• "repeat sale" - Repeat last sale
• "top products" - Best selling products

📝 *TEXT COMMANDS:*
• "edit product" - Modify products
• "delete product" - Remove products
• "menu" - Return to menu
• "cancel" - Cancel action
• "back" - Go back

🌐 *LANGUAGE:* Type "Swahili" to switch language
💡 *TIPS:* Type number, command word, or quick shortcut`;
  }
}

/**
 * Get help message with all commands
 */
function getHelpMessage(language) {
  if (language === 'sw') {
    return `📚 *MSAADA KAMILI - WHATSAPP POS TANZANIA* 🇹🇿

🏪 *MAUZO YA KISASA:*
• "1" au "uza" - Mauzo ya bidhaa nyingi (cart)
• "mauzo haraka" - Mauzo ya haraka ya bidhaa maarufu
• "rudia mauzo" - Rudia mauzo ya mwisho
• "bidhaa bora" - Bidhaa zinazouzwa zaidi

📦 *USIMAMIZI WA BIDHAA:*
• "2" au "ongeza bidhaa" - Ongeza bidhaa mpya
• "3" au "bidhaa" - Orodha ya bidhaa zote
• "hariri bidhaa" - Badilisha taarifa za bidhaa
• "futa bidhaa" - Futa bidhaa kutoka mfumo
• "8" au "hisa" - Angalia hisa za bidhaa

👥 *USIMAMIZI WA WATEJA:*
• "4" au "ongeza mteja" - Ongeza mteja mpya
• "5" au "wateja" - Orodha ya wateja wote

📊 *RIPOTI ZA BIASHARA:*
• "6" au "ripoti ya leo" - Ripoti ya leo (na mabadiliko)
• "7" au "ripoti ya wiki" - Ripoti ya wiki
• "9" au "taarifa za biashara" - Taarifa za biashara

⚡ *NJIA ZA HARAKA:*
• "cart" - Muhtasari wa kart
• "maliza" - Maliza mauzo
• "back" - Rudi nyuma
• "cancel" - Ghairi kitendo
• "menu" - Kurudi menyuni kuu

🌐 *LUGHA:*
• "English" - Badili kwenda Kiingereza

💡 *VIDOKEZO:*
Tumia nambari (1-9), maneno ya amri, au njia za haraka`;
  } else {
    return `📚 *COMPLETE HELP - WHATSAPP POS TANZANIA* 🇹🇿

🏪 *MODERN SALES:*
• "1" or "sell" - Multi-product sales (cart system)
• "quick sale" - Fast sale with top products
• "repeat sale" - Repeat last transaction
• "top products" - Best selling products

📦 *PRODUCT MANAGEMENT:*
• "2" or "add product" - Add new product
• "3" or "products" - List all products
• "edit product" - Modify product information
• "delete product" - Remove product from system
• "8" or "stock" - Check inventory levels

👥 *CUSTOMER MANAGEMENT:*
• "4" or "add customer" - Add new customer
• "5" or "customers" - List all customers

📊 *BUSINESS REPORTS:*
• "6" or "daily report" - Today's report (with trends)
• "7" or "weekly report" - Weekly report
• "9" or "business info" - Business information

⚡ *QUICK SHORTCUTS:*
• "cart" - Cart summary
• "finish" - Complete sale
• "back" - Go back
• "cancel" - Cancel action
• "menu" - Return to main menu

🌐 *LANGUAGE:*
• "Swahili" - Switch to Swahili

💡 *TIPS:*
Use numbers (1-9), command words, or quick shortcuts`;
  }
}

/**
 * List products for editing with numbered options
 */
async function listProductsForEdit(business, language) {
  try {
    const products = await Product.find({
      business_id: business._id,
      is_active: true
    }).limit(20).lean();

    if (products.length === 0) {
      return getMessage('products.list_empty', language);
    }

    let response = getMessage('products.edit_select', language);

    products.forEach((product, index) => {
      response += `\n${index + 1}. ${product.name} - ${formatCurrency(product.price, language)}`;
    });

    response += `\n\n${getMessage('products.edit_instructions', language)}`;
    return response;

  } catch (error) {
    logger.error('List products for edit error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * List products for deletion with numbered options
 */
async function listProductsForDelete(business, language) {
  try {
    const products = await Product.find({
      business_id: business._id,
      is_active: true
    }).limit(20).lean();

    if (products.length === 0) {
      return getMessage('products.list_empty', language);
    }

    let response = getMessage('products.delete_select', language);

    products.forEach((product, index) => {
      response += `\n${index + 1}. ${product.name} - ${formatCurrency(product.price, language)}`;
    });

    response += `\n\n${getMessage('products.delete_warning', language)}`;
    return response;

  } catch (error) {
    logger.error('List products for delete error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Handle edit product flow
 */
async function handleEditProductFlow(messageBody, session, business, language) {
  const { data } = session;

  if (!data.product_id) {
    // Step 1: Select product to edit
    const productIndex = parseInt(messageBody.trim()) - 1;

    try {
      const products = await Product.find({
        business_id: business._id,
        is_active: true
      }).limit(20);

      if (productIndex >= 0 && productIndex < products.length) {
        data.product_id = products[productIndex]._id;
        data.product = products[productIndex];
        return getMessage('products.edit_field_select', language, {
          name: products[productIndex].name
        });
      } else {
        return getMessage('errors.invalid_selection', language);
      }
    } catch (error) {
      logger.error('Product selection error:', error);
      return getMessage('errors.network_error', language);
    }
  }

  if (!data.field) {
    // Step 2: Select field to edit
    const fieldMap = {
      '1': 'name',
      '2': 'price',
      '3': 'stock',
      '4': 'category'
    };

    data.field = fieldMap[messageBody.trim()];
    if (!data.field) {
      return getMessage('errors.invalid_selection', language);
    }

    return getMessage(`products.edit_${data.field}_prompt`, language, {
      current: data.product[data.field]
    });
  }

  // Step 3: Update the field
  try {
    const updateData = {};

    switch (data.field) {
      case 'name':
        updateData.name = messageBody.trim();
        break;
      case 'price':
        const price = parseFloat(messageBody.replace(/[^\d.]/g, ''));
        if (isNaN(price) || price <= 0) {
          return getMessage('errors.invalid_price', language);
        }
        updateData.price = price;
        break;
      case 'stock':
        const stock = parseInt(messageBody.replace(/[^\d]/g, ''));
        if (isNaN(stock) || stock < 0) {
          return getMessage('errors.invalid_quantity', language);
        }
        updateData.stock = stock;
        break;
      case 'category':
        const categoryMap = {
          '1': 'food', '2': 'drinks', '3': 'electronics', '4': 'clothing',
          '5': 'beauty', '6': 'health', '7': 'household', '8': 'stationery',
          '9': 'services', '10': 'other'
        };
        updateData.category = categoryMap[messageBody.trim()] || 'other';
        break;
    }

    await Product.findByIdAndUpdate(data.product_id, updateData);

    // Reset session
    session.step = 'welcome';
    session.data = {};

    return getMessage('products.edit_success', language, {
      name: data.product.name,
      field: data.field,
      value: updateData[data.field]
    });

  } catch (error) {
    logger.error('Product update error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Handle delete product flow
 */
async function handleDeleteProductFlow(messageBody, session, business, language) {
  const { data } = session;

  if (!data.product_id) {
    // Step 1: Select product to delete
    const productIndex = parseInt(messageBody.trim()) - 1;

    try {
      const products = await Product.find({
        business_id: business._id,
        is_active: true
      }).limit(20);

      if (productIndex >= 0 && productIndex < products.length) {
        data.product_id = products[productIndex]._id;
        data.product = products[productIndex];
        return getMessage('products.delete_confirm', language, {
          name: products[productIndex].name
        });
      } else {
        return getMessage('errors.invalid_selection', language);
      }
    } catch (error) {
      logger.error('Product selection error:', error);
      return getMessage('errors.network_error', language);
    }
  }

  // Step 2: Confirm deletion
  const confirmation = messageBody.toLowerCase().trim();
  if (confirmation === 'yes' || confirmation === 'ndio' || confirmation === 'y') {
    try {
      // Soft delete - mark as inactive
      await Product.findByIdAndUpdate(data.product_id, { is_active: false });

      // Reset session
      session.step = 'welcome';
      session.data = {};

      return getMessage('products.delete_success', language, {
        name: data.product.name
      });

    } catch (error) {
      logger.error('Product deletion error:', error);
      return getMessage('errors.network_error', language);
    }
  } else if (confirmation === 'no' || confirmation === 'hapana' || confirmation === 'n') {
    // Cancel deletion
    session.step = 'welcome';
    session.data = {};
    return getMessage('products.delete_cancelled', language);
  } else {
    return getMessage('products.delete_confirm_again', language);
  }
}

/**
 * Handle product selection in advanced sales flow
 */
async function handleProductSelection(messageBody, data, business, language) {
  const input = messageBody.trim();

  // Check if it's a product search by name
  if (isNaN(input)) {
    return await searchProductByName(input, data, business, language);
  }

  // Handle numbered product selection
  const productIndex = parseInt(input) - 1;
  const dbStartTime = Date.now();

  try {
    const products = await Product.find({
      business_id: business._id,
      is_active: true
    }).select('name price stock is_service category').limit(20).lean();

    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Product query completed', { duration: dbQueryTime, success: true });

    if (productIndex < 0 || productIndex >= products.length) {
      return getMessage('errors.invalid_selection', language) + '\n\n' +
             getMessage('sales.try_product_search', language);
    }

    const product = products[productIndex];

    // Check stock availability
    if (!product.is_service && product.stock <= 0) {
      return getMessage('errors.out_of_stock', language, { product: product.name });
    }

    data.current_product = {
      id: product._id,
      name: product.name,
      price: product.price,
      available_stock: product.stock,
      is_service: product.is_service
    };

    data.step = 'quantity_input';

    return getMessage('sales.product_selected_quantity', language, {
      product: product.name,
      price: formatCurrency(product.price, language),
      stock: product.is_service ? getMessage('common.unlimited', language) : product.stock
    });

  } catch (error) {
    logger.error('Product selection error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Search products by name
 */
async function searchProductByName(searchTerm, data, business, language) {
  try {
    // Get all products for AI-powered search
    const allProducts = await Product.find({
      business_id: business._id,
      is_active: true
    }).select('name price stock is_service category').limit(50).lean();

    if (allProducts.length === 0) {
      return getMessage('sales.no_products_available', language);
    }

    // Use AI for smart product search
    const aiSearchResult = await aiService.smartProductSearch(searchTerm, allProducts, language);

    if (aiSearchResult.found) {
      // Direct match found
      const product = aiSearchResult.product;

      // Check stock availability
      if (!product.is_service && product.stock <= 0) {
        return getMessage('errors.out_of_stock', language, { product: product.name });
      }

      // Store selected product
      data.selected_product = product;
      data.step = 'quantity_input';

      const stockInfo = product.is_service ?
        getMessage('common.service', language) :
        `${product.stock} ${getMessage('common.in_stock', language)}`;

      return getMessage('sales.product_found_ai', language, {
        product: product.name,
        price: formatCurrency(product.price, language),
        stock: stockInfo,
        confidence: Math.round(aiSearchResult.confidence * 100)
      }) + '\n\n' + getMessage('sales.enter_quantity', language);
    }

    // No direct match, show suggestions
    let products = aiSearchResult.suggestions || [];

    // Fallback to regex search if AI didn't find suggestions
    if (products.length === 0) {
      products = allProducts.filter(p =>
        p.name.toLowerCase().includes(searchTerm.toLowerCase())
      ).slice(0, 10);
    }

    if (products.length === 0) {
      return getMessage('sales.no_products_found', language, { search: searchTerm }) + '\n\n' +
             getMessage('sales.try_different_search', language);
    }

    let response = getMessage('sales.search_results', language, { search: searchTerm });

    products.forEach((product, index) => {
      const stockInfo = product.is_service ?
        getMessage('common.service', language) :
        `${product.stock} ${getMessage('common.in_stock', language)}`;

      const stockIcon = product.is_service ? '🔧' :
                       (product.stock > 10 ? '✅' :
                        product.stock > 0 ? '⚠️' : '❌');

      response += `\n${index + 1}. ${stockIcon} ${product.name}\n   💰 ${formatCurrency(product.price, language)} | 📊 ${stockInfo}`;
    });

    response += `\n\n${getMessage('sales.select_from_search', language)}`;
    response += `\n${getMessage('sales.or_try_different_search', language)}`;

    // Store search results for selection
    data.search_results = products;

    return response;

  } catch (error) {
    logger.error('AI-enhanced product search error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Handle quantity input
 */
async function handleQuantityInput(messageBody, data, language) {
  // Use AI to extract quantity from natural language
  const aiQuantityResult = await aiService.extractQuantityAndPrice(messageBody, language);

  let quantity;

  if (aiQuantityResult.quantity && aiQuantityResult.confidence > 0.5) {
    quantity = aiQuantityResult.quantity;
    logger.info(`AI extracted quantity: ${quantity} from "${messageBody}" (confidence: ${aiQuantityResult.confidence})`);
  } else {
    // Fallback to regex extraction
    const extracted = parseFloat(messageBody.replace(/[^\d.]/g, ''));
    if (!isNaN(extracted) && extracted > 0) {
      quantity = extracted;
    }
  }

  if (!quantity || quantity <= 0) {
    return getMessage('errors.invalid_quantity', language) + '\n\n' +
           getMessage('sales.quantity_examples', language);
  }

  const product = data.current_product;

  // Check stock availability for physical products
  if (!product.is_service && quantity > product.available_stock) {
    return getMessage('errors.insufficient_stock', language, {
      available: product.available_stock,
      requested: quantity
    });
  }

  // Add to cart
  const cartItem = {
    product_id: product.id,
    name: product.name,
    quantity: quantity,
    unit_price: product.price,
    total_price: quantity * product.price,
    is_service: product.is_service
  };

  data.cart.push(cartItem);
  data.cart_total += cartItem.total_price;

  // Clear current product
  delete data.current_product;
  data.step = 'product_selection';

  const cartSummary = getCartSummary(data, language);
  const nextActions = getMessage('sales.item_added_next_actions', language);

  return getMessage('sales.item_added_to_cart', language, {
    product: cartItem.name,
    quantity: quantity,
    total: formatCurrency(cartItem.total_price, language)
  }) + '\n\n' + cartSummary + '\n\n' + nextActions;
}

/**
 * Get cart summary
 */
function getCartSummary(data, language) {
  if (!data.cart || data.cart.length === 0) {
    return getMessage('sales.cart_empty', language);
  }

  let summary = getMessage('sales.cart_summary_header', language);

  data.cart.forEach((item, index) => {
    summary += `\n${index + 1}. ${item.name} x${item.quantity} = ${formatCurrency(item.total_price, language)}`;
  });

  summary += `\n\n${getMessage('sales.cart_total', language, {
    total: formatCurrency(data.cart_total, language),
    items: data.cart.length
  })}`;

  return summary;
}

/**
 * Remove item from cart
 */
function removeFromCart(data, itemIndex, language) {
  if (!data.cart || itemIndex < 0 || itemIndex >= data.cart.length) {
    return getMessage('errors.invalid_selection', language);
  }

  const removedItem = data.cart[itemIndex];
  data.cart_total -= removedItem.total_price;
  data.cart.splice(itemIndex, 1);

  return getMessage('sales.item_removed_from_cart', language, {
    product: removedItem.name
  }) + '\n\n' + getCartSummary(data, language);
}

/**
 * Show customer selection for sale
 */
async function showCustomerSelectionForSale(business, data, language) {
  try {
    const customers = await Customer.find({
      business_id: business._id,
      is_active: true
    }).select('name phone').limit(10).lean();

    let response = getMessage('sales.customer_selection_header', language);
    response += '\n\n0. ' + getMessage('sales.no_customer_option', language);

    customers.forEach((customer, index) => {
      response += `\n${index + 1}. ${customer.name} (${customer.phone})`;
    });

    response += '\n\n' + getMessage('sales.customer_selection_instructions', language);

    // Store customers for selection
    data.available_customers = customers;

    return response;

  } catch (error) {
    logger.error('Customer selection error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Handle customer selection
 */
async function handleCustomerSelection(messageBody, data, business, language) {
  const customerIndex = parseInt(messageBody.trim());

  if (customerIndex === 0) {
    // No customer selected
    data.customer = null;
    data.step = 'payment_method';
    return getPaymentMethodSelection(language);
  }

  if (isNaN(customerIndex) || customerIndex < 0 || customerIndex > data.available_customers.length) {
    return getMessage('errors.invalid_selection', language);
  }

  const selectedCustomer = data.available_customers[customerIndex - 1];
  data.customer = {
    id: selectedCustomer._id,
    name: selectedCustomer.name,
    phone: selectedCustomer.phone
  };

  data.step = 'payment_method';

  return getMessage('sales.customer_selected', language, {
    customer: selectedCustomer.name
  }) + '\n\n' + getPaymentMethodSelection(language);
}

/**
 * Get payment method selection
 */
function getPaymentMethodSelection(language) {
  return getMessage('sales.payment_method_selection', language);
}

/**
 * Handle payment method selection
 */
async function handlePaymentMethod(messageBody, data, business, language) {
  const input = messageBody.trim().toLowerCase();

  const paymentMethods = {
    '1': 'cash',
    '2': 'mobile_money',
    '3': 'bank_transfer',
    '4': 'credit',
    'cash': 'cash',
    'pesa': 'cash',
    'mobile': 'mobile_money',
    'mpesa': 'mobile_money',
    'bank': 'bank_transfer',
    'benki': 'bank_transfer',
    'credit': 'credit',
    'mkopo': 'credit'
  };

  const paymentMethod = paymentMethods[input];

  if (!paymentMethod) {
    return getMessage('errors.invalid_payment_method', language);
  }

  data.payment_method = paymentMethod;

  if (paymentMethod === 'credit') {
    // For credit sales, complete immediately
    return await completeSale(data, business, language);
  } else {
    // Ask for payment amount
    data.step = 'payment_amount';
    return getMessage('sales.payment_amount_prompt', language, {
      total: formatCurrency(data.cart_total, language),
      method: getMessage(`payment_methods.${paymentMethod}`, language)
    });
  }
}

/**
 * Handle payment amount
 */
async function handlePaymentAmount(messageBody, data, business, language) {
  const amount = parseFloat(messageBody.replace(/[^\d.]/g, ''));

  if (isNaN(amount) || amount < 0) {
    return getMessage('errors.invalid_amount', language);
  }

  data.payment_amount = amount;
  data.change_amount = amount - data.cart_total;

  return await completeSale(data, business, language);
}

/**
 * Complete the sale and save to database
 */
async function completeSale(data, business, language) {
  try {
    // Create sale record
    const saleData = {
      business_id: business._id,
      customer_id: data.customer ? data.customer.id : null,
      items: data.cart.map(item => ({
        product_id: item.product_id,
        product_name: item.name,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price
      })),
      total_amount: data.cart_total,
      payment_method: data.payment_method,
      payment_amount: data.payment_amount || data.cart_total,
      change_amount: data.change_amount || 0,
      sale_date: new Date(),
      status: 'completed'
    };

    const sale = new Sale(saleData);
    await sale.save();

    // Update product stock for physical products
    for (const item of data.cart) {
      if (!item.is_service) {
        await Product.findByIdAndUpdate(
          item.product_id,
          { $inc: { stock: -item.quantity } }
        );
      }
    }

    // Update business stats
    await Business.findByIdAndUpdate(business._id, {
      $inc: {
        total_sales: data.cart_total,
        total_transactions: 1
      }
    });

    // Generate receipt
    const receipt = generateReceipt(sale, data, business, language);

    // Reset session
    const session = { step: 'welcome', data: {} };

    // Return success message with receipt and next actions
    return receipt + '\n\n' + getPostSaleActions(language);

  } catch (error) {
    logger.error('Sale completion error:', error);
    return getMessage('errors.sale_failed', language);
  }
}

/**
 * Generate receipt for completed sale
 */
function generateReceipt(sale, data, business, language) {
  let receipt = getMessage('sales.receipt_header', language, {
    business: business.name,
    date: formatDate(sale.sale_date, language),
    sale_id: sale._id.toString().slice(-6)
  });

  receipt += '\n\n' + getMessage('sales.receipt_items_header', language);

  data.cart.forEach(item => {
    receipt += `\n${item.name} x${item.quantity} = ${formatCurrency(item.total_price, language)}`;
  });

  receipt += '\n' + '─'.repeat(25);
  receipt += `\n${getMessage('sales.receipt_total', language)} ${formatCurrency(data.cart_total, language)}`;

  if (data.payment_method !== 'credit') {
    receipt += `\n${getMessage('sales.receipt_paid', language)} ${formatCurrency(data.payment_amount, language)}`;
    if (data.change_amount > 0) {
      receipt += `\n${getMessage('sales.receipt_change', language)} ${formatCurrency(data.change_amount, language)}`;
    }
  }

  receipt += `\n${getMessage('sales.receipt_payment_method', language)} ${getMessage(`payment_methods.${data.payment_method}`, language)}`;

  if (data.customer) {
    receipt += `\n${getMessage('sales.receipt_customer', language)} ${data.customer.name}`;
  }

  receipt += '\n\n' + getMessage('sales.receipt_footer', language);

  return receipt;
}

/**
 * Get post-sale action suggestions
 */
function getPostSaleActions(language) {
  return getMessage('sales.post_sale_actions', language);
}

/**
 * Get quick sale options (top 5 products)
 */
async function getQuickSaleOptions(business, language) {
  try {
    const topProducts = await Product.find({
      business_id: business._id,
      is_active: true
    }).select('name price stock is_service')
      .sort({ sales_count: -1 })
      .limit(5)
      .lean();

    if (topProducts.length === 0) {
      return getMessage('products.list_empty', language);
    }

    let response = getMessage('sales.quick_sale_header', language);

    topProducts.forEach((product, index) => {
      const stockInfo = product.is_service ?
        getMessage('common.service', language) :
        `${product.stock} ${getMessage('common.in_stock', language)}`;

      response += `\n${index + 1}. ${product.name} - ${formatCurrency(product.price, language)} (${stockInfo})`;
    });

    response += '\n\n' + getMessage('sales.quick_sale_instructions', language);

    return response;

  } catch (error) {
    logger.error('Quick sale options error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Handle quick sale flow (single product, fast checkout)
 */
async function handleQuickSaleFlow(messageBody, session, business, language) {
  const { data } = session;

  if (!data.product_id) {
    // Step 1: Select product
    const productIndex = parseInt(messageBody.trim()) - 1;

    try {
      const topProducts = await Product.find({
        business_id: business._id,
        is_active: true
      }).select('name price stock is_service')
        .sort({ sales_count: -1 })
        .limit(5)
        .lean();

      if (productIndex >= 0 && productIndex < topProducts.length) {
        const product = topProducts[productIndex];

        // Check stock
        if (!product.is_service && product.stock <= 0) {
          return getMessage('errors.out_of_stock', language, { product: product.name });
        }

        data.product_id = product._id;
        data.product = product;

        return getMessage('sales.quick_sale_quantity', language, {
          product: product.name,
          price: formatCurrency(product.price, language)
        });
      } else {
        return getMessage('errors.invalid_selection', language);
      }
    } catch (error) {
      logger.error('Quick sale product selection error:', error);
      return getMessage('errors.network_error', language);
    }
  }

  if (!data.quantity) {
    // Step 2: Get quantity
    const quantity = parseFloat(messageBody.replace(/[^\d.]/g, ''));

    if (isNaN(quantity) || quantity <= 0) {
      return getMessage('errors.invalid_quantity', language);
    }

    // Check stock
    if (!data.product.is_service && quantity > data.product.stock) {
      return getMessage('errors.insufficient_stock', language, {
        available: data.product.stock,
        requested: quantity
      });
    }

    data.quantity = quantity;
    data.total = quantity * data.product.price;

    return getMessage('sales.quick_sale_payment', language, {
      product: data.product.name,
      quantity: quantity,
      total: formatCurrency(data.total, language)
    });
  }

  // Step 3: Complete quick sale
  const paymentMethod = messageBody.trim().toLowerCase();
  const paymentMethods = {
    '1': 'cash', 'cash': 'cash', 'pesa': 'cash',
    '2': 'mobile_money', 'mobile': 'mobile_money', 'mpesa': 'mobile_money',
    '3': 'credit', 'credit': 'credit', 'mkopo': 'credit'
  };

  const selectedPayment = paymentMethods[paymentMethod];

  if (!selectedPayment) {
    return getMessage('errors.invalid_payment_method', language);
  }

  // Complete quick sale
  try {
    const saleData = {
      business_id: business._id,
      items: [{
        product_id: data.product_id,
        product_name: data.product.name,
        quantity: data.quantity,
        unit_price: data.product.price,
        total_price: data.total
      }],
      total_amount: data.total,
      payment_method: selectedPayment,
      payment_amount: data.total,
      change_amount: 0,
      sale_date: new Date(),
      status: 'completed'
    };

    const sale = new Sale(saleData);
    await sale.save();

    // Update stock
    if (!data.product.is_service) {
      await Product.findByIdAndUpdate(
        data.product_id,
        {
          $inc: {
            stock: -data.quantity,
            sales_count: 1
          }
        }
      );
    }

    // Update business stats
    await Business.findByIdAndUpdate(business._id, {
      $inc: {
        total_sales: data.total,
        total_transactions: 1
      }
    });

    // Reset session
    session.step = 'welcome';
    session.data = {};

    return getMessage('sales.quick_sale_success', language, {
      product: data.product.name,
      quantity: data.quantity,
      total: formatCurrency(data.total, language),
      payment: getMessage(`payment_methods.${selectedPayment}`, language)
    }) + '\n\n' + getPostSaleActions(language);

  } catch (error) {
    logger.error('Quick sale completion error:', error);
    return getMessage('errors.sale_failed', language);
  }
}

/**
 * Get last sale for repeat functionality
 */
async function getLastSaleForRepeat(business, session, language) {
  try {
    const lastSale = await Sale.findOne({
      business_id: business._id
    }).sort({ sale_date: -1 }).lean();

    if (!lastSale) {
      return getMessage('sales.no_previous_sales', language);
    }

    session.step = 'repeat_sale_confirm';
    session.data = { last_sale: lastSale };

    let response = getMessage('sales.repeat_sale_header', language);

    lastSale.items.forEach(item => {
      response += `\n• ${item.product_name} x${item.quantity} = ${formatCurrency(item.total_price, language)}`;
    });

    response += `\n\n${getMessage('sales.repeat_sale_total', language, {
      total: formatCurrency(lastSale.total_amount, language)
    })}`;

    response += '\n\n' + getMessage('sales.repeat_sale_confirm', language);

    return response;

  } catch (error) {
    logger.error('Last sale retrieval error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Get top products for quick selection
 */
async function getTopProductsQuickSelect(business, language) {
  try {
    const topProducts = await Product.find({
      business_id: business._id,
      is_active: true
    }).select('name price stock is_service sales_count')
      .sort({ sales_count: -1 })
      .limit(10)
      .lean();

    if (topProducts.length === 0) {
      return getMessage('products.list_empty', language);
    }

    let response = getMessage('sales.top_products_header', language);

    topProducts.forEach((product, index) => {
      const stockInfo = product.is_service ?
        getMessage('common.service', language) :
        `${product.stock} ${getMessage('common.in_stock', language)}`;

      response += `\n${index + 1}. ${product.name} - ${formatCurrency(product.price, language)} (${stockInfo}) [${product.sales_count || 0} ${getMessage('sales.times_sold', language)}]`;
    });

    response += '\n\n' + getMessage('sales.top_products_instructions', language);

    return response;

  } catch (error) {
    logger.error('Top products error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Get contextual actions based on report data
 */
function getReportContextualActions(revenue, transactions, language) {
  let actions = getMessage('reports.contextual_actions_header', language);

  if (revenue === 0) {
    // No sales today
    actions += '\n• ' + getMessage('reports.action_record_first_sale', language);
    actions += '\n• ' + getMessage('reports.action_add_products', language);
    actions += '\n• ' + getMessage('reports.action_quick_sale', language);
  } else if (transactions < 5) {
    // Low transaction volume
    actions += '\n• ' + getMessage('reports.action_quick_sale', language);
    actions += '\n• ' + getMessage('reports.action_add_customers', language);
    actions += '\n• ' + getMessage('reports.action_view_products', language);
  } else {
    // Good sales day
    actions += '\n• ' + getMessage('reports.action_record_more_sales', language);
    actions += '\n• ' + getMessage('reports.action_check_stock', language);
    actions += '\n• ' + getMessage('reports.action_weekly_report', language);
  }

  return actions;
}

module.exports = {
  handleWhatsAppMessage,
  processMessage
};
