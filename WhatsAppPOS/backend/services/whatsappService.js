/**
 * WhatsApp Service
 * Comprehensive WhatsApp bot functionality with conversation flow management
 */

const { sendWhatsAppMessage } = require('../config/twilio');
const { sessionManager } = require('../config/redis');
const { getMessage, detectCommand, detectLanguage, formatCurrency, formatDate } = require('../utils/translations');
const { Business, User, Product, Sale, Customer } = require('../models');
const logger = require('../config/logger');
const { monitorWhatsAppMessage, monitorDatabaseQuery, performanceMonitor } = require('../config/monitoring');

/**
 * Send WhatsApp message with optimized retry logic for production performance
 */
async function sendWhatsAppMessageWithRetry(phoneNumber, message, maxRetries = 2) {
  const isDebugMode = process.env.WHATSAPP_DEBUG_MODE === 'true';
  const startTime = Date.now();

  // Fast-fail errors that shouldn't be retried
  const nonRetryableErrors = [
    'exceeded the 9 daily messages limit',
    'invalid phone number',
    'message too long',
    'invalid account'
  ];

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await sendWhatsAppMessage(phoneNumber, message);

      if (result.success) {
        if (attempt > 1) {
          logger.info(`Message sent on attempt ${attempt} (${Date.now() - startTime}ms)`, { phoneNumber });
        }
        return result;
      }

      // Handle daily limit with production-ready fallback
      if (result.error && result.error.includes('exceeded the 9 daily messages limit')) {
        logger.warn(`Twilio daily limit reached (${Date.now() - startTime}ms)`, {
          phoneNumber,
          messageLength: message.length,
          error: result.error
        });

        if (isDebugMode) {
          // Debug mode: Simulate successful sending for testing
          logger.info('Debug mode: Simulating successful send due to daily limit');
          return {
            success: true,
            messageSid: 'DEBUG_MODE_' + Date.now(),
            status: 'sent',
            debugMode: true
          };
        } else {
          // Production mode: Log for monitoring and return failure
          logger.error('PRODUCTION ALERT: Twilio daily limit reached - upgrade account required', {
            phoneNumber,
            timestamp: new Date().toISOString(),
            messageLength: message.length
          });

          // In production, you could:
          // 1. Store message in queue for later delivery
          // 2. Send email notification to admin
          // 3. Switch to backup messaging service
          // 4. Return user-friendly error message

          return {
            success: false,
            error: 'Service temporarily unavailable. Please try again later.',
            errorCode: 'DAILY_LIMIT_EXCEEDED',
            fastFail: true
          };
        }
      }

      // Fast-fail for other non-retryable errors
      const isNonRetryable = nonRetryableErrors.some(err =>
        result.error && result.error.toLowerCase().includes(err.toLowerCase())
      );

      if (isNonRetryable) {
        logger.warn(`Fast-fail for non-retryable error (${Date.now() - startTime}ms):`, {
          phoneNumber,
          error: result.error
        });
        return {
          success: false,
          error: result.error,
          fastFail: true
        };
      }

      // Only retry for network/temporary errors
      if (attempt < maxRetries) {
        // Reduced delay: 200ms, 500ms instead of 1s, 2s, 4s
        const delay = attempt === 1 ? 200 : 500;
        logger.warn(`Retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries}):`, {
          phoneNumber,
          error: result.error
        });
        await new Promise(resolve => setTimeout(resolve, delay));
      }

    } catch (error) {
      // Fast-fail for connection errors in debug mode
      if (isDebugMode && error.message.includes('getaddrinfo ENOTFOUND')) {
        logger.info(`Debug mode: Network error fast-fail (${Date.now() - startTime}ms)`, {
          phoneNumber,
          error: error.message
        });
        return {
          success: true,
          messageSid: 'DEBUG_MODE_NETWORK_' + Date.now(),
          status: 'sent',
          debugMode: true
        };
      }

      logger.error(`Attempt ${attempt} error (${Date.now() - startTime}ms):`, {
        phoneNumber,
        error: error.message
      });

      if (attempt === maxRetries) {
        return {
          success: false,
          error: `Failed after ${maxRetries} attempts: ${error.message}`
        };
      }

      // Quick retry for network errors
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }
  }

  return {
    success: false,
    error: `Failed after ${maxRetries} attempts`
  };
}

/**
 * Main WhatsApp message handler
 */
async function handleWhatsAppMessage(req, res) {
  const startTime = Date.now();

  try {
    const { From, Body, MessageSid } = req.body;
    let phoneNumber = From.replace('whatsapp:', '').trim();

    // Ensure phone number has + prefix for international format
    if (!phoneNumber.startsWith('+')) {
      phoneNumber = '+' + phoneNumber;
    }

    const messageBody = Body.trim();

    logger.whatsapp('Received WhatsApp message', {
      from: phoneNumber,
      body: messageBody,
      messageSid: MessageSid
    });

    // Track message received
    logger.whatsapp('Message received', {
      from: phoneNumber,
      body: messageBody,
      messageSid: MessageSid
    });

    // Get or create session
    let session = await sessionManager.getSession(phoneNumber);
    if (!session) {
      session = {
        phone: phoneNumber,
        step: 'welcome',
        language: 'sw',
        data: {}
      };
    }

    // Detect language preference from message content or use session language
    let language = session.language;

    // Check if user is explicitly switching language
    if (messageBody.toLowerCase().includes('english') || messageBody.toLowerCase().includes('en')) {
      language = 'en';
    } else if (messageBody.toLowerCase().includes('swahili') || messageBody.toLowerCase().includes('sw')) {
      language = 'sw';
    }

    session.language = language;

    // Process the message
    const response = await processMessage(phoneNumber, messageBody, session);

    // Send response with retry logic
    if (response) {
      const result = await sendWhatsAppMessageWithRetry(phoneNumber, response);

      // Track message sending
      logger.whatsapp('Message sent', {
        to: phoneNumber,
        duration: Date.now() - startTime,
        success: result.success
      });

      if (!result.success) {
        logger.error('Failed to send WhatsApp message after retries:', {
          phone: phoneNumber,
          error: result.error,
          response: response.substring(0, 100) + '...'
        });

        // Check if it's a daily limit error
        if (result.error && result.error.includes('exceeded the 9 daily messages limit')) {
          logger.warn('Twilio daily message limit reached - system in degraded mode', {
            phone: phoneNumber,
            timestamp: new Date().toISOString()
          });

          // In production, you might want to:
          // 1. Store the response in database for later delivery
          // 2. Send email notification to user
          // 3. Queue message for next day delivery
          // 4. Switch to backup messaging service
        }
      }
    }

    // Update session (optimized)
    await sessionManager.setSession(phoneNumber, session, 1800); // 30 minutes

    // Track total processing time and memory usage
    const processingTime = Date.now() - startTime;
    const memoryUsage = process.memoryUsage();

    if (processingTime > 1000) { // Reduced threshold from 2000ms to 1000ms
      logger.warn('Slow WhatsApp message processing', {
        phoneNumber,
        processingTime,
        messageLength: messageBody.length,
        memoryUsageMB: Math.round(memoryUsage.heapUsed / 1024 / 1024)
      });
    }

    // Memory leak detection
    if (memoryUsage.heapUsed > 100 * 1024 * 1024) { // 100MB threshold
      logger.warn('High memory usage detected', {
        heapUsedMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotalMB: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        rssMB: Math.round(memoryUsage.rss / 1024 / 1024)
      });

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
        logger.info('Forced garbage collection executed');
      }
    }

    res.status(200).send('OK');

  } catch (error) {
    logger.error('WhatsApp message handling error:', error);
    res.status(500).send('Error processing message');
  }
}

/**
 * Process incoming message based on current session state
 */
async function processMessage(phoneNumber, messageBody, session) {
  const language = session.language;
  const globalCommand = detectCommand(messageBody, language);

  logger.info(`Processing message: "${messageBody}" from ${phoneNumber}`);
  logger.info(`Detected command: "${globalCommand}", Language: ${language}`);

  // Handle global commands first (work in any context)
  if (globalCommand === 'help' || globalCommand === 'msaada') {
    logger.info('Returning help message');
    session.step = 'welcome';
    session.data = {};
    return getHelpMessage(language);
  }

  if (globalCommand === 'cancel' || globalCommand === 'ghairi') {
    logger.info('Cancelling current operation');
    session.step = 'welcome';
    session.data = {};
    return getMessage('common.cancel', language) + '\n\n' + getMainMenu(language);
  }

  if (globalCommand === 'back' || globalCommand === 'rudi') {
    logger.info('Going back to previous step');
    return handleBackCommand(session, language);
  }

  if (globalCommand === 'menu' || globalCommand === 'menyu') {
    logger.info('Returning to main menu');
    session.step = 'welcome';
    session.data = {};
    return getMainMenu(language);
  }

  // Handle language switching
  if (messageBody.toLowerCase() === 'english' || messageBody === '🇬🇧' || messageBody === 'en') {
    session.language = 'en';
    session.step = 'welcome'; // Reset to welcome
    return getMessage('language.switched_to_english', 'en') + '\n\n' + getMainMenu('en');
  }

  if (messageBody.toLowerCase() === 'swahili' || messageBody === '🇹🇿' || messageBody === 'sw') {
    session.language = 'sw';
    session.step = 'welcome'; // Reset to welcome
    return getMessage('language.switched_to_swahili', 'sw') + '\n\n' + getMainMenu('sw');
  }

  // Handle numbered menu options contextually
  const input = messageBody.trim();

  // Only process global menu options if we're in welcome step or no active flow
  const isInActiveFlow = ['add_product', 'record_sale', 'add_customer', 'registration'].includes(session.step);

  let globalMenuCommand = null;
  if (!isInActiveFlow && /^[0-9]$/.test(input)) {
    const menuOptions = {
      '1': 'record_sale',
      '2': 'add_product',
      '3': 'list_products',
      '4': 'add_customer',
      '5': 'list_customers',
      '6': 'daily_report',
      '7': 'weekly_report',
      '8': 'stock_check',
      '9': 'business_info',
      '0': 'help'
    };

    globalMenuCommand = menuOptions[input];
    if (globalMenuCommand) {
      logger.info(`Detected global menu option: ${input} -> ${globalMenuCommand}`);
      // Don't reset session.step here - let the welcome handler manage it
    }
  }

  // Check if user has a registered business (with optimized timeout handling)
  let business = null;
  const dbStartTime = Date.now();

  try {
    business = await Promise.race([
      Business.findByPhone(phoneNumber),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database timeout')), 2000) // Reduced from 5s to 2s
      )
    ]);

    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Business lookup completed', { duration: dbQueryTime, success: true });

    logger.info(`Business lookup: ${business ? 'Found ' + business.name : 'No business'} (${dbQueryTime}ms)`);
  } catch (error) {
    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Business lookup failed', { duration: dbQueryTime, success: false, error: error.message });

    logger.warn(`Database lookup failed (${dbQueryTime}ms):`, error.message);
    // Continue without business - user can still access basic features
  }

  // Allow certain commands without business registration
  const allowedWithoutBusiness = ['help', 'menu', 'register', 'business_info'];

  if (!business && session.step !== 'registration' && !allowedWithoutBusiness.includes(globalCommand)) {
    // Check if it's a numbered option that requires business
    const businessRequiredNumbers = ['1', '2', '3', '4', '5', '6', '7', '8'];
    if (businessRequiredNumbers.includes(messageBody.trim())) {
      return getRegistrationPrompt(language);
    }
  }

  // Route to appropriate handler based on current step
  logger.info(`Processing step: ${session.step}`);

  let result;
  switch (session.step) {
    case 'welcome':
      logger.info('Handling welcome step');
      result = await handleWelcomeStep(messageBody, session, business, language);
      break;

    case 'registration':
      logger.info('Handling registration flow');
      result = await handleRegistrationFlow(messageBody, session, language);
      break;

    case 'add_product':
      logger.info('Handling add product flow');
      result = await handleAddProductFlow(messageBody, session, business, language);
      break;

    case 'record_sale':
      logger.info('Handling record sale flow');
      result = await handleRecordSaleFlow(messageBody, session, business, language);
      break;

    case 'add_customer':
      logger.info('Handling add customer flow');
      result = await handleAddCustomerFlow(messageBody, session, business, language);
      break;

    case 'edit_product':
      logger.info('Handling edit product flow');
      result = await handleEditProductFlow(messageBody, session, business, language);
      break;

    case 'delete_product':
      logger.info('Handling delete product flow');
      result = await handleDeleteProductFlow(messageBody, session, business, language);
      break;

    default:
      logger.info('Handling default welcome step');
      result = await handleWelcomeStep(messageBody, session, business, language);
      break;
  }

  logger.info(`Result from step processing: ${result ? 'Got response (' + result.length + ' chars)' : 'No response'}`);
  return result;
}

/**
 * Handle back command - context-aware navigation
 */
function handleBackCommand(session, language) {
  const { step, data } = session;

  switch (step) {
    case 'add_product':
      if (data.name && !data.price) {
        // Go back to name input
        delete data.name;
        return getMessage('products.add_start', language);
      } else if (data.price && !data.stock) {
        // Go back to price input
        delete data.price;
        return getMessage('products.name_received', language);
      } else if (data.stock && !data.category) {
        // Go back to stock input
        delete data.stock;
        return getMessage('products.price_received', language);
      } else {
        // Go back to main menu
        session.step = 'welcome';
        session.data = {};
        return getMainMenu(language);
      }

    case 'record_sale':
      if (data.items && data.items.length > 0) {
        // Remove last item
        data.items.pop();
        return getMessage('sales.item_removed', language) + '\n\n' +
               getMessage('sales.continue_or_finish', language);
      } else {
        // Go back to main menu
        session.step = 'welcome';
        session.data = {};
        return getMainMenu(language);
      }

    case 'add_customer':
      if (data.name && !data.phone) {
        // Go back to name input
        delete data.name;
        return getMessage('customers.add_start', language);
      } else {
        // Go back to main menu
        session.step = 'welcome';
        session.data = {};
        return getMainMenu(language);
      }

    case 'registration':
      if (data.name && !data.type) {
        // Go back to name input
        delete data.name;
        return getBusinessRegistrationStart(language);
      } else if (data.type && !data.owner_name) {
        // Go back to type selection
        delete data.type;
        return getBusinessTypeMessage(language);
      } else {
        // Go back to main menu
        session.step = 'welcome';
        session.data = {};
        return getMainMenu(language);
      }

    default:
      // Default: go to main menu
      session.step = 'welcome';
      session.data = {};
      return getMainMenu(language);
  }
}

/**
 * Handle welcome step and main commands
 */
async function handleWelcomeStep(messageBody, session, business, language) {
  const input = messageBody.trim();
  const detectedCommand = detectCommand(messageBody, language);

  // Handle numbered menu options
  const menuOptions = {
    '1': 'record_sale',
    '2': 'add_product',
    '3': 'list_products',
    '4': 'add_customer',
    '5': 'list_customers',
    '6': 'daily_report',
    '7': 'weekly_report',
    '8': 'stock_check',
    '9': 'business_info',
    '0': 'help'
  };

  // Handle registration commands and menu options
  let selectedCommand;
  if (input.toLowerCase().includes('register') || input.toLowerCase().includes('sajili')) {
    selectedCommand = 'register';
  } else if (/^[0-9]$/.test(input)) {
    // Numbered menu option
    selectedCommand = menuOptions[input];
  } else {
    // Text command
    selectedCommand = detectedCommand;
  }

  // Handle commands that require business registration
  if (!business && ['record_sale', 'add_product', 'list_products', 'daily_report', 'weekly_report', 'add_customer', 'list_customers', 'stock_check'].includes(selectedCommand)) {
    return getRegistrationPrompt(language);
  }

  switch (selectedCommand) {
    case 'record_sale':
      session.step = 'record_sale';
      session.data = {};
      return await startSaleFlow(business, language);

    case 'add_product':
      session.step = 'add_product';
      session.data = {};
      return getMessage('products.add_start', language);

    case 'list_products':
      return await listProducts(business, language);

    case 'edit_product':
      session.step = 'edit_product';
      session.data = {};
      return await listProductsForEdit(business, language);

    case 'delete_product':
      session.step = 'delete_product';
      session.data = {};
      return await listProductsForDelete(business, language);

    case 'daily_report':
      return await generateDailyReport(business, language);

    case 'weekly_report':
      return await generateWeeklyReport(business, language);

    case 'add_customer':
      session.step = 'add_customer';
      session.data = {};
      return getMessage('customers.add_start', language);

    case 'list_customers':
      return await listCustomers(business, language);

    case 'stock_check':
      return await checkLowStock(business, language);

    case 'business_info':
      if (business) {
        return await getBusinessInfo(business, language);
      } else {
        return getSystemInfo(language);
      }

    case 'help':
      return getHelpMessage(language);

    case 'register':
      session.step = 'registration';
      session.data = {};
      return getBusinessRegistrationStart(language);

    default:
      return getMainMenu(language);
  }
}

/**
 * Handle business registration flow
 */
async function handleRegistrationFlow(messageBody, session, language) {
  const { data } = session;

  if (!data.name) {
    // Step 1: Get business name
    data.name = messageBody.trim();
    return getBusinessTypeMessage(language);
  }

  if (!data.type) {
    // Step 2: Get business type
    const typeMap = {
      '1': 'duka', '2': 'salon', '3': 'restaurant', '4': 'pharmacy',
      '5': 'electronics', '6': 'clothing', '7': 'hardware', '8': 'services', '9': 'other'
    };

    data.type = typeMap[messageBody.trim()] || 'other';
    return getOwnerNameMessage(language);
  }

  if (!data.owner_name) {
    // Step 3: Get owner name
    data.owner_name = messageBody.trim();

    // Create business
    try {
      const business = new Business({
        name: data.name,
        phone: session.phone,
        owner_name: data.owner_name,
        business_type: data.type,
        settings: { language }
      });

      await business.save();

      // Create owner user
      const owner = new User({
        business_id: business._id,
        phone: session.phone,
        name: data.owner_name,
        role: 'owner',
        created_by: business._id
      });

      await owner.save();

      // Reset session
      session.step = 'welcome';
      session.data = {};

      return getMessage('business_registration.success', language, {
        phone: session.phone,
        name: data.name,
        owner: data.owner_name
      });

    } catch (error) {
      logger.error('Business registration error:', error);
      return getMessage('errors.network_error', language);
    }
  }
}

/**
 * Handle add product flow
 */
async function handleAddProductFlow(messageBody, session, business, language) {
  const { data } = session;

  if (!data.name) {
    data.name = messageBody.trim();
    return getMessage('products.name_received', language);
  }

  if (!data.price) {
    const price = parseFloat(messageBody.replace(/[^\d.]/g, ''));
    if (isNaN(price) || price <= 0) {
      return getMessage('errors.invalid_price', language);
    }
    data.price = price;
    return getMessage('products.price_received', language);
  }

  if (!data.stock) {
    const stock = parseInt(messageBody.replace(/[^\d]/g, ''));
    if (isNaN(stock) || stock < 0) {
      return getMessage('errors.invalid_quantity', language);
    }
    data.stock = stock;
    return getMessage('products.category_prompt', language);
  }

  if (!data.category) {
    const categoryMap = {
      '1': 'food', '2': 'drinks', '3': 'electronics', '4': 'clothing',
      '5': 'beauty', '6': 'health', '7': 'household', '8': 'stationery',
      '9': 'services', '10': 'other'
    };

    data.category = categoryMap[messageBody.trim()] || 'other';

    // Create product
    try {
      const owner = await User.findOne({ business_id: business._id, role: 'owner' });

      const product = new Product({
        business_id: business._id,
        name: data.name,
        price: data.price,
        stock: data.stock,
        category: data.category,
        is_service: data.category === 'services',
        created_by: owner._id
      });

      await product.save();

      // Update business stats
      await business.updateStats();

      // Reset session
      session.step = 'welcome';
      session.data = {};

      return getMessage('products.success', language, {
        name: data.name,
        price: formatCurrency(data.price, language),
        stock: data.stock,
        category: data.category
      });

    } catch (error) {
      logger.error('Product creation error:', error);
      return getMessage('errors.network_error', language);
    }
  }
}

/**
 * Handle record sale flow
 */
async function handleRecordSaleFlow(messageBody, session, business, language) {
  const { data } = session;

  if (!data.product_id) {
    // Parse product selection (optimized)
    const productIndex = parseInt(messageBody.trim()) - 1;
    const dbStartTime = Date.now();

    const products = await Product.find({
      business_id: business._id,
      is_active: true
    }).select('name price stock is_service').limit(20).lean();

    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Product query completed', { duration: dbQueryTime, success: true });

    if (productIndex < 0 || productIndex >= products.length) {
      return getMessage('errors.product_not_found', language);
    }

    const product = products[productIndex];
    data.product_id = product._id;
    data.product_name = product.name;
    data.product_price = product.price;
    data.available_stock = product.stock;

    return getMessage('sales.product_selected', language, { product: product.name });
  }

  if (!data.quantity) {
    const quantity = parseFloat(messageBody.replace(/[^\d.]/g, ''));
    if (isNaN(quantity) || quantity <= 0) {
      return getMessage('errors.invalid_quantity', language);
    }

    if (quantity > data.available_stock) {
      return getMessage('errors.insufficient_stock', language, {
        available: data.available_stock
      });
    }

    data.quantity = quantity;
    return getPaymentOptionsMessage(language);
  }

  if (!data.payment_method) {
    const paymentMap = {
      '1': 'cash', '2': 'mpesa', '3': 'tigo', '4': 'airtel', '5': 'bank'
    };

    data.payment_method = paymentMap[messageBody.trim()] || 'cash';

    // Create sale
    try {
      const owner = await User.findOne({ business_id: business._id, role: 'owner' });
      const total = data.quantity * data.product_price;

      // Generate sale number
      const saleCount = await Sale.countDocuments({ business_id: business._id });
      const saleNumber = `${business.name.substring(0, 3).toUpperCase()}-${Date.now()}-${saleCount + 1}`;

      const sale = new Sale({
        business_id: business._id,
        user_id: owner._id,
        sale_number: saleNumber,
        items: [{
          product_id: data.product_id,
          product_name: data.product_name,
          quantity: data.quantity,
          unit_price: data.product_price,
          total_price: total
        }],
        subtotal: total,
        total: total,
        payment: {
          method: data.payment_method,
          amount_paid: total,
          status: 'completed'
        }
      });

      await sale.save();

      // Update product stock
      const product = await Product.findById(data.product_id);
      if (!product.is_service) {
        product.stock -= data.quantity;
        product.sales_data.total_sold += data.quantity;
        product.sales_data.last_sold_date = new Date();
        product.sales_data.revenue_generated += total;
        await product.save();
      }

      // Update business stats
      await business.updateStats();

      // Reset session
      session.step = 'welcome';
      session.data = {};

      const paymentMethods = {
        cash: language === 'sw' ? 'Fedha taslimu' : 'Cash',
        mpesa: 'M-Pesa',
        tigo: 'Tigo Pesa',
        airtel: 'Airtel Money',
        bank: language === 'sw' ? 'Benki' : 'Bank'
      };

      return getMessage('sales.success', language, {
        receipt: sale.receipt.number,
        product: data.product_name,
        quantity: data.quantity,
        total: formatCurrency(total, language),
        payment: paymentMethods[data.payment_method],
        date: formatDate(new Date(), language)
      });

    } catch (error) {
      logger.error('Sale creation error:', error);
      return getMessage('errors.network_error', language);
    }
  }
}

/**
 * Handle add customer flow
 */
async function handleAddCustomerFlow(messageBody, session, business, language) {
  const { data } = session;

  if (!data.name) {
    data.name = messageBody.trim();
    return getMessage('customers.name_received', language);
  }

  if (!data.phone_processed) {
    const phone = messageBody.trim();
    data.phone = phone === '' ? null : phone;
    data.phone_processed = true;

    // Create customer
    try {
      const owner = await User.findOne({ business_id: business._id, role: 'owner' });

      const customer = new Customer({
        business_id: business._id,
        name: data.name,
        phone: data.phone,
        created_by: owner._id
      });

      await customer.save();

      // Update business stats
      await business.updateStats();

      // Reset session
      session.step = 'welcome';
      session.data = {};

      return getMessage('customers.success', language, {
        name: data.name,
        phone: data.phone || (language === 'sw' ? 'Hakuna' : 'None')
      });

    } catch (error) {
      logger.error('Customer creation error:', error);
      return getMessage('errors.network_error', language);
    }
  }
}

/**
 * List products (optimized with monitoring)
 */
async function listProducts(business, language) {
  const dbStartTime = Date.now();

  try {
    const products = await Product.find({
      business_id: business._id,
      is_active: true
    }).limit(20).lean(); // Use lean() for better performance

    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Products list query completed', { duration: dbQueryTime, success: true });

    if (products.length === 0) {
      return getMessage('products.list_empty', language);
    }

    let response = getMessage('products.list_header', language);

    products.forEach((product, index) => {
      response += getMessage('products.list_item', language, {
        index: index + 1,
        name: product.name,
        price: formatCurrency(product.price, language),
        stock: product.stock
      });
    });

    return response;

  } catch (error) {
    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Products list query failed', { duration: dbQueryTime, success: false, error: error.message });

    logger.error(`List products error (${dbQueryTime}ms):`, error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Start sale flow (optimized with monitoring)
 */
async function startSaleFlow(business, language) {
  const dbStartTime = Date.now();

  try {
    // Use lean() and select only needed fields for performance
    const products = await Product.find({
      business_id: business._id,
      is_active: true,
      $or: [
        { is_service: true },
        { stock: { $gt: 0 } }
      ]
    }).select('name price stock is_service').limit(20).lean();

    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Sale flow started', { duration: dbQueryTime, success: true });

    if (products.length === 0) {
      return getMessage('products.list_empty', language);
    }

    let response = getMessage('sales.start', language);

    products.forEach((product, index) => {
      response += `${index + 1}. *${product.name}*\n   💰 ${formatCurrency(product.price, language)}`;
      if (!product.is_service) {
        response += ` | 📊 ${product.stock} ${language === 'sw' ? 'zimesalia' : 'available'}`;
      }
      response += '\n';
    });

    return response;

  } catch (error) {
    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Sale flow failed', { duration: dbQueryTime, success: false, error: error.message });

    logger.error(`Start sale flow error (${dbQueryTime}ms):`, error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Generate daily report (optimized with monitoring)
 */
async function generateDailyReport(business, language) {
  const dbStartTime = Date.now();

  try {
    const today = new Date();
    const startOfDay = new Date(today);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    // Use lean() and select only needed fields for performance
    const sales = await Sale.find({
      business_id: business._id,
      sale_date: { $gte: startOfDay, $lte: endOfDay },
      status: 'completed'
    }).select('total total_items customer_id items').lean();

    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Daily report query completed', { duration: dbQueryTime, success: true });

    const revenue = sales.reduce((total, sale) => total + sale.total, 0);
    const itemsSold = sales.reduce((total, sale) => total + sale.total_items, 0);
    const uniqueCustomers = new Set(sales.map(sale => sale.customer_id).filter(Boolean)).size;

    // Get top products (optimized)
    const productSales = {};
    sales.forEach(sale => {
      if (sale.items) {
        sale.items.forEach(item => {
          if (!productSales[item.product_name]) {
            productSales[item.product_name] = 0;
          }
          productSales[item.product_name] += item.quantity;
        });
      }
    });

    const topProducts = Object.entries(productSales)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([name, qty]) => `• ${name}: ${qty}`)
      .join('\n');

    return getMessage('sales.daily_report', language, {
      date: formatDate(today, language),
      revenue: formatCurrency(revenue, language),
      items: itemsSold,
      sales: sales.length,
      customers: uniqueCustomers,
      top_products: topProducts || (language === 'sw' ? 'Hakuna' : 'None')
    });

  } catch (error) {
    const dbQueryTime = Date.now() - dbStartTime;
    logger.database('Daily report query failed', { duration: dbQueryTime, success: false, error: error.message });

    logger.error(`Daily report error (${dbQueryTime}ms):`, error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Generate weekly report
 */
async function generateWeeklyReport(business, language) {
  // Similar to daily report but for the past 7 days
  // Implementation would be similar to generateDailyReport
  return getMessage('errors.network_error', language); // Placeholder
}

/**
 * List customers
 */
async function listCustomers(business, language) {
  try {
    const customers = await Customer.find({
      business_id: business._id,
      is_active: true
    }).limit(20);

    if (customers.length === 0) {
      return language === 'sw' ?
        'Hakuna wateja bado. Andika "ongeza mteja" kuanza.' :
        'No customers yet. Type "add customer" to start.';
    }

    let response = language === 'sw' ? '👥 *WATEJA WAKO*\n\n' : '👥 *YOUR CUSTOMERS*\n\n';

    customers.forEach((customer, index) => {
      response += `${index + 1}. *${customer.name}*\n`;
      if (customer.phone) {
        response += `   📱 ${customer.phone}\n`;
      }
      response += `   💰 ${formatCurrency(customer.purchase_history.total_spent, language)}\n\n`;
    });

    return response;

  } catch (error) {
    logger.error('List customers error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Check low stock
 */
async function checkLowStock(business, language) {
  try {
    const lowStockProducts = await Product.find({
      business_id: business._id,
      is_active: true,
      is_service: false,
      $expr: { $lte: ['$stock', '$low_stock_alert'] }
    });

    if (lowStockProducts.length === 0) {
      return language === 'sw' ?
        '✅ Bidhaa zote zina hisa ya kutosha!' :
        '✅ All products have sufficient stock!';
    }

    let response = language === 'sw' ?
      '⚠️ *BIDHAA ZILIZOPUNGUA*\n\n' :
      '⚠️ *LOW STOCK PRODUCTS*\n\n';

    lowStockProducts.forEach(product => {
      response += `• *${product.name}*: ${product.stock} ${language === 'sw' ? 'zimesalia' : 'remaining'}\n`;
    });

    return response;

  } catch (error) {
    logger.error('Check low stock error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Get business information
 */
async function getBusinessInfo(business, language) {
  const daysRemaining = business.days_remaining;
  const status = business.subscription_active ?
    (language === 'sw' ? 'Inatumika' : 'Active') :
    (language === 'sw' ? 'Imekwisha' : 'Expired');

  return language === 'sw' ?
    `🏪 *MAELEZO YA BIASHARA*\n\n` +
    `📛 Jina: ${business.name}\n` +
    `👤 Mmiliki: ${business.owner_name}\n` +
    `📱 Simu: ${business.phone}\n` +
    `🏷️ Aina: ${business.business_type}\n` +
    `📅 Usajili: ${status}\n` +
    `⏰ Siku zilizosalia: ${daysRemaining}\n` +
    `📊 Bidhaa: ${business.stats.total_products}\n` +
    `💰 Mapato: ${formatCurrency(business.stats.total_revenue, language)}` :

    `🏪 *BUSINESS INFORMATION*\n\n` +
    `📛 Name: ${business.name}\n` +
    `👤 Owner: ${business.owner_name}\n` +
    `📱 Phone: ${business.phone}\n` +
    `🏷️ Type: ${business.business_type}\n` +
    `📅 Subscription: ${status}\n` +
    `⏰ Days remaining: ${daysRemaining}\n` +
    `📊 Products: ${business.stats.total_products}\n` +
    `💰 Revenue: ${formatCurrency(business.stats.total_revenue, language)}`;
}

/**
 * Get business registration start message
 */
function getBusinessRegistrationStart(language) {
  if (language === 'sw') {
    return `🏪 *KARIBU KWENYE USAJILI WA BIASHARA*

Tutakusaidia kusajili biashara yako hatua kwa hatua.

📝 *HATUA YA KWANZA:*
Andika jina la biashara yako

💡 *Mfano:* Duka la Mama Fatuma, Salon Beauty, Mkahawa Kilimanjaro

Andika jina la biashara yako:`;
  } else {
    return `🏪 *WELCOME TO BUSINESS REGISTRATION*

We'll help you register your business step by step.

📝 *FIRST STEP:*
Enter your business name

💡 *Example:* Mama Fatuma Shop, Beauty Salon, Kilimanjaro Restaurant

Enter your business name:`;
  }
}

/**
 * Get system info for unregistered users
 */
function getSystemInfo(language) {
  if (language === 'sw') {
    return `ℹ️ *KUHUSU WHATSAPP POS TANZANIA*

🏪 *NI NINI:*
Mfumo wa kisasa wa mauzo kupitia WhatsApp

✨ *FAIDA:*
• Rekodi mauzo haraka
• Fuatilia hisa za bidhaa
• Pata ripoti za kila siku
• Ongeza wateja na bidhaa
• Huduma ya Kiswahili

🚀 *JINSI YA KUANZA:*
1. Sajili biashara yako
2. Ongeza bidhaa zako
3. Anza kurekodi mauzo

📞 *MSAADA:* Andika "msaada" wakati wowote`;
  } else {
    return `ℹ️ *ABOUT WHATSAPP POS TANZANIA*

🏪 *WHAT IT IS:*
Modern sales system via WhatsApp

✨ *BENEFITS:*
• Record sales quickly
• Track product inventory
• Get daily reports
• Add customers and products
• Swahili language support

🚀 *HOW TO START:*
1. Register your business
2. Add your products
3. Start recording sales

📞 *HELP:* Type "help" anytime`;
  }
}

/**
 * Get registration prompt for users without business
 */
function getRegistrationPrompt(language) {
  if (language === 'sw') {
    return `🏪 *SAJILI BIASHARA YAKO KWANZA*

Ili kutumia huduma za mauzo, unahitaji kusajili biashara yako kwanza.

📝 *JINSI YA KUSAJILI:*
• Andika "sajili" au "register"
• Fuata maelekezo

💡 *AU CHAGUA:*
0️⃣ Msaada - Ona maelekezo kamili
9️⃣ Maelezo - Jifunze kuhusu mfumo

Andika chaguo lako:`;
  } else {
    return `🏪 *REGISTER YOUR BUSINESS FIRST*

To use sales features, you need to register your business first.

📝 *HOW TO REGISTER:*
• Type "register" or "sajili"
• Follow the instructions

💡 *OR CHOOSE:*
0️⃣ Help - View complete instructions
9️⃣ Info - Learn about the system

Type your choice:`;
  }
}

/**
 * Get business type selection message
 */
function getBusinessTypeMessage(language) {
  if (language === 'sw') {
    return `🏪 *CHAGUA AINA YA BIASHARA:*

1️⃣ Duka la Jumla
2️⃣ Salon/Urembo
3️⃣ Mkahawa/Hoteli
4️⃣ Duka la Dawa
5️⃣ Vifaa vya Umeme
6️⃣ Nguo na Mavazi
7️⃣ Vifaa vya Ujenzi
8️⃣ Huduma
9️⃣ Nyingine

💡 Andika nambari ya chaguo lako`;
  } else {
    return `🏪 *SELECT BUSINESS TYPE:*

1️⃣ General Store
2️⃣ Salon/Beauty
3️⃣ Restaurant/Hotel
4️⃣ Pharmacy
5️⃣ Electronics
6️⃣ Clothing
7️⃣ Hardware
8️⃣ Services
9️⃣ Other

💡 Type the number of your choice`;
  }
}

/**
 * Get owner name message
 */
function getOwnerNameMessage(language) {
  if (language === 'sw') {
    return `👤 *ANDIKA JINA LA MMILIKI:*

💡 Andika jina kamili la mmiliki wa biashara`;
  } else {
    return `👤 *ENTER OWNER NAME:*

💡 Type the full name of the business owner`;
  }
}

/**
 * Get payment options message
 */
function getPaymentOptionsMessage(language) {
  if (language === 'sw') {
    return `💳 *CHAGUA NJIA YA MALIPO:*

1️⃣ Fedha Taslimu (Cash)
2️⃣ M-Pesa
3️⃣ Tigo Pesa
4️⃣ Airtel Money
5️⃣ Benki (Bank Transfer)

💡 Andika nambari ya chaguo lako`;
  } else {
    return `💳 *SELECT PAYMENT METHOD:*

1️⃣ Cash
2️⃣ M-Pesa
3️⃣ Tigo Pesa
4️⃣ Airtel Money
5️⃣ Bank Transfer

💡 Type the number of your choice`;
  }
}

/**
 * Get main menu with numbered options
 */
function getMainMenu(language) {
  if (language === 'sw') {
    return `🏪 *KARIBU WHATSAPP POS TANZANIA* 🇹🇿

📋 *MENYU KAVU:*
1️⃣ Rekodi Mauzo
2️⃣ Ongeza Bidhaa
3️⃣ Orodha ya Bidhaa
4️⃣ Ongeza Mteja
5️⃣ Orodha ya Wateja
6️⃣ Ripoti ya Leo
7️⃣ Ripoti ya Wiki
8️⃣ Angalia Hisa
9️⃣ Taarifa za Biashara
0️⃣ Msaada

📝 *AMRI ZA MAANDISHI:*
• "hariri bidhaa" - Badilisha bidhaa
• "futa bidhaa" - Futa bidhaa
• "menu" - Kurudi menyuni
• "cancel" - Ghairi kitendo
• "back" - Rudi nyuma

🌐 *LUGHA:* Andika "English" kubadili lugha
💡 *VIDOKEZO:* Andika nambari au neno la amri`;
  } else {
    return `🏪 *WELCOME TO WHATSAPP POS TANZANIA* 🇹🇿

📋 *MAIN MENU:*
1️⃣ Record Sale
2️⃣ Add Product
3️⃣ List Products
4️⃣ Add Customer
5️⃣ List Customers
6️⃣ Daily Report
7️⃣ Weekly Report
8️⃣ Check Stock
9️⃣ Business Info
0️⃣ Help

📝 *TEXT COMMANDS:*
• "edit product" - Modify products
• "delete product" - Remove products
• "menu" - Return to menu
• "cancel" - Cancel action
• "back" - Go back

🌐 *LANGUAGE:* Type "Swahili" to switch language
💡 *TIP:* Type number or command word`;
  }
}

/**
 * Get help message with all commands
 */
function getHelpMessage(language) {
  if (language === 'sw') {
    return `📚 *MSAADA - AMRI ZOTE*

🛒 *MAUZO:*
• "1" au "mauzo" - Rekodi mauzo
• "6" au "ripoti leo" - Ripoti ya leo
• "7" au "ripoti wiki" - Ripoti ya wiki

📦 *BIDHAA:*
• "2" au "ongeza bidhaa" - Ongeza bidhaa mpya
• "3" au "orodha bidhaa" - Ona bidhaa zote
• "8" au "hisa" - Angalia hisa

👥 *WATEJA:*
• "4" au "ongeza mteja" - Ongeza mteja mpya
• "5" au "orodha wateja" - Ona wateja wote

ℹ️ *MENGINEYO:*
• "9" au "taarifa" - Taarifa za biashara
• "ghairi" - Ghairi shughuli
• "English" - Badili lugha

💡 Andika nambari au neno la amri`;
  } else {
    return `📚 *HELP - ALL COMMANDS*

🛒 *SALES:*
• "1" or "sale" - Record a sale
• "6" or "daily report" - Today's report
• "7" or "weekly report" - Weekly report

📦 *PRODUCTS:*
• "2" or "add product" - Add new product
• "3" or "list products" - View all products
• "8" or "stock" - Check stock levels

👥 *CUSTOMERS:*
• "4" or "add customer" - Add new customer
• "5" or "list customers" - View all customers

ℹ️ *OTHER:*
• "9" or "info" - Business information
• "cancel" - Cancel current action
• "Swahili" - Switch language

💡 Type number or command word`;
  }
}

/**
 * List products for editing with numbered options
 */
async function listProductsForEdit(business, language) {
  try {
    const products = await Product.find({
      business_id: business._id,
      is_active: true
    }).limit(20).lean();

    if (products.length === 0) {
      return getMessage('products.list_empty', language);
    }

    let response = getMessage('products.edit_select', language);

    products.forEach((product, index) => {
      response += `\n${index + 1}. ${product.name} - ${formatCurrency(product.price, language)}`;
    });

    response += `\n\n${getMessage('products.edit_instructions', language)}`;
    return response;

  } catch (error) {
    logger.error('List products for edit error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * List products for deletion with numbered options
 */
async function listProductsForDelete(business, language) {
  try {
    const products = await Product.find({
      business_id: business._id,
      is_active: true
    }).limit(20).lean();

    if (products.length === 0) {
      return getMessage('products.list_empty', language);
    }

    let response = getMessage('products.delete_select', language);

    products.forEach((product, index) => {
      response += `\n${index + 1}. ${product.name} - ${formatCurrency(product.price, language)}`;
    });

    response += `\n\n${getMessage('products.delete_warning', language)}`;
    return response;

  } catch (error) {
    logger.error('List products for delete error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Handle edit product flow
 */
async function handleEditProductFlow(messageBody, session, business, language) {
  const { data } = session;

  if (!data.product_id) {
    // Step 1: Select product to edit
    const productIndex = parseInt(messageBody.trim()) - 1;

    try {
      const products = await Product.find({
        business_id: business._id,
        is_active: true
      }).limit(20);

      if (productIndex >= 0 && productIndex < products.length) {
        data.product_id = products[productIndex]._id;
        data.product = products[productIndex];
        return getMessage('products.edit_field_select', language, {
          name: products[productIndex].name
        });
      } else {
        return getMessage('errors.invalid_selection', language);
      }
    } catch (error) {
      logger.error('Product selection error:', error);
      return getMessage('errors.network_error', language);
    }
  }

  if (!data.field) {
    // Step 2: Select field to edit
    const fieldMap = {
      '1': 'name',
      '2': 'price',
      '3': 'stock',
      '4': 'category'
    };

    data.field = fieldMap[messageBody.trim()];
    if (!data.field) {
      return getMessage('errors.invalid_selection', language);
    }

    return getMessage(`products.edit_${data.field}_prompt`, language, {
      current: data.product[data.field]
    });
  }

  // Step 3: Update the field
  try {
    const updateData = {};

    switch (data.field) {
      case 'name':
        updateData.name = messageBody.trim();
        break;
      case 'price':
        const price = parseFloat(messageBody.replace(/[^\d.]/g, ''));
        if (isNaN(price) || price <= 0) {
          return getMessage('errors.invalid_price', language);
        }
        updateData.price = price;
        break;
      case 'stock':
        const stock = parseInt(messageBody.replace(/[^\d]/g, ''));
        if (isNaN(stock) || stock < 0) {
          return getMessage('errors.invalid_quantity', language);
        }
        updateData.stock = stock;
        break;
      case 'category':
        const categoryMap = {
          '1': 'food', '2': 'drinks', '3': 'electronics', '4': 'clothing',
          '5': 'beauty', '6': 'health', '7': 'household', '8': 'stationery',
          '9': 'services', '10': 'other'
        };
        updateData.category = categoryMap[messageBody.trim()] || 'other';
        break;
    }

    await Product.findByIdAndUpdate(data.product_id, updateData);

    // Reset session
    session.step = 'welcome';
    session.data = {};

    return getMessage('products.edit_success', language, {
      name: data.product.name,
      field: data.field,
      value: updateData[data.field]
    });

  } catch (error) {
    logger.error('Product update error:', error);
    return getMessage('errors.network_error', language);
  }
}

/**
 * Handle delete product flow
 */
async function handleDeleteProductFlow(messageBody, session, business, language) {
  const { data } = session;

  if (!data.product_id) {
    // Step 1: Select product to delete
    const productIndex = parseInt(messageBody.trim()) - 1;

    try {
      const products = await Product.find({
        business_id: business._id,
        is_active: true
      }).limit(20);

      if (productIndex >= 0 && productIndex < products.length) {
        data.product_id = products[productIndex]._id;
        data.product = products[productIndex];
        return getMessage('products.delete_confirm', language, {
          name: products[productIndex].name
        });
      } else {
        return getMessage('errors.invalid_selection', language);
      }
    } catch (error) {
      logger.error('Product selection error:', error);
      return getMessage('errors.network_error', language);
    }
  }

  // Step 2: Confirm deletion
  const confirmation = messageBody.toLowerCase().trim();
  if (confirmation === 'yes' || confirmation === 'ndio' || confirmation === 'y') {
    try {
      // Soft delete - mark as inactive
      await Product.findByIdAndUpdate(data.product_id, { is_active: false });

      // Reset session
      session.step = 'welcome';
      session.data = {};

      return getMessage('products.delete_success', language, {
        name: data.product.name
      });

    } catch (error) {
      logger.error('Product deletion error:', error);
      return getMessage('errors.network_error', language);
    }
  } else if (confirmation === 'no' || confirmation === 'hapana' || confirmation === 'n') {
    // Cancel deletion
    session.step = 'welcome';
    session.data = {};
    return getMessage('products.delete_cancelled', language);
  } else {
    return getMessage('products.delete_confirm_again', language);
  }
}

module.exports = {
  handleWhatsAppMessage,
  processMessage
};
