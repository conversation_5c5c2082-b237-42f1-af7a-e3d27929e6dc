/**
 * Authentication Routes
 * Handles user authentication, registration, and token management
 */

const express = require('express');
const bcrypt = require('bcryptjs');
const { Business, User } = require('../models');
const { generateToken, generateRefreshToken, verifyRefreshToken } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');
const { authLimiter, registrationLimiter } = require('../middleware/rateLimiter');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const logger = require('../config/logger');

const router = express.Router();

/**
 * Register new business
 */
router.post('/register',
  registrationLimiter,
  validate(schemas.businessRegistration),
  catchAsync(async (req, res) => {
    const { name, phone, owner_name, business_type, location } = req.body;

    // Check if business already exists
    const existingBusiness = await Business.findByPhone(phone);
    if (existingBusiness) {
      throw new AppError('Business with this phone number already exists', 400,
        '<PERSON>iashara na nambari hii ya simu tayari ipo');
    }

    // Create business
    const business = new Business({
      name,
      phone,
      owner_name,
      business_type,
      location,
      settings: {
        language: req.body.language || 'sw'
      }
    });

    await business.save();

    // Create owner user
    const owner = new User({
      business_id: business._id,
      phone,
      name: owner_name,
      role: 'owner',
      preferences: {
        language: req.body.language || 'sw'
      },
      created_by: business._id
    });

    await owner.save();

    // Generate tokens
    const accessToken = generateToken(owner._id, business._id);
    const refreshToken = generateRefreshToken(owner._id, business._id);

    logger.info('Business registered successfully', {
      businessId: business._id,
      ownerId: owner._id,
      businessName: name,
      phone
    });

    res.status(201).json({
      success: true,
      message: 'Business registered successfully',
      sw: 'Biashara imesajiliwa kikamilifu',
      en: 'Business registered successfully',
      data: {
        business: {
          id: business._id,
          name: business.name,
          phone: business.phone,
          owner_name: business.owner_name,
          business_type: business.business_type,
          subscription: business.subscription,
          trial_days_remaining: business.days_remaining
        },
        user: {
          id: owner._id,
          name: owner.name,
          role: owner.role,
          permissions: owner.all_permissions
        },
        tokens: {
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_in: '24h'
        }
      }
    });
  })
);

/**
 * Login user
 */
router.post('/login',
  authLimiter,
  validate(require('joi').object({
    phone: schemas.businessRegistration.extract('phone'),
    password: require('joi').string().min(6).optional()
  })),
  catchAsync(async (req, res) => {
    const { phone, password } = req.body;

    // Find user by phone
    const user = await User.findByPhone(phone)
      .populate('business_id', 'name phone subscription settings is_active')
      .select('+authentication.password_hash +authentication.login_attempts +authentication.locked_until');

    if (!user) {
      throw new AppError('Invalid phone number or password', 401,
        'Nambari ya simu au nenosiri si sahihi');
    }

    // Check if account is locked
    if (user.authentication.locked_until && user.authentication.locked_until > Date.now()) {
      throw new AppError('Account is temporarily locked due to too many failed attempts', 423,
        'Akaunti imefungwa kwa muda kwa sababu ya majaribio mengi ya kushindwa');
    }

    // Check if business is active
    if (!user.business_id || !user.business_id.is_active) {
      throw new AppError('Business account is inactive', 401,
        'Akaunti ya biashara haifanyi kazi');
    }

    // For WhatsApp-only businesses, allow login without password
    if (!password && !user.authentication.password_hash) {
      // Generate verification code (in real implementation, send via SMS/WhatsApp)
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

      // In development, return the code; in production, send it
      if (process.env.NODE_ENV === 'development') {
        return res.status(200).json({
          success: true,
          message: 'Verification code sent',
          sw: 'Msimbo wa uthibitisho umetumwa',
          en: 'Verification code sent',
          verification_code: verificationCode // Only in development
        });
      }

      // TODO: Send verification code via WhatsApp/SMS
      return res.status(200).json({
        success: true,
        message: 'Verification code sent to your WhatsApp',
        sw: 'Msimbo wa uthibitisho umetumwa kwenye WhatsApp yako',
        en: 'Verification code sent to your WhatsApp'
      });
    }

    // Verify password if provided
    if (password && user.authentication.password_hash) {
      const isValidPassword = await user.comparePassword(password);

      if (!isValidPassword) {
        await user.incrementLoginAttempts();
        throw new AppError('Invalid phone number or password', 401,
          'Nambari ya simu au nenosiri si sahihi');
      }
    }

    // Reset login attempts on successful login
    await user.resetLoginAttempts();

    // Update last login
    user.authentication.last_login = new Date();
    user.last_active = new Date();
    await user.save();

    // Generate tokens
    const accessToken = generateToken(user._id, user.business_id._id);
    const refreshToken = generateRefreshToken(user._id, user.business_id._id);

    logger.info('User logged in successfully', {
      userId: user._id,
      businessId: user.business_id._id,
      phone: user.phone,
      role: user.role
    });

    res.status(200).json({
      success: true,
      message: 'Login successful',
      sw: 'Umeingia kikamilifu',
      en: 'Login successful',
      data: {
        user: {
          id: user._id,
          name: user.name,
          phone: user.phone,
          role: user.role,
          permissions: user.all_permissions,
          preferences: user.preferences
        },
        business: {
          id: user.business_id._id,
          name: user.business_id.name,
          phone: user.business_id.phone,
          subscription: user.business_id.subscription,
          settings: user.business_id.settings
        },
        tokens: {
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_in: '24h'
        }
      }
    });
  })
);

/**
 * Refresh access token
 */
router.post('/refresh',
  catchAsync(async (req, res) => {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      throw new AppError('Refresh token is required', 400,
        'Tokeni ya kuburudisha inahitajika');
    }

    // Verify refresh token
    const decoded = verifyRefreshToken(refresh_token);

    // Find user
    const user = await User.findById(decoded.userId)
      .populate('business_id', 'name phone subscription settings is_active');

    if (!user || !user.is_active || !user.business_id.is_active) {
      throw new AppError('Invalid refresh token', 401,
        'Tokeni ya kuburudisha si sahihi');
    }

    // Generate new tokens
    const accessToken = generateToken(user._id, user.business_id._id);
    const newRefreshToken = generateRefreshToken(user._id, user.business_id._id);

    res.status(200).json({
      success: true,
      message: 'Token refreshed successfully',
      sw: 'Tokeni imeburudishwa kikamilifu',
      en: 'Token refreshed successfully',
      data: {
        access_token: accessToken,
        refresh_token: newRefreshToken,
        expires_in: '24h'
      }
    });
  })
);

/**
 * Logout user
 */
router.post('/logout',
  catchAsync(async (req, res) => {
    // In a more sophisticated implementation, you would blacklist the token
    // For now, we just return success

    res.status(200).json({
      success: true,
      message: 'Logged out successfully',
      sw: 'Umetoka kikamilifu',
      en: 'Logged out successfully'
    });
  })
);

/**
 * Verify phone number (for WhatsApp-only authentication)
 */
router.post('/verify-phone',
  authLimiter,
  validate(require('joi').object({
    phone: schemas.businessRegistration.extract('phone'),
    verification_code: require('joi').string().length(6).required()
  })),
  catchAsync(async (req, res) => {
    const { phone, verification_code } = req.body;

    // Find business by phone
    const business = await Business.findByPhone(phone);
    if (!business) {
      throw new AppError('Business not found', 404,
        'Biashara haijapatikana');
    }

    // In a real implementation, verify the code against stored value
    // For development, accept any 6-digit code
    if (process.env.NODE_ENV !== 'development' && verification_code !== '123456') {
      throw new AppError('Invalid verification code', 400,
        'Msimbo wa uthibitisho si sahihi');
    }

    // Find user
    const user = await User.findOne({ business_id: business._id, role: 'owner' })
      .populate('business_id', 'name phone subscription settings is_active');

    if (!user) {
      throw new AppError('User not found', 404,
        'Mtumiaji hajapatikana');
    }

    // Generate tokens
    const accessToken = generateToken(user._id, business._id);
    const refreshToken = generateRefreshToken(user._id, business._id);

    // Update last login
    user.authentication.last_login = new Date();
    user.last_active = new Date();
    await user.save();

    logger.info('Phone verification successful', {
      userId: user._id,
      businessId: business._id,
      phone
    });

    res.status(200).json({
      success: true,
      message: 'Phone verified successfully',
      sw: 'Simu imethibitishwa kikamilifu',
      en: 'Phone verified successfully',
      data: {
        user: {
          id: user._id,
          name: user.name,
          phone: user.phone,
          role: user.role,
          permissions: user.all_permissions,
          preferences: user.preferences
        },
        business: {
          id: business._id,
          name: business.name,
          phone: business.phone,
          subscription: business.subscription,
          settings: business.settings
        },
        tokens: {
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_in: '24h'
        }
      }
    });
  })
);

/**
 * Check authentication status
 */
router.get('/me',
  require('../middleware/auth').authenticateToken,
  catchAsync(async (req, res) => {
    res.status(200).json({
      success: true,
      data: {
        user: {
          id: req.user._id,
          name: req.user.name,
          phone: req.user.phone,
          role: req.user.role,
          permissions: req.user.all_permissions,
          preferences: req.user.preferences,
          last_active: req.user.last_active
        },
        business: {
          id: req.business._id,
          name: req.business.name,
          phone: req.business.phone,
          subscription: req.business.subscription,
          settings: req.business.settings,
          stats: req.business.stats
        }
      }
    });
  })
);

module.exports = router;
