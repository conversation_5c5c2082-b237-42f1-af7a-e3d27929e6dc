/**
 * Backup Management Routes
 * Handles backup creation, listing, and management
 */

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { requirePermission } = require('../middleware/permissions');
const { catchAsync } = require('../middleware/errorHandler');
const logger = require('../config/logger');

/**
 * Create manual backup
 * POST /api/backup/create
 */
router.post('/create', 
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    if (!global.backupService) {
      return res.status(503).json({
        success: false,
        message: 'Backup service not available'
      });
    }

    logger.info('Manual backup requested', {
      userId: req.user.id,
      businessId: req.user.business_id
    });

    const result = await global.backupService.createBackup();

    res.json({
      success: true,
      message: 'Backup created successfully',
      data: result
    });
  })
);

/**
 * List available backups
 * GET /api/backup/list
 */
router.get('/list',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    if (!global.backupService) {
      return res.status(503).json({
        success: false,
        message: 'Backup service not available'
      });
    }

    const backups = await global.backupService.listBackups();

    res.json({
      success: true,
      data: backups
    });
  })
);

/**
 * Get backup service status
 * GET /api/backup/status
 */
router.get('/status',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const status = global.backupService 
      ? global.backupService.getStatus()
      : { enabled: false, message: 'Backup service not initialized' };

    res.json({
      success: true,
      data: status
    });
  })
);

/**
 * Download backup file
 * GET /api/backup/download/:filename
 */
router.get('/download/:filename',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const { filename } = req.params;
    
    // Validate filename to prevent directory traversal
    if (!/^whatsapp-pos-backup-[\d-T]+\.tar\.gz$/.test(filename)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid backup filename'
      });
    }

    const backupPath = require('path').join(
      process.env.BACKUP_DIR || require('path').join(__dirname, '../../backups'),
      filename
    );

    try {
      await require('fs').promises.access(backupPath);
      
      logger.info('Backup download requested', {
        filename,
        userId: req.user.id,
        businessId: req.user.business_id
      });

      res.download(backupPath, filename, (err) => {
        if (err) {
          logger.error('Backup download failed:', err);
          if (!res.headersSent) {
            res.status(500).json({
              success: false,
              message: 'Failed to download backup'
            });
          }
        }
      });

    } catch (error) {
      res.status(404).json({
        success: false,
        message: 'Backup file not found'
      });
    }
  })
);

/**
 * Delete backup file
 * DELETE /api/backup/:filename
 */
router.delete('/:filename',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const { filename } = req.params;
    
    // Validate filename
    if (!/^whatsapp-pos-backup-[\d-T]+\.tar\.gz$/.test(filename)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid backup filename'
      });
    }

    const backupPath = require('path').join(
      process.env.BACKUP_DIR || require('path').join(__dirname, '../../backups'),
      filename
    );

    try {
      await require('fs').promises.unlink(backupPath);
      
      logger.info('Backup deleted', {
        filename,
        userId: req.user.id,
        businessId: req.user.business_id
      });

      res.json({
        success: true,
        message: 'Backup deleted successfully'
      });

    } catch (error) {
      if (error.code === 'ENOENT') {
        res.status(404).json({
          success: false,
          message: 'Backup file not found'
        });
      } else {
        throw error;
      }
    }
  })
);

/**
 * Get backup configuration
 * GET /api/backup/config
 */
router.get('/config',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const config = {
      enabled: process.env.BACKUP_ENABLED === 'true',
      schedule: process.env.BACKUP_SCHEDULE || '0 2 * * *',
      retentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS) || 30,
      s3Enabled: !!(process.env.AWS_ACCESS_KEY_ID && process.env.BACKUP_S3_BUCKET),
      s3Bucket: process.env.BACKUP_S3_BUCKET,
      backupDir: process.env.BACKUP_DIR || 'backups'
    };

    res.json({
      success: true,
      data: config
    });
  })
);

/**
 * Test backup configuration
 * POST /api/backup/test
 */
router.post('/test',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    if (!global.backupService) {
      return res.status(503).json({
        success: false,
        message: 'Backup service not available'
      });
    }

    try {
      // Test backup directory access
      const backupDir = process.env.BACKUP_DIR || require('path').join(__dirname, '../../backups');
      await require('fs').promises.access(backupDir);

      // Test MongoDB connection
      const mongoose = require('mongoose');
      if (mongoose.connection.readyState !== 1) {
        throw new Error('MongoDB not connected');
      }

      // Test S3 connection if configured
      let s3Status = 'not configured';
      if (process.env.AWS_ACCESS_KEY_ID && process.env.BACKUP_S3_BUCKET) {
        try {
          const AWS = require('aws-sdk');
          const s3 = new AWS.S3({
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            region: process.env.AWS_REGION || 'us-east-1'
          });
          
          await s3.headBucket({ Bucket: process.env.BACKUP_S3_BUCKET }).promise();
          s3Status = 'connected';
        } catch (error) {
          s3Status = `error: ${error.message}`;
        }
      }

      res.json({
        success: true,
        message: 'Backup configuration test completed',
        data: {
          backupDir: 'accessible',
          mongodb: 'connected',
          s3: s3Status
        }
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Backup configuration test failed',
        error: error.message
      });
    }
  })
);

module.exports = router;
