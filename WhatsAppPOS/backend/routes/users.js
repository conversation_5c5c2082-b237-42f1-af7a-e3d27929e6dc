/**
 * Users Routes
 * Handles user management within businesses
 */

const express = require('express');
const { User } = require('../models');
const { authenticateToken, requirePermission, requireRole } = require('../middleware/auth');
const { ensureBusinessIsolation, enforceBusinessCreation } = require('../middleware/businessIsolation');
const { validate, schemas } = require('../middleware/validation');
const { subscriptionBasedLimiter } = require('../middleware/rateLimiter');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const logger = require('../config/logger');

const router = express.Router();

// Apply authentication and business isolation to all routes
router.use(authenticateToken);
router.use(ensureBusinessIsolation);

/**
 * Get all users for the business
 */
router.get('/',
  requirePermission('can_view_users'),
  validate(schemas.query.pagination, 'query'),
  catchAsync(async (req, res) => {
    const { page, limit, sort, order, role, active_only } = req.query;
    
    // Build query
    const query = { business_id: req.business._id };
    
    if (role) {
      query.role = role;
    }
    
    if (active_only !== 'false') {
      query.is_active = true;
    }
    
    // Build sort
    const sortField = sort || 'created_at';
    const sortOrder = order === 'asc' ? 1 : -1;
    const sortObj = { [sortField]: sortOrder };
    
    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [users, total] = await Promise.all([
      User.find(query)
        .sort(sortObj)
        .skip(skip)
        .limit(limit)
        .populate('created_by', 'name')
        .select('-authentication'),
      User.countDocuments(query)
    ]);
    
    res.status(200).json({
      success: true,
      data: {
        users,
        pagination: {
          current_page: page,
          total_pages: Math.ceil(total / limit),
          total_items: total,
          items_per_page: limit,
          has_next: page < Math.ceil(total / limit),
          has_prev: page > 1
        }
      }
    });
  })
);

/**
 * Get single user by ID
 */
router.get('/:id',
  requirePermission('can_view_users'),
  catchAsync(async (req, res) => {
    const user = await User.findOne({
      _id: req.params.id,
      business_id: req.business._id
    })
    .populate('created_by', 'name')
    .select('-authentication');
    
    if (!user) {
      throw new AppError('User not found', 404, 'Mtumiaji hajapatikana');
    }
    
    res.status(200).json({
      success: true,
      data: { user }
    });
  })
);

/**
 * Create new user
 */
router.post('/',
  subscriptionBasedLimiter(20), // 20 requests per 15 minutes based on plan
  requirePermission('can_manage_users'),
  enforceBusinessCreation,
  validate(schemas.userCreation),
  catchAsync(async (req, res) => {
    const userData = {
      ...req.body,
      business_id: req.business._id,
      created_by: req.user._id
    };
    
    // Check for duplicate phone
    const existingUser = await User.findOne({
      phone: userData.phone,
      is_active: true
    });
    
    if (existingUser) {
      throw new AppError('User with this phone number already exists', 400,
        'Mtumiaji na nambari hii ya simu tayari yupo');
    }
    
    // Only owners can create other owners
    if (userData.role === 'owner' && req.user.role !== 'owner') {
      throw new AppError('Only owners can create other owners', 403,
        'Ni wamiliki tu wanaweza kuunda wamiliki wengine');
    }
    
    const user = new User(userData);
    await user.save();
    
    logger.business('User created', req.business._id, {
      userId: user._id,
      userName: user.name,
      userRole: user.role,
      createdBy: req.user._id
    });
    
    res.status(201).json({
      success: true,
      message: 'User created successfully',
      sw: 'Mtumiaji ameundwa kikamilifu',
      en: 'User created successfully',
      data: { 
        user: {
          id: user._id,
          name: user.name,
          phone: user.phone,
          role: user.role,
          permissions: user.all_permissions,
          is_active: user.is_active
        }
      }
    });
  })
);

/**
 * Update user
 */
router.put('/:id',
  requirePermission('can_manage_users'),
  validate(require('joi').object({
    name: require('joi').string().trim().min(2).max(100).optional(),
    role: require('joi').string().valid('owner', 'manager', 'employee', 'viewer').optional(),
    permissions: require('joi').object().optional(),
    preferences: require('joi').object({
      language: require('joi').string().valid('sw', 'en').optional(),
      notifications: require('joi').object().optional(),
      dashboard_layout: require('joi').string().valid('compact', 'detailed').optional()
    }).optional(),
    is_active: require('joi').boolean().optional()
  })),
  catchAsync(async (req, res) => {
    const user = await User.findOne({
      _id: req.params.id,
      business_id: req.business._id
    });
    
    if (!user) {
      throw new AppError('User not found', 404, 'Mtumiaji hajapatikana');
    }
    
    // Prevent users from modifying themselves in certain ways
    if (user._id.toString() === req.user._id.toString()) {
      if (req.body.role && req.body.role !== user.role) {
        throw new AppError('Cannot change your own role', 400,
          'Huwezi kubadilisha jukumu lako mwenyewe');
      }
      
      if (req.body.is_active === false) {
        throw new AppError('Cannot deactivate yourself', 400,
          'Huwezi kujifunga mwenyewe');
      }
    }
    
    // Only owners can modify other owners
    if (user.role === 'owner' && req.user.role !== 'owner') {
      throw new AppError('Only owners can modify other owners', 403,
        'Ni wamiliki tu wanaweza kubadilisha wamiliki wengine');
    }
    
    // Only owners can create other owners
    if (req.body.role === 'owner' && req.user.role !== 'owner') {
      throw new AppError('Only owners can promote users to owner', 403,
        'Ni wamiliki tu wanaweza kupandisha watumiaji kuwa wamiliki');
    }
    
    // Update user
    const allowedUpdates = ['name', 'role', 'permissions', 'preferences', 'is_active'];
    for (const field of allowedUpdates) {
      if (req.body[field] !== undefined) {
        if (field === 'permissions' || field === 'preferences') {
          Object.assign(user[field], req.body[field]);
        } else {
          user[field] = req.body[field];
        }
      }
    }
    
    await user.save();
    
    logger.business('User updated', req.business._id, {
      userId: user._id,
      userName: user.name,
      updatedBy: req.user._id,
      changes: Object.keys(req.body)
    });
    
    res.status(200).json({
      success: true,
      message: 'User updated successfully',
      sw: 'Mtumiaji amesasishwa kikamilifu',
      en: 'User updated successfully',
      data: { 
        user: {
          id: user._id,
          name: user.name,
          phone: user.phone,
          role: user.role,
          permissions: user.all_permissions,
          preferences: user.preferences,
          is_active: user.is_active
        }
      }
    });
  })
);

/**
 * Delete user (soft delete)
 */
router.delete('/:id',
  requirePermission('can_manage_users'),
  catchAsync(async (req, res) => {
    const user = await User.findOne({
      _id: req.params.id,
      business_id: req.business._id
    });
    
    if (!user) {
      throw new AppError('User not found', 404, 'Mtumiaji hajapatikana');
    }
    
    // Prevent users from deleting themselves
    if (user._id.toString() === req.user._id.toString()) {
      throw new AppError('Cannot delete yourself', 400,
        'Huwezi kujifuta mwenyewe');
    }
    
    // Only owners can delete other owners
    if (user.role === 'owner' && req.user.role !== 'owner') {
      throw new AppError('Only owners can delete other owners', 403,
        'Ni wamiliki tu wanaweza kufuta wamiliki wengine');
    }
    
    // Soft delete
    user.is_active = false;
    await user.save();
    
    logger.business('User deleted', req.business._id, {
      userId: user._id,
      userName: user.name,
      deletedBy: req.user._id
    });
    
    res.status(200).json({
      success: true,
      message: 'User deleted successfully',
      sw: 'Mtumiaji amefutwa kikamilifu',
      en: 'User deleted successfully'
    });
  })
);

/**
 * Get user activity log
 */
router.get('/:id/activity',
  requirePermission('can_view_users'),
  validate(schemas.query.pagination, 'query'),
  catchAsync(async (req, res) => {
    const user = await User.findOne({
      _id: req.params.id,
      business_id: req.business._id
    });
    
    if (!user) {
      throw new AppError('User not found', 404, 'Mtumiaji hajapatikana');
    }
    
    // Get user's recent activities
    const { Sale, Product } = require('../models');
    const { page, limit } = req.query;
    const skip = (page - 1) * limit;
    
    const [sales, products] = await Promise.all([
      Sale.find({
        business_id: req.business._id,
        user_id: user._id
      })
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit)
      .select('sale_number total sale_date'),
      
      Product.find({
        business_id: req.business._id,
        $or: [
          { created_by: user._id },
          { last_updated_by: user._id }
        ]
      })
      .sort({ updated_at: -1 })
      .limit(10)
      .select('name created_at updated_at created_by last_updated_by')
    ]);
    
    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: user.name,
          role: user.role
        },
        activity: {
          recent_sales: sales,
          recent_products: products,
          stats: {
            total_sales: user.activity.total_sales_recorded,
            total_products: user.activity.total_products_added,
            last_whatsapp_activity: user.activity.last_whatsapp_activity,
            last_dashboard_activity: user.activity.last_dashboard_activity
          }
        }
      }
    });
  })
);

/**
 * Reset user password (for dashboard access)
 */
router.patch('/:id/reset-password',
  requirePermission('can_manage_users'),
  validate(require('joi').object({
    new_password: require('joi').string().min(6).max(128).required()
  })),
  catchAsync(async (req, res) => {
    const { new_password } = req.body;
    
    const user = await User.findOne({
      _id: req.params.id,
      business_id: req.business._id
    });
    
    if (!user) {
      throw new AppError('User not found', 404, 'Mtumiaji hajapatikana');
    }
    
    // Set new password
    user.authentication.password_hash = new_password; // Will be hashed in pre-save middleware
    user.authentication.login_attempts = 0;
    user.authentication.locked_until = undefined;
    
    await user.save();
    
    logger.business('User password reset', req.business._id, {
      userId: user._id,
      userName: user.name,
      resetBy: req.user._id
    });
    
    res.status(200).json({
      success: true,
      message: 'Password reset successfully',
      sw: 'Nenosiri limebadilishwa kikamilifu',
      en: 'Password reset successfully'
    });
  })
);

/**
 * Get user permissions
 */
router.get('/:id/permissions',
  requirePermission('can_view_users'),
  catchAsync(async (req, res) => {
    const user = await User.findOne({
      _id: req.params.id,
      business_id: req.business._id
    });
    
    if (!user) {
      throw new AppError('User not found', 404, 'Mtumiaji hajapatikana');
    }
    
    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: user.name,
          role: user.role
        },
        permissions: user.all_permissions
      }
    });
  })
);

/**
 * Update user permissions
 */
router.patch('/:id/permissions',
  requireRole('owner'), // Only owners can modify permissions
  validate(require('joi').object({
    permissions: require('joi').object({
      can_add_products: require('joi').boolean().optional(),
      can_edit_products: require('joi').boolean().optional(),
      can_delete_products: require('joi').boolean().optional(),
      can_record_sales: require('joi').boolean().optional(),
      can_edit_sales: require('joi').boolean().optional(),
      can_delete_sales: require('joi').boolean().optional(),
      can_add_customers: require('joi').boolean().optional(),
      can_edit_customers: require('joi').boolean().optional(),
      can_view_reports: require('joi').boolean().optional(),
      can_export_data: require('joi').boolean().optional(),
      can_manage_users: require('joi').boolean().optional(),
      can_view_users: require('joi').boolean().optional(),
      can_manage_settings: require('joi').boolean().optional(),
      can_view_settings: require('joi').boolean().optional()
    }).required()
  })),
  catchAsync(async (req, res) => {
    const { permissions } = req.body;
    
    const user = await User.findOne({
      _id: req.params.id,
      business_id: req.business._id
    });
    
    if (!user) {
      throw new AppError('User not found', 404, 'Mtumiaji hajapatikana');
    }
    
    // Update permissions
    Object.assign(user.permissions, permissions);
    await user.save();
    
    logger.business('User permissions updated', req.business._id, {
      userId: user._id,
      userName: user.name,
      updatedBy: req.user._id,
      newPermissions: permissions
    });
    
    res.status(200).json({
      success: true,
      message: 'User permissions updated successfully',
      sw: 'Ruhusa za mtumiaji zimesasishwa kikamilifu',
      en: 'User permissions updated successfully',
      data: {
        user: {
          id: user._id,
          name: user.name,
          role: user.role
        },
        permissions: user.all_permissions
      }
    });
  })
);

module.exports = router;
