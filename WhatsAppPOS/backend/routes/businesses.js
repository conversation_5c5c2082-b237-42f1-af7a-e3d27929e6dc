/**
 * Businesses Routes
 * Handles business management and settings
 */

const express = require('express');
const { Business } = require('../models');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { ensureBusinessIsolation } = require('../middleware/businessIsolation');
const { validate } = require('../middleware/validation');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const logger = require('../config/logger');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * Get current business information
 */
router.get('/me',
  catchAsync(async (req, res) => {
    const business = req.business;
    
    // Update stats before returning
    await business.updateStats();
    
    res.status(200).json({
      success: true,
      data: { business }
    });
  })
);

/**
 * Update business information
 */
router.put('/me',
  requireRole('owner'),
  validate(require('joi').object({
    name: require('joi').string().trim().min(2).max(100).optional(),
    business_type: require('joi').string().valid(
      'duka', 'shop', 'salon', 'restaurant', 'pharmacy', 'electronics',
      'clothing', 'hardware', 'grocery', 'services', 'repair', 'other'
    ).optional(),
    location: require('joi').object({
      region: require('joi').string().trim().max(50).optional(),
      district: require('joi').string().trim().max(50).optional(),
      ward: require('joi').string().trim().max(50).optional(),
      street: require('joi').string().trim().max(100).optional()
    }).optional()
  })),
  catchAsync(async (req, res) => {
    const business = req.business;
    const allowedUpdates = ['name', 'business_type', 'location'];
    
    // Apply updates
    for (const field of allowedUpdates) {
      if (req.body[field] !== undefined) {
        if (field === 'location') {
          Object.assign(business.location, req.body[field]);
        } else {
          business[field] = req.body[field];
        }
      }
    }
    
    await business.save();
    
    logger.business('Business information updated', business._id, {
      userId: req.user._id,
      updates: Object.keys(req.body)
    });
    
    res.status(200).json({
      success: true,
      message: 'Business information updated successfully',
      sw: 'Maelezo ya biashara yamebadilishwa kikamilifu',
      en: 'Business information updated successfully',
      data: { business }
    });
  })
);

/**
 * Update business settings
 */
router.patch('/me/settings',
  requireRole('owner'),
  validate(require('joi').object({
    language: require('joi').string().valid('sw', 'en').optional(),
    currency_format: require('joi').string().max(50).optional(),
    low_stock_threshold: require('joi').number().min(0).max(1000).optional(),
    notifications: require('joi').object({
      low_stock: require('joi').boolean().optional(),
      daily_summary: require('joi').boolean().optional(),
      payment_reminders: require('joi').boolean().optional()
    }).optional(),
    business_hours: require('joi').object({
      open_time: require('joi').string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      close_time: require('joi').string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      working_days: require('joi').array().items(
        require('joi').string().valid('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
      ).optional()
    }).optional()
  })),
  catchAsync(async (req, res) => {
    const business = req.business;
    
    // Update settings
    Object.assign(business.settings, req.body);
    await business.save();
    
    logger.business('Business settings updated', business._id, {
      userId: req.user._id,
      settings: req.body
    });
    
    res.status(200).json({
      success: true,
      message: 'Business settings updated successfully',
      sw: 'Mipangilio ya biashara imebadilishwa kikamilifu',
      en: 'Business settings updated successfully',
      data: { 
        settings: business.settings 
      }
    });
  })
);

/**
 * Get business statistics
 */
router.get('/me/stats',
  catchAsync(async (req, res) => {
    const business = req.business;
    
    // Update stats
    await business.updateStats();
    
    // Get additional analytics
    const { Product, Sale, Customer, User } = require('../models');
    
    const [
      lowStockCount,
      todaySales,
      activeUsers,
      recentActivity
    ] = await Promise.all([
      // Low stock products count
      Product.countDocuments({
        business_id: business._id,
        is_active: true,
        is_service: false,
        $expr: { $lte: ['$stock', '$low_stock_alert'] }
      }),
      
      // Today's sales
      Sale.aggregate([
        {
          $match: {
            business_id: business._id,
            sale_date: {
              $gte: new Date(new Date().setHours(0, 0, 0, 0)),
              $lte: new Date(new Date().setHours(23, 59, 59, 999))
            },
            status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            count: { $sum: 1 },
            revenue: { $sum: '$total' }
          }
        }
      ]),
      
      // Active users count
      User.countDocuments({
        business_id: business._id,
        is_active: true
      }),
      
      // Recent activity (last 10 sales)
      Sale.find({
        business_id: business._id,
        status: 'completed'
      })
      .sort({ sale_date: -1 })
      .limit(10)
      .select('sale_number total sale_date')
    ]);
    
    const todayStats = todaySales[0] || { count: 0, revenue: 0 };
    
    res.status(200).json({
      success: true,
      data: {
        business_stats: business.stats,
        subscription: {
          plan: business.subscription.plan,
          status: business.subscription.status,
          days_remaining: business.days_remaining,
          is_active: business.subscription_active
        },
        alerts: {
          low_stock_products: lowStockCount
        },
        today: {
          sales_count: todayStats.count,
          revenue: todayStats.revenue
        },
        users: {
          active_count: activeUsers
        },
        recent_activity: recentActivity
      }
    });
  })
);

/**
 * Get subscription information
 */
router.get('/me/subscription',
  catchAsync(async (req, res) => {
    const business = req.business;
    const { getSubscriptionLimits } = require('../middleware/businessIsolation');
    
    const limits = getSubscriptionLimits(business.subscription.plan);
    
    res.status(200).json({
      success: true,
      data: {
        subscription: business.subscription,
        limits,
        days_remaining: business.days_remaining,
        is_active: business.subscription_active,
        usage: {
          products: business.stats.total_products,
          // Add other usage metrics here
        }
      }
    });
  })
);

/**
 * Update subscription plan
 */
router.patch('/me/subscription',
  requireRole('owner'),
  validate(require('joi').object({
    plan: require('joi').string().valid('trial', 'basic', 'pro').required(),
    payment_method: require('joi').string().valid('mpesa', 'tigo', 'airtel', 'bank', 'cash').optional(),
    payment_reference: require('joi').string().max(50).optional()
  })),
  catchAsync(async (req, res) => {
    const { plan, payment_method, payment_reference } = req.body;
    const business = req.business;
    
    // In a real implementation, you would:
    // 1. Validate payment
    // 2. Process payment through payment gateway
    // 3. Update subscription
    
    // For now, we'll just update the plan
    const planDurations = {
      trial: 14,
      basic: 30,
      pro: 30
    };
    
    const duration = planDurations[plan];
    const now = new Date();
    
    business.subscription.plan = plan;
    business.subscription.status = 'active';
    business.subscription.start_date = now;
    business.subscription.end_date = new Date(now.getTime() + (duration * 24 * 60 * 60 * 1000));
    business.subscription.next_billing = new Date(now.getTime() + (duration * 24 * 60 * 60 * 1000));
    
    if (payment_method) {
      business.subscription.payment_method = payment_method;
    }
    
    await business.save();
    
    logger.business('Subscription updated', business._id, {
      userId: req.user._id,
      plan,
      payment_method,
      payment_reference
    });
    
    res.status(200).json({
      success: true,
      message: 'Subscription updated successfully',
      sw: 'Usajili umebadilishwa kikamilifu',
      en: 'Subscription updated successfully',
      data: {
        subscription: business.subscription,
        days_remaining: business.days_remaining
      }
    });
  })
);

/**
 * Deactivate business (soft delete)
 */
router.delete('/me',
  requireRole('owner'),
  validate(require('joi').object({
    confirmation: require('joi').string().valid('DELETE_BUSINESS').required(),
    reason: require('joi').string().max(200).optional()
  })),
  catchAsync(async (req, res) => {
    const { reason } = req.body;
    const business = req.business;
    
    // Deactivate business
    business.is_active = false;
    await business.save();
    
    // Deactivate all users
    const { User } = require('../models');
    await User.updateMany(
      { business_id: business._id },
      { is_active: false }
    );
    
    logger.business('Business deactivated', business._id, {
      userId: req.user._id,
      reason
    });
    
    res.status(200).json({
      success: true,
      message: 'Business deactivated successfully',
      sw: 'Biashara imefungwa kikamilifu',
      en: 'Business deactivated successfully'
    });
  })
);

/**
 * Export business data
 */
router.get('/me/export',
  requireRole('owner'),
  catchAsync(async (req, res) => {
    // This would generate a comprehensive export of all business data
    // For now, return a placeholder
    
    res.status(501).json({
      success: false,
      message: 'Data export not yet implemented',
      sw: 'Uhamishaji wa data bado haujaanzishwa',
      en: 'Data export not yet implemented'
    });
  })
);

module.exports = router;
