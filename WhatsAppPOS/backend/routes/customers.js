/**
 * Customers Routes
 * Handles customer management operations
 */

const express = require('express');
const { Customer, Sale } = require('../models');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { ensureBusinessIsolation, enforceBusinessCreation } = require('../middleware/businessIsolation');
const { validate, schemas } = require('../middleware/validation');
const { subscriptionBasedLimiter } = require('../middleware/rateLimiter');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const logger = require('../config/logger');

const router = express.Router();

// Apply authentication and business isolation to all routes
router.use(authenticateToken);
router.use(ensureBusinessIsolation);

/**
 * Get all customers for the business
 */
router.get('/',
  requirePermission('can_view_reports'),
  validate(schemas.query.pagination, 'query'),
  catchAsync(async (req, res) => {
    const { page, limit, sort, order, search, status } = req.query;

    // Build query
    const query = { business_id: req.business._id, is_active: true };

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort
    const sortField = sort || 'created_at';
    const sortOrder = order === 'asc' ? 1 : -1;
    const sortObj = { [sortField]: sortOrder };

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [customers, total] = await Promise.all([
      Customer.find(query)
        .sort(sortObj)
        .skip(skip)
        .limit(limit)
        .populate('created_by', 'name'),
      Customer.countDocuments(query)
    ]);

    res.status(200).json({
      success: true,
      data: {
        customers,
        pagination: {
          current_page: page,
          total_pages: Math.ceil(total / limit),
          total_items: total,
          items_per_page: limit,
          has_next: page < Math.ceil(total / limit),
          has_prev: page > 1
        }
      }
    });
  })
);

/**
 * Get single customer by ID
 */
router.get('/:id',
  requirePermission('can_view_reports'),
  catchAsync(async (req, res) => {
    const customer = await Customer.findOne({
      _id: req.params.id,
      business_id: req.business._id,
      is_active: true
    })
    .populate('created_by', 'name');

    if (!customer) {
      throw new AppError('Customer not found', 404, 'Mteja hajapatikana');
    }

    // Get customer's purchase history
    const purchases = await Sale.find({
      business_id: req.business._id,
      customer_id: customer._id,
      status: 'completed'
    })
    .sort({ sale_date: -1 })
    .limit(10)
    .select('sale_number total sale_date payment.method');

    res.status(200).json({
      success: true,
      data: {
        customer,
        recent_purchases: purchases
      }
    });
  })
);

/**
 * Create new customer
 */
router.post('/',
  subscriptionBasedLimiter(30), // 30 requests per 15 minutes based on plan
  requirePermission('can_add_customers'),
  enforceBusinessCreation,
  validate(schemas.customer),
  catchAsync(async (req, res) => {
    const customerData = {
      ...req.body,
      business_id: req.business._id,
      created_by: req.user._id
    };

    // Check for duplicate phone within business
    if (customerData.phone) {
      const existingCustomer = await Customer.findOne({
        business_id: req.business._id,
        phone: customerData.phone,
        is_active: true
      });

      if (existingCustomer) {
        throw new AppError('Customer with this phone number already exists', 400,
          'Mteja na nambari hii ya simu tayari yupo');
      }
    }

    const customer = new Customer(customerData);
    await customer.save();

    // Update business stats
    await req.business.updateStats();

    logger.business('Customer created', req.business._id, {
      customerId: customer._id,
      customerName: customer.name,
      userId: req.user._id
    });

    res.status(201).json({
      success: true,
      message: 'Customer created successfully',
      sw: 'Mteja ameundwa kikamilifu',
      en: 'Customer created successfully',
      data: { customer }
    });
  })
);

/**
 * Update customer
 */
router.put('/:id',
  requirePermission('can_edit_customers'),
  validate(schemas.customer),
  catchAsync(async (req, res) => {
    const customer = await Customer.findOne({
      _id: req.params.id,
      business_id: req.business._id,
      is_active: true
    });

    if (!customer) {
      throw new AppError('Customer not found', 404, 'Mteja hajapatikana');
    }

    // Check for duplicate phone if phone is being changed
    if (req.body.phone && req.body.phone !== customer.phone) {
      const existingCustomer = await Customer.findOne({
        business_id: req.business._id,
        phone: req.body.phone,
        is_active: true,
        _id: { $ne: customer._id }
      });

      if (existingCustomer) {
        throw new AppError('Customer with this phone number already exists', 400,
          'Mteja na nambari hii ya simu tayari yupo');
      }
    }

    // Update customer
    Object.assign(customer, req.body);
    await customer.save();

    logger.business('Customer updated', req.business._id, {
      customerId: customer._id,
      customerName: customer.name,
      userId: req.user._id
    });

    res.status(200).json({
      success: true,
      message: 'Customer updated successfully',
      sw: 'Mteja amesasishwa kikamilifu',
      en: 'Customer updated successfully',
      data: { customer }
    });
  })
);

/**
 * Delete customer (soft delete)
 */
router.delete('/:id',
  requirePermission('can_edit_customers'),
  catchAsync(async (req, res) => {
    const customer = await Customer.findOne({
      _id: req.params.id,
      business_id: req.business._id,
      is_active: true
    });

    if (!customer) {
      throw new AppError('Customer not found', 404, 'Mteja hajapatikana');
    }

    // Soft delete
    customer.is_active = false;
    await customer.save();

    // Update business stats
    await req.business.updateStats();

    logger.business('Customer deleted', req.business._id, {
      customerId: customer._id,
      customerName: customer.name,
      userId: req.user._id
    });

    res.status(200).json({
      success: true,
      message: 'Customer deleted successfully',
      sw: 'Mteja amefutwa kikamilifu',
      en: 'Customer deleted successfully'
    });
  })
);

/**
 * Search customers
 */
router.get('/search/:query',
  requirePermission('can_view_reports'),
  validate(require('joi').object({
    query: require('joi').string().min(1).max(100).required()
  }), 'params'),
  catchAsync(async (req, res) => {
    const { query } = req.params;
    const { limit = 20 } = req.query;

    const customers = await Customer.find({
      business_id: req.business._id,
      is_active: true,
      $or: [
        { name: { $regex: query, $options: 'i' } },
        { phone: { $regex: query, $options: 'i' } },
        { email: { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } }
      ]
    })
    .limit(parseInt(limit))
    .sort({ name: 1 });

    res.status(200).json({
      success: true,
      data: {
        customers,
        query,
        count: customers.length
      }
    });
  })
);

/**
 * Get customer analytics
 */
router.get('/analytics/overview',
  requirePermission('can_view_reports'),
  catchAsync(async (req, res) => {
    const analytics = await Customer.aggregate([
      {
        $match: {
          business_id: req.business._id,
          is_active: true
        }
      },
      {
        $group: {
          _id: null,
          total_customers: { $sum: 1 },
          total_spent: { $sum: '$purchase_history.total_spent' },
          average_spent: { $avg: '$purchase_history.total_spent' },
          loyalty_tiers: { $push: '$loyalty.tier' }
        }
      }
    ]);

    // Get top customers
    const topCustomers = await Customer.find({
      business_id: req.business._id,
      is_active: true
    })
    .sort({ 'purchase_history.total_spent': -1 })
    .limit(10)
    .select('name phone purchase_history.total_spent purchase_history.total_purchases');

    res.status(200).json({
      success: true,
      data: {
        overview: analytics[0] || {
          total_customers: 0,
          total_spent: 0,
          average_spent: 0,
          loyalty_tiers: []
        },
        top_customers: topCustomers
      }
    });
  })
);

/**
 * Add loyalty points to customer
 */
router.patch('/:id/loyalty/add',
  requirePermission('can_edit_customers'),
  validate(require('joi').object({
    points: require('joi').number().integer().min(1).max(10000).required(),
    reason: require('joi').string().max(100).optional()
  })),
  catchAsync(async (req, res) => {
    const { points, reason } = req.body;

    const customer = await Customer.findOne({
      _id: req.params.id,
      business_id: req.business._id,
      is_active: true
    });

    if (!customer) {
      throw new AppError('Customer not found', 404, 'Mteja hajapatikana');
    }

    customer.addLoyaltyPoints(points);
    await customer.save();

    logger.business('Loyalty points added', req.business._id, {
      customerId: customer._id,
      customerName: customer.name,
      points,
      reason,
      userId: req.user._id
    });

    res.status(200).json({
      success: true,
      message: 'Loyalty points added successfully',
      sw: 'Pointi za uongozi zimeongezwa kikamilifu',
      en: 'Loyalty points added successfully',
      data: {
        customer_id: customer._id,
        new_points_balance: customer.loyalty.points,
        tier: customer.loyalty.tier
      }
    });
  })
);

/**
 * Redeem loyalty points
 */
router.patch('/:id/loyalty/redeem',
  requirePermission('can_edit_customers'),
  validate(require('joi').object({
    points: require('joi').number().integer().min(1).required(),
    reason: require('joi').string().max(100).optional()
  })),
  catchAsync(async (req, res) => {
    const { points, reason } = req.body;

    const customer = await Customer.findOne({
      _id: req.params.id,
      business_id: req.business._id,
      is_active: true
    });

    if (!customer) {
      throw new AppError('Customer not found', 404, 'Mteja hajapatikana');
    }

    if (customer.loyalty.points < points) {
      throw new AppError('Insufficient loyalty points', 400,
        'Pointi za uongozi hazitoshi');
    }

    await customer.redeemLoyaltyPoints(points);

    logger.business('Loyalty points redeemed', req.business._id, {
      customerId: customer._id,
      customerName: customer.name,
      points,
      reason,
      userId: req.user._id
    });

    res.status(200).json({
      success: true,
      message: 'Loyalty points redeemed successfully',
      sw: 'Pointi za uongozi zimetumika kikamilifu',
      en: 'Loyalty points redeemed successfully',
      data: {
        customer_id: customer._id,
        new_points_balance: customer.loyalty.points,
        tier: customer.loyalty.tier
      }
    });
  })
);

/**
 * Get customer purchase history
 */
router.get('/:id/purchases',
  requirePermission('can_view_reports'),
  validate(schemas.query.pagination, 'query'),
  catchAsync(async (req, res) => {
    const { page, limit } = req.query;

    const customer = await Customer.findOne({
      _id: req.params.id,
      business_id: req.business._id,
      is_active: true
    });

    if (!customer) {
      throw new AppError('Customer not found', 404, 'Mteja hajapatikana');
    }

    const skip = (page - 1) * limit;
    const [purchases, total] = await Promise.all([
      Sale.find({
        business_id: req.business._id,
        customer_id: customer._id,
        status: 'completed'
      })
      .sort({ sale_date: -1 })
      .skip(skip)
      .limit(limit)
      .populate('user_id', 'name'),
      Sale.countDocuments({
        business_id: req.business._id,
        customer_id: customer._id,
        status: 'completed'
      })
    ]);

    res.status(200).json({
      success: true,
      data: {
        customer: {
          id: customer._id,
          name: customer.name,
          phone: customer.phone
        },
        purchases,
        pagination: {
          current_page: page,
          total_pages: Math.ceil(total / limit),
          total_items: total,
          items_per_page: limit,
          has_next: page < Math.ceil(total / limit),
          has_prev: page > 1
        }
      }
    });
  })
);

module.exports = router;
