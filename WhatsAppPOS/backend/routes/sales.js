/**
 * Sales Routes
 * Handles sales recording, retrieval, and management
 */

const express = require('express');
const mongoose = require('mongoose');
const { Sale, Product, Customer } = require('../models');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { ensureBusinessIsolation, enforceBusinessCreation, validateSubscriptionLimits } = require('../middleware/businessIsolation');
const { validate, schemas } = require('../middleware/validation');
const { salesLimiter, subscriptionBasedLimiter } = require('../middleware/rateLimiter');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const logger = require('../config/logger');

const router = express.Router();

// Apply authentication and business isolation to all routes
router.use(authenticateToken);
router.use(ensureBusinessIsolation);

/**
 * Get all sales for the business
 */
router.get('/',
  requirePermission('can_view_reports'),
  validate(schemas.query.pagination, 'query'),
  validate(schemas.query.dateRange, 'query'),
  catchAsync(async (req, res) => {
    const { page, limit, sort, order, start_date, end_date, payment_method, status } = req.query;

    // Build query
    const query = { business_id: req.business._id };

    if (start_date && end_date) {
      query.sale_date = {
        $gte: new Date(start_date),
        $lte: new Date(end_date)
      };
    }

    if (payment_method) {
      query['payment.method'] = payment_method;
    }

    if (status) {
      query.status = status;
    }

    // Build sort
    const sortField = sort || 'sale_date';
    const sortOrder = order === 'asc' ? 1 : -1;
    const sortObj = { [sortField]: sortOrder };

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [sales, total] = await Promise.all([
      Sale.find(query)
        .sort(sortObj)
        .skip(skip)
        .limit(limit)
        .populate('user_id', 'name')
        .populate('customer_id', 'name phone'),
      Sale.countDocuments(query)
    ]);

    // Calculate totals
    const totals = await Sale.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          total_revenue: { $sum: '$total' },
          total_items: { $sum: '$total_items' },
          average_sale: { $avg: '$total' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        sales,
        pagination: {
          current_page: page,
          total_pages: Math.ceil(total / limit),
          total_items: total,
          items_per_page: limit,
          has_next: page < Math.ceil(total / limit),
          has_prev: page > 1
        },
        summary: totals[0] || {
          total_revenue: 0,
          total_items: 0,
          average_sale: 0
        }
      }
    });
  })
);

/**
 * Get single sale by ID
 */
router.get('/:id',
  requirePermission('can_view_reports'),
  catchAsync(async (req, res) => {
    const sale = await Sale.findOne({
      _id: req.params.id,
      business_id: req.business._id
    })
    .populate('user_id', 'name')
    .populate('customer_id', 'name phone email')
    .populate('items.product_id', 'name category');

    if (!sale) {
      throw new AppError('Sale not found', 404, 'Mauzo hayajapatikana');
    }

    res.status(200).json({
      success: true,
      data: { sale }
    });
  })
);

/**
 * Record new sale
 */
router.post('/',
  salesLimiter,
  requirePermission('can_record_sales'),
  validateSubscriptionLimits('monthly_sales'),
  enforceBusinessCreation,
  validate(schemas.sale),
  catchAsync(async (req, res) => {
    const { items, customer_id, payment_method, payment_reference, discount, notes, customer_info } = req.body;

    // Validate and process items
    const processedItems = [];
    let subtotal = 0;

    for (const item of items) {
      const product = await Product.findOne({
        _id: item.product_id,
        business_id: req.business._id,
        is_active: true
      });

      if (!product) {
        throw new AppError(`Product not found: ${item.product_id}`, 404,
          `Bidhaa haijapatikana: ${item.product_id}`);
      }

      // Check stock for physical products
      if (!product.is_service && product.stock < item.quantity) {
        throw new AppError(`Insufficient stock for ${product.name}. Available: ${product.stock}`, 400,
          `Hisa haitoshi kwa ${product.name}. Zilizopo: ${product.stock}`);
      }

      const unitPrice = item.unit_price || product.price;
      const totalPrice = item.quantity * unitPrice;

      processedItems.push({
        product_id: product._id,
        product_name: product.name,
        product_sku: product.sku,
        quantity: item.quantity,
        unit_price: unitPrice,
        total_price: totalPrice,
        is_service: product.is_service
      });

      subtotal += totalPrice;
    }

    // Create sale
    const saleData = {
      business_id: req.business._id,
      user_id: req.user._id,
      items: processedItems,
      subtotal,
      payment: {
        method: payment_method,
        reference: payment_reference,
        amount_paid: subtotal, // Will be recalculated after discounts
        status: 'completed'
      },
      notes,
      customer_info
    };

    // Add customer if provided
    if (customer_id) {
      const customer = await Customer.findOne({
        _id: customer_id,
        business_id: req.business._id
      });

      if (customer) {
        saleData.customer_id = customer._id;
        saleData.customer_info = {
          name: customer.name,
          phone: customer.phone
        };
      }
    }

    // Apply discount if provided
    if (discount) {
      saleData.discount = discount;
    }

    const sale = new Sale(saleData);

    // Calculate totals (this happens in pre-save middleware)
    await sale.save();

    // Update product stocks and sales data
    for (const item of processedItems) {
      const product = await Product.findById(item.product_id);

      if (!product.is_service) {
        product.stock -= item.quantity;
      }

      product.sales_data.total_sold += item.quantity;
      product.sales_data.last_sold_date = new Date();
      product.sales_data.revenue_generated += item.total_price;

      await product.save();
    }

    // Update customer purchase history
    if (sale.customer_id) {
      const customer = await Customer.findById(sale.customer_id);
      await customer.addPurchase(sale.total, payment_method);

      // Add favorite products
      for (const item of processedItems) {
        await customer.addFavoriteProduct(item.product_id, item.product_name);
      }
    }

    // Update business stats
    await req.business.updateStats();

    logger.business('Sale recorded', req.business._id, {
      saleId: sale._id,
      saleNumber: sale.sale_number,
      total: sale.total,
      itemsCount: sale.items.length,
      userId: req.user._id
    });

    res.status(201).json({
      success: true,
      message: 'Sale recorded successfully',
      sw: 'Mauzo yamerekodiwa kikamilifu',
      en: 'Sale recorded successfully',
      data: { sale }
    });
  })
);

/**
 * Update sale
 */
router.put('/:id',
  requirePermission('can_edit_sales'),
  validate(schemas.sale),
  catchAsync(async (req, res) => {
    const sale = await Sale.findOne({
      _id: req.params.id,
      business_id: req.business._id
    });

    if (!sale) {
      throw new AppError('Sale not found', 404, 'Mauzo hayajapatikana');
    }

    if (sale.status === 'refunded') {
      throw new AppError('Cannot edit refunded sale', 400,
        'Huwezi kuhariri mauzo yaliyorudishwa');
    }

    // For simplicity, only allow updating notes and customer info
    const allowedUpdates = ['notes', 'customer_info'];
    const updates = {};

    for (const field of allowedUpdates) {
      if (req.body[field] !== undefined) {
        updates[field] = req.body[field];
      }
    }

    Object.assign(sale, updates);
    await sale.save();

    logger.business('Sale updated', req.business._id, {
      saleId: sale._id,
      saleNumber: sale.sale_number,
      userId: req.user._id
    });

    res.status(200).json({
      success: true,
      message: 'Sale updated successfully',
      sw: 'Mauzo yamesasishwa kikamilifu',
      en: 'Sale updated successfully',
      data: { sale }
    });
  })
);

/**
 * Cancel/Refund sale
 */
router.patch('/:id/refund',
  requirePermission('can_delete_sales'),
  validate(require('joi').object({
    refund_amount: require('joi').number().min(0).optional(),
    refund_reason: require('joi').string().max(200).required()
  })),
  catchAsync(async (req, res) => {
    const { refund_amount, refund_reason } = req.body;

    const sale = await Sale.findOne({
      _id: req.params.id,
      business_id: req.business._id
    });

    if (!sale) {
      throw new AppError('Sale not found', 404, 'Mauzo hayajapatikana');
    }

    if (sale.status === 'refunded') {
      throw new AppError('Sale already refunded', 400,
        'Mauzo tayari yamerudishwa');
    }

    const refundAmount = refund_amount || sale.total;

    if (refundAmount > sale.total) {
      throw new AppError('Refund amount cannot exceed sale total', 400,
        'Kiasi cha kurudisha hakiwezi kuzidi jumla ya mauzo');
    }

    // Update sale status
    sale.status = 'refunded';
    sale.refund_info = {
      refunded_amount: refundAmount,
      refund_date: new Date(),
      refund_reason,
      refunded_by: req.user._id
    };

    await sale.save();

    // Restore product stocks
    for (const item of sale.items) {
      if (!item.is_service) {
        const product = await Product.findById(item.product_id);
        if (product) {
          product.stock += item.quantity;
          product.sales_data.total_sold -= item.quantity;
          product.sales_data.revenue_generated -= item.total_price;
          await product.save();
        }
      }
    }

    // Update customer purchase history
    if (sale.customer_id) {
      const customer = await Customer.findById(sale.customer_id);
      if (customer) {
        customer.purchase_history.total_purchases -= 1;
        customer.purchase_history.total_spent -= refundAmount;
        await customer.save();
      }
    }

    // Update business stats
    await req.business.updateStats();

    logger.business('Sale refunded', req.business._id, {
      saleId: sale._id,
      saleNumber: sale.sale_number,
      refundAmount,
      reason: refund_reason,
      userId: req.user._id
    });

    res.status(200).json({
      success: true,
      message: 'Sale refunded successfully',
      sw: 'Mauzo yamerudishwa kikamilifu',
      en: 'Sale refunded successfully',
      data: { sale }
    });
  })
);

/**
 * Get daily sales summary
 */
router.get('/reports/daily',
  requirePermission('can_view_reports'),
  validate(require('joi').object({
    date: require('joi').date().default(() => new Date())
  }), 'query'),
  catchAsync(async (req, res) => {
    const { date } = req.query;
    const targetDate = new Date(date);

    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    const [salesSummary, topProducts, paymentMethods] = await Promise.all([
      // Sales summary
      Sale.aggregate([
        {
          $match: {
            business_id: req.business._id,
            sale_date: { $gte: startOfDay, $lte: endOfDay },
            status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            total_sales: { $sum: 1 },
            total_revenue: { $sum: '$total' },
            total_items: { $sum: '$total_items' },
            average_sale: { $avg: '$total' }
          }
        }
      ]),

      // Top products
      Sale.aggregate([
        {
          $match: {
            business_id: req.business._id,
            sale_date: { $gte: startOfDay, $lte: endOfDay },
            status: 'completed'
          }
        },
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.product_name',
            quantity_sold: { $sum: '$items.quantity' },
            revenue: { $sum: '$items.total_price' }
          }
        },
        { $sort: { quantity_sold: -1 } },
        { $limit: 5 }
      ]),

      // Payment methods
      Sale.aggregate([
        {
          $match: {
            business_id: req.business._id,
            sale_date: { $gte: startOfDay, $lte: endOfDay },
            status: 'completed'
          }
        },
        {
          $group: {
            _id: '$payment.method',
            count: { $sum: 1 },
            total: { $sum: '$total' }
          }
        }
      ])
    ]);

    res.status(200).json({
      success: true,
      data: {
        date: targetDate.toISOString().split('T')[0],
        summary: salesSummary[0] || {
          total_sales: 0,
          total_revenue: 0,
          total_items: 0,
          average_sale: 0
        },
        top_products: topProducts,
        payment_methods: paymentMethods
      }
    });
  })
);

/**
 * Get sales analytics
 */
router.get('/analytics/overview',
  requirePermission('can_view_reports'),
  validate(schemas.query.dateRange, 'query'),
  catchAsync(async (req, res) => {
    const { start_date, end_date, period } = req.query;

    let startDate, endDate;

    if (period) {
      const now = new Date();
      switch (period) {
        case 'today':
          startDate = new Date(now);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
          break;
        case 'week':
          startDate = new Date(now);
          startDate.setDate(now.getDate() - 7);
          endDate = now;
          break;
        case 'month':
          startDate = new Date(now);
          startDate.setMonth(now.getMonth() - 1);
          endDate = now;
          break;
        default:
          startDate = new Date(now);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(now);
          endDate.setHours(23, 59, 59, 999);
      }
    } else {
      startDate = start_date ? new Date(start_date) : new Date();
      endDate = end_date ? new Date(end_date) : new Date();
    }

    const analytics = await Sale.aggregate([
      {
        $match: {
          business_id: req.business._id,
          sale_date: { $gte: startDate, $lte: endDate },
          status: 'completed'
        }
      },
      {
        $group: {
          _id: null,
          total_sales: { $sum: 1 },
          total_revenue: { $sum: '$total' },
          total_items: { $sum: '$total_items' },
          average_sale: { $avg: '$total' },
          payment_methods: { $push: '$payment.method' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        period: { start_date: startDate, end_date: endDate },
        analytics: analytics[0] || {
          total_sales: 0,
          total_revenue: 0,
          total_items: 0,
          average_sale: 0,
          payment_methods: []
        }
      }
    });
  })
);

module.exports = router;
