/**
 * Products Routes
 * Handles product management operations
 */

const express = require('express');
const { Product } = require('../models');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { ensureBusinessIsolation, enforceBusinessCreation, validateSubscriptionLimits } = require('../middleware/businessIsolation');
const { validate, schemas } = require('../middleware/validation');
const { subscriptionBasedLimiter } = require('../middleware/rateLimiter');
const { catchAsync, AppError } = require('../middleware/errorHandler');
// const { cacheConfigs, invalidationPatterns } = require('../middleware/cache');
const logger = require('../config/logger');

const router = express.Router();

// Apply authentication and business isolation to all routes
router.use(authenticateToken);
router.use(ensureBusinessIsolation);

/**
 * Get all products for the business
 */
router.get('/',
  validate(schemas.query.pagination, 'query'),
  // cacheConfigs.products,
  catchAsync(async (req, res) => {
    const { page, limit, sort, order, category, search, in_stock } = req.query;

    // Build query
    const query = { business_id: req.business._id, is_active: true };

    if (category) {
      query.category = category;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } }
      ];
    }

    if (in_stock === 'true') {
      query.$or = [
        { is_service: true },
        { stock: { $gt: 0 } }
      ];
    }

    // Build sort
    const sortField = sort || 'created_at';
    const sortOrder = order === 'asc' ? 1 : -1;
    const sortObj = { [sortField]: sortOrder };

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [products, total] = await Promise.all([
      Product.find(query)
        .sort(sortObj)
        .skip(skip)
        .limit(limit)
        .populate('created_by', 'name')
        .populate('last_updated_by', 'name'),
      Product.countDocuments(query)
    ]);

    res.status(200).json({
      success: true,
      data: {
        products,
        pagination: {
          current_page: page,
          total_pages: Math.ceil(total / limit),
          total_items: total,
          items_per_page: limit,
          has_next: page < Math.ceil(total / limit),
          has_prev: page > 1
        }
      }
    });
  })
);

/**
 * Get single product by ID
 */
router.get('/:id',
  catchAsync(async (req, res) => {
    const product = await Product.findOne({
      _id: req.params.id,
      business_id: req.business._id,
      is_active: true
    })
    .populate('created_by', 'name')
    .populate('last_updated_by', 'name');

    if (!product) {
      throw new AppError('Product not found', 404, 'Bidhaa haijapatikana');
    }

    res.status(200).json({
      success: true,
      data: { product }
    });
  })
);

/**
 * Create new product
 */
router.post('/',
  subscriptionBasedLimiter(50), // 50 requests per 15 minutes based on plan
  requirePermission('can_add_products'),
  validateSubscriptionLimits('products'),
  enforceBusinessCreation,
  validate(schemas.product),
  // invalidationPatterns.products,
  catchAsync(async (req, res) => {
    const productData = {
      ...req.body,
      business_id: req.business._id,
      created_by: req.user._id
    };

    // Check for duplicate SKU within business
    if (productData.sku) {
      const existingProduct = await Product.findOne({
        business_id: req.business._id,
        sku: productData.sku,
        is_active: true
      });

      if (existingProduct) {
        throw new AppError('Product with this SKU already exists', 400,
          'Bidhaa na SKU hii tayari ipo');
      }
    }

    const product = new Product(productData);
    await product.save();

    // Update business stats
    await req.business.updateStats();

    logger.business('Product created', req.business._id, {
      productId: product._id,
      productName: product.name,
      userId: req.user._id
    });

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      sw: 'Bidhaa imeundwa kikamilifu',
      en: 'Product created successfully',
      data: { product }
    });
  })
);

/**
 * Update product
 */
router.put('/:id',
  requirePermission('can_edit_products'),
  validate(schemas.product),
  // invalidationPatterns.products,
  catchAsync(async (req, res) => {
    const product = await Product.findOne({
      _id: req.params.id,
      business_id: req.business._id,
      is_active: true
    });

    if (!product) {
      throw new AppError('Product not found', 404, 'Bidhaa haijapatikana');
    }

    // Check for duplicate SKU if SKU is being changed
    if (req.body.sku && req.body.sku !== product.sku) {
      const existingProduct = await Product.findOne({
        business_id: req.business._id,
        sku: req.body.sku,
        is_active: true,
        _id: { $ne: product._id }
      });

      if (existingProduct) {
        throw new AppError('Product with this SKU already exists', 400,
          'Bidhaa na SKU hii tayari ipo');
      }
    }

    // Update product
    Object.assign(product, req.body);
    product.last_updated_by = req.user._id;
    await product.save();

    logger.business('Product updated', req.business._id, {
      productId: product._id,
      productName: product.name,
      userId: req.user._id
    });

    res.status(200).json({
      success: true,
      message: 'Product updated successfully',
      sw: 'Bidhaa imesasishwa kikamilifu',
      en: 'Product updated successfully',
      data: { product }
    });
  })
);

/**
 * Delete product (soft delete)
 */
router.delete('/:id',
  requirePermission('can_delete_products'),
  // invalidationPatterns.products,
  catchAsync(async (req, res) => {
    const product = await Product.findOne({
      _id: req.params.id,
      business_id: req.business._id,
      is_active: true
    });

    if (!product) {
      throw new AppError('Product not found', 404, 'Bidhaa haijapatikana');
    }

    // Soft delete
    product.is_active = false;
    product.last_updated_by = req.user._id;
    await product.save();

    // Update business stats
    await req.business.updateStats();

    logger.business('Product deleted', req.business._id, {
      productId: product._id,
      productName: product.name,
      userId: req.user._id
    });

    res.status(200).json({
      success: true,
      message: 'Product deleted successfully',
      sw: 'Bidhaa imefutwa kikamilifu',
      en: 'Product deleted successfully'
    });
  })
);

/**
 * Update product stock
 */
router.patch('/:id/stock',
  requirePermission('can_edit_products'),
  validate(require('joi').object({
    stock: require('joi').number().integer().min(0).required(),
    operation: require('joi').string().valid('set', 'add', 'subtract').default('set')
  })),
  catchAsync(async (req, res) => {
    const { stock, operation } = req.body;

    const product = await Product.findOne({
      _id: req.params.id,
      business_id: req.business._id,
      is_active: true
    });

    if (!product) {
      throw new AppError('Product not found', 404, 'Bidhaa haijapatikana');
    }

    if (product.is_service) {
      throw new AppError('Cannot update stock for services', 400,
        'Huwezi kusasisha hisa kwa huduma');
    }

    // Update stock based on operation
    switch (operation) {
      case 'set':
        product.stock = stock;
        break;
      case 'add':
        product.stock += stock;
        break;
      case 'subtract':
        if (product.stock < stock) {
          throw new AppError('Insufficient stock', 400,
            'Hisa haitoshi');
        }
        product.stock -= stock;
        break;
    }

    product.last_updated_by = req.user._id;
    await product.save();

    logger.business('Product stock updated', req.business._id, {
      productId: product._id,
      productName: product.name,
      operation,
      newStock: product.stock,
      userId: req.user._id
    });

    res.status(200).json({
      success: true,
      message: 'Stock updated successfully',
      sw: 'Hisa imesasishwa kikamilifu',
      en: 'Stock updated successfully',
      data: {
        product_id: product._id,
        new_stock: product.stock,
        stock_status: product.stock_status
      }
    });
  })
);

/**
 * Get low stock products
 */
router.get('/alerts/low-stock',
  requirePermission('can_view_reports'),
  catchAsync(async (req, res) => {
    const lowStockProducts = await Product.find({
      business_id: req.business._id,
      is_active: true,
      is_service: false,
      $expr: { $lte: ['$stock', '$low_stock_alert'] }
    }).sort({ stock: 1 });

    res.status(200).json({
      success: true,
      data: {
        products: lowStockProducts,
        count: lowStockProducts.length
      }
    });
  })
);

/**
 * Get product categories
 */
router.get('/meta/categories',
  catchAsync(async (req, res) => {
    const categories = await Product.distinct('category', {
      business_id: req.business._id,
      is_active: true
    });

    res.status(200).json({
      success: true,
      data: { categories }
    });
  })
);

/**
 * Search products
 */
router.get('/search/:query',
  validate(require('joi').object({
    query: require('joi').string().min(1).max(100).required()
  }), 'params'),
  catchAsync(async (req, res) => {
    const { query } = req.params;
    const { limit = 20 } = req.query;

    const products = await Product.find({
      business_id: req.business._id,
      is_active: true,
      $or: [
        { name: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { sku: { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } }
      ]
    })
    .limit(parseInt(limit))
    .sort({ name: 1 });

    res.status(200).json({
      success: true,
      data: {
        products,
        query,
        count: products.length
      }
    });
  })
);

/**
 * Bulk import products (CSV)
 */
router.post('/import',
  requirePermission('can_add_products'),
  validateSubscriptionLimits('products'),
  // TODO: Add multer middleware for file upload
  catchAsync(async (req, res) => {
    // This would handle CSV file upload and parsing
    // For now, return a placeholder response

    res.status(501).json({
      success: false,
      message: 'Bulk import not yet implemented',
      sw: 'Uingizaji wa wingi bado haujaanzishwa',
      en: 'Bulk import not yet implemented'
    });
  })
);

/**
 * Export products (CSV)
 */
router.get('/export',
  requirePermission('can_export_data'),
  catchAsync(async (req, res) => {
    // This would generate and return a CSV file
    // For now, return a placeholder response

    res.status(501).json({
      success: false,
      message: 'Export not yet implemented',
      sw: 'Uhamishaji bado haujaanzishwa',
      en: 'Export not yet implemented'
    });
  })
);

module.exports = router;
