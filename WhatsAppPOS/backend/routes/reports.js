/**
 * Reports Routes
 * Handles business analytics and reporting
 */

const express = require('express');
const mongoose = require('mongoose');
const { Sale, Product, Customer } = require('../models');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { ensureBusinessIsolation } = require('../middleware/businessIsolation');
const { validate, schemas } = require('../middleware/validation');
const { reportLimiter } = require('../middleware/rateLimiter');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const logger = require('../config/logger');

const router = express.Router();

// Apply authentication and business isolation to all routes
router.use(authenticateToken);
router.use(ensureBusinessIsolation);
router.use(requirePermission('can_view_reports'));

/**
 * Get dashboard overview
 */
router.get('/dashboard',
  catchAsync(async (req, res) => {
    const businessId = req.business._id;
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    const [
      todayStats,
      weekStats,
      monthStats,
      topProducts,
      lowStockProducts,
      recentSales
    ] = await Promise.all([
      // Today's statistics
      Sale.aggregate([
        {
          $match: {
            business_id: businessId,
            sale_date: { $gte: startOfDay, $lte: endOfDay },
            status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            total_sales: { $sum: 1 },
            total_revenue: { $sum: '$total' },
            total_items: { $sum: '$total_items' }
          }
        }
      ]),

      // This week's statistics
      Sale.aggregate([
        {
          $match: {
            business_id: businessId,
            sale_date: {
              $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
              $lte: new Date()
            },
            status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            total_sales: { $sum: 1 },
            total_revenue: { $sum: '$total' }
          }
        }
      ]),

      // This month's statistics
      Sale.aggregate([
        {
          $match: {
            business_id: businessId,
            sale_date: {
              $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              $lte: new Date()
            },
            status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            total_sales: { $sum: 1 },
            total_revenue: { $sum: '$total' }
          }
        }
      ]),

      // Top selling products
      Sale.aggregate([
        {
          $match: {
            business_id: businessId,
            sale_date: {
              $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            },
            status: 'completed'
          }
        },
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.product_name',
            quantity_sold: { $sum: '$items.quantity' },
            revenue: { $sum: '$items.total_price' }
          }
        },
        { $sort: { quantity_sold: -1 } },
        { $limit: 5 }
      ]),

      // Low stock products
      Product.find({
        business_id: businessId,
        is_active: true,
        is_service: false,
        $expr: { $lte: ['$stock', '$low_stock_alert'] }
      })
      .limit(10)
      .select('name stock low_stock_alert'),

      // Recent sales
      Sale.find({
        business_id: businessId,
        status: 'completed'
      })
      .sort({ sale_date: -1 })
      .limit(10)
      .select('sale_number total sale_date payment.method')
      .populate('user_id', 'name')
    ]);

    res.status(200).json({
      success: true,
      data: {
        today: todayStats[0] || { total_sales: 0, total_revenue: 0, total_items: 0 },
        week: weekStats[0] || { total_sales: 0, total_revenue: 0 },
        month: monthStats[0] || { total_sales: 0, total_revenue: 0 },
        top_products: topProducts,
        low_stock_products: lowStockProducts,
        recent_sales: recentSales,
        business_stats: req.business.stats
      }
    });
  })
);

/**
 * Get sales report
 */
router.get('/sales',
  reportLimiter,
  validate(schemas.query.dateRange, 'query'),
  catchAsync(async (req, res) => {
    const { start_date, end_date, period, group_by } = req.query;
    const businessId = req.business._id;

    let startDate, endDate;

    // Handle period shortcuts
    if (period) {
      const now = new Date();
      switch (period) {
        case 'today':
          startDate = new Date(now.setHours(0, 0, 0, 0));
          endDate = new Date(now.setHours(23, 59, 59, 999));
          break;
        case 'yesterday':
          const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
          startDate = new Date(yesterday.setHours(0, 0, 0, 0));
          endDate = new Date(yesterday.setHours(23, 59, 59, 999));
          break;
        case 'week':
          startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          endDate = new Date();
          break;
        case 'month':
          startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          endDate = new Date();
          break;
        case 'year':
          startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
          endDate = new Date();
          break;
        default:
          startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          endDate = new Date();
      }
    } else {
      startDate = start_date ? new Date(start_date) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      endDate = end_date ? new Date(end_date) : new Date();
    }

    // Build aggregation pipeline
    const pipeline = [
      {
        $match: {
          business_id: businessId,
          sale_date: { $gte: startDate, $lte: endDate },
          status: 'completed'
        }
      }
    ];

    // Group by date, week, or month if requested
    if (group_by) {
      let groupId;
      switch (group_by) {
        case 'day':
          groupId = {
            year: { $year: '$sale_date' },
            month: { $month: '$sale_date' },
            day: { $dayOfMonth: '$sale_date' }
          };
          break;
        case 'week':
          groupId = {
            year: { $year: '$sale_date' },
            week: { $week: '$sale_date' }
          };
          break;
        case 'month':
          groupId = {
            year: { $year: '$sale_date' },
            month: { $month: '$sale_date' }
          };
          break;
        default:
          groupId = null;
      }

      if (groupId) {
        pipeline.push({
          $group: {
            _id: groupId,
            total_sales: { $sum: 1 },
            total_revenue: { $sum: '$total' },
            total_items: { $sum: '$total_items' },
            average_sale: { $avg: '$total' }
          }
        });
        pipeline.push({ $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } });
      }
    } else {
      pipeline.push({
        $group: {
          _id: null,
          total_sales: { $sum: 1 },
          total_revenue: { $sum: '$total' },
          total_items: { $sum: '$total_items' },
          average_sale: { $avg: '$total' },
          payment_methods: { $push: '$payment.method' }
        }
      });
    }

    const results = await Sale.aggregate(pipeline);

    res.status(200).json({
      success: true,
      data: {
        period: { start_date: startDate, end_date: endDate },
        results: results,
        group_by: group_by || 'total'
      }
    });
  })
);

/**
 * Get product performance report
 */
router.get('/products',
  reportLimiter,
  validate(schemas.query.dateRange, 'query'),
  catchAsync(async (req, res) => {
    const { start_date, end_date, category, limit = 20 } = req.query;
    const businessId = req.business._id;

    const startDate = start_date ? new Date(start_date) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = end_date ? new Date(end_date) : new Date();

    const pipeline = [
      {
        $match: {
          business_id: businessId,
          sale_date: { $gte: startDate, $lte: endDate },
          status: 'completed'
        }
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: {
            product_id: '$items.product_id',
            product_name: '$items.product_name'
          },
          quantity_sold: { $sum: '$items.quantity' },
          revenue: { $sum: '$items.total_price' },
          sales_count: { $sum: 1 },
          average_price: { $avg: '$items.unit_price' }
        }
      },
      { $sort: { quantity_sold: -1 } },
      { $limit: parseInt(limit) }
    ];

    const productPerformance = await Sale.aggregate(pipeline);

    // Get current stock levels
    const productIds = productPerformance.map(p => p._id.product_id);
    const products = await Product.find({
      _id: { $in: productIds },
      business_id: businessId
    }).select('name stock category');

    // Merge stock data
    const enrichedData = productPerformance.map(perf => {
      const product = products.find(p => p._id.toString() === perf._id.product_id.toString());
      return {
        ...perf,
        current_stock: product ? product.stock : 0,
        category: product ? product.category : 'unknown'
      };
    });

    res.status(200).json({
      success: true,
      data: {
        period: { start_date: startDate, end_date: endDate },
        products: enrichedData
      }
    });
  })
);

/**
 * Get customer analytics
 */
router.get('/customers',
  reportLimiter,
  catchAsync(async (req, res) => {
    const businessId = req.business._id;

    const [
      customerStats,
      topCustomers,
      loyaltyDistribution
    ] = await Promise.all([
      // Customer statistics
      Customer.aggregate([
        { $match: { business_id: businessId, is_active: true } },
        {
          $group: {
            _id: null,
            total_customers: { $sum: 1 },
            total_spent: { $sum: '$purchase_history.total_spent' },
            average_spent: { $avg: '$purchase_history.total_spent' },
            total_purchases: { $sum: '$purchase_history.total_purchases' }
          }
        }
      ]),

      // Top customers by spending
      Customer.find({
        business_id: businessId,
        is_active: true
      })
      .sort({ 'purchase_history.total_spent': -1 })
      .limit(10)
      .select('name phone purchase_history loyalty'),

      // Loyalty tier distribution
      Customer.aggregate([
        { $match: { business_id: businessId, is_active: true } },
        {
          $group: {
            _id: '$loyalty.tier',
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    res.status(200).json({
      success: true,
      data: {
        overview: customerStats[0] || {
          total_customers: 0,
          total_spent: 0,
          average_spent: 0,
          total_purchases: 0
        },
        top_customers: topCustomers,
        loyalty_distribution: loyaltyDistribution
      }
    });
  })
);

/**
 * Get inventory report
 */
router.get('/inventory',
  reportLimiter,
  catchAsync(async (req, res) => {
    const businessId = req.business._id;
    const { category, stock_status } = req.query;

    const query = { business_id: businessId, is_active: true };

    if (category) {
      query.category = category;
    }

    if (stock_status) {
      switch (stock_status) {
        case 'low':
          query.$expr = { $lte: ['$stock', '$low_stock_alert'] };
          break;
        case 'out':
          query.stock = 0;
          query.is_service = false;
          break;
        case 'in_stock':
          query.stock = { $gt: 0 };
          query.is_service = false;
          break;
      }
    }

    const [
      products,
      categoryStats,
      stockSummary
    ] = await Promise.all([
      // Product list
      Product.find(query)
        .sort({ name: 1 })
        .select('name stock low_stock_alert price category sales_data'),

      // Category breakdown
      Product.aggregate([
        { $match: { business_id: businessId, is_active: true } },
        {
          $group: {
            _id: '$category',
            count: { $sum: 1 },
            total_value: { $sum: { $multiply: ['$stock', '$price'] } },
            low_stock_count: {
              $sum: {
                $cond: [{ $lte: ['$stock', '$low_stock_alert'] }, 1, 0]
              }
            }
          }
        }
      ]),

      // Stock summary
      Product.aggregate([
        { $match: { business_id: businessId, is_active: true } },
        {
          $group: {
            _id: null,
            total_products: { $sum: 1 },
            total_stock_value: { $sum: { $multiply: ['$stock', '$price'] } },
            low_stock_products: {
              $sum: {
                $cond: [{ $lte: ['$stock', '$low_stock_alert'] }, 1, 0]
              }
            },
            out_of_stock_products: {
              $sum: {
                $cond: [{ $and: [{ $eq: ['$stock', 0] }, { $eq: ['$is_service', false] }] }, 1, 0]
              }
            }
          }
        }
      ])
    ]);

    res.status(200).json({
      success: true,
      data: {
        products,
        category_breakdown: categoryStats,
        summary: stockSummary[0] || {
          total_products: 0,
          total_stock_value: 0,
          low_stock_products: 0,
          out_of_stock_products: 0
        }
      }
    });
  })
);

/**
 * Export report data
 */
router.get('/export/:type',
  requirePermission('can_export_data'),
  validate(require('joi').object({
    type: require('joi').string().valid('sales', 'products', 'customers', 'inventory').required()
  }), 'params'),
  catchAsync(async (req, res) => {
    const { type } = req.params;

    // This would generate CSV/Excel files
    // For now, return a placeholder

    res.status(501).json({
      success: false,
      message: `${type} export not yet implemented`,
      sw: `Uhamishaji wa ${type} bado haujaanzishwa`,
      en: `${type} export not yet implemented`
    });
  })
);

module.exports = router;
