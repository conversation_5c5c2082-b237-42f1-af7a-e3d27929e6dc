/**
 * Webhook Routes
 * Handles incoming WhatsApp messages and other webhook events
 */

const express = require('express');
const { verifyWebhookSignature } = require('../config/twilio');
const { handleWhatsAppMessage } = require('../services/whatsappService');
const { whatsappLimiter } = require('../middleware/rateLimiter');
const logger = require('../config/logger');

const router = express.Router();

/**
 * Webhook signature verification middleware
 */
const verifyTwilioSignature = (req, res, next) => {
  // Skip verification in development
  if (process.env.NODE_ENV === 'development') {
    return next();
  }
  
  const signature = req.headers['x-twilio-signature'];
  const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
  const body = req.body;
  
  if (!verifyWebhookSignature(signature, url, body)) {
    logger.security('Invalid webhook signature', {
      signature,
      url,
      ip: req.ip
    });
    
    return res.status(403).json({
      error: 'Invalid signature'
    });
  }
  
  next();
};

/**
 * WhatsApp webhook endpoint
 * Handles incoming messages from Twilio WhatsApp
 */
router.post('/whatsapp', 
  whatsappLimiter,
  verifyTwilioSignature,
  async (req, res) => {
    try {
      logger.whatsapp('WhatsApp webhook received', {
        body: req.body,
        headers: req.headers,
        ip: req.ip
      });
      
      // Handle the message
      await handleWhatsAppMessage(req, res);
      
    } catch (error) {
      logger.error('WhatsApp webhook error:', error);
      res.status(500).json({
        error: 'Internal server error'
      });
    }
  }
);

/**
 * WhatsApp webhook verification (GET request)
 * Used by Twilio to verify the webhook URL
 */
router.get('/whatsapp', (req, res) => {
  const { hub } = req.query;
  
  logger.info('WhatsApp webhook verification request', {
    query: req.query,
    ip: req.ip
  });
  
  // Echo back the hub.challenge value
  if (hub && hub['hub.challenge']) {
    res.status(200).send(hub['hub.challenge']);
  } else {
    res.status(200).send('WhatsApp webhook endpoint is active');
  }
});

/**
 * WhatsApp status webhook
 * Handles message delivery status updates
 */
router.post('/whatsapp/status',
  verifyTwilioSignature,
  (req, res) => {
    try {
      const { MessageSid, MessageStatus, ErrorCode, ErrorMessage } = req.body;
      
      logger.whatsapp('WhatsApp status update', {
        messageSid: MessageSid,
        status: MessageStatus,
        errorCode: ErrorCode,
        errorMessage: ErrorMessage
      });
      
      // TODO: Update message status in database if needed
      
      res.status(200).send('OK');
      
    } catch (error) {
      logger.error('WhatsApp status webhook error:', error);
      res.status(500).send('Error');
    }
  }
);

/**
 * General webhook health check
 */
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'webhook',
    timestamp: new Date().toISOString(),
    endpoints: {
      whatsapp: '/webhook/whatsapp',
      status: '/webhook/whatsapp/status'
    }
  });
});

/**
 * Test webhook endpoint for development
 */
if (process.env.NODE_ENV === 'development') {
  router.post('/test/whatsapp', async (req, res) => {
    try {
      const testMessage = {
        From: req.body.From || 'whatsapp:+255700000000',
        Body: req.body.Body || 'test message',
        MessageSid: 'TEST_' + Date.now()
      };
      
      req.body = testMessage;
      
      await handleWhatsAppMessage(req, res);
      
    } catch (error) {
      logger.error('Test webhook error:', error);
      res.status(500).json({ error: error.message });
    }
  });
  
  router.get('/test/whatsapp', (req, res) => {
    res.status(200).json({
      message: 'WhatsApp test endpoint',
      usage: 'POST with { "From": "whatsapp:+255...", "Body": "message" }',
      example: {
        From: 'whatsapp:+255700000000',
        Body: 'msaada'
      }
    });
  });
}

module.exports = router;
