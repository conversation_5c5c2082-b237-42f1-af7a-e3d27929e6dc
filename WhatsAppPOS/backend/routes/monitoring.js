/**
 * Monitoring and Performance Routes
 * Provides endpoints for monitoring application health and performance
 */

const express = require('express');
const router = express.Router();
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { catchAsync } = require('../middleware/errorHandler');
const { performanceMonitor, getMonitoringStatus } = require('../config/monitoring');
const logger = require('../config/logger');
const LogAggregationService = require('../services/logAggregationService');
const cacheService = require('../services/cacheService');

// Initialize log aggregation service
const logAggregation = new LogAggregationService();

/**
 * Get application monitoring status
 * GET /api/monitoring/status
 */
router.get('/status',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const status = getMonitoringStatus();
    
    res.json({
      success: true,
      data: status
    });
  })
);

/**
 * Get performance metrics
 * GET /api/monitoring/metrics
 */
router.get('/metrics',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const metrics = performanceMonitor.getMetrics();
    
    res.json({
      success: true,
      data: metrics
    });
  })
);

/**
 * Get performance alerts
 * GET /api/monitoring/alerts
 */
router.get('/alerts',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const alerts = performanceMonitor.getAlerts();
    
    res.json({
      success: true,
      data: alerts
    });
  })
);

/**
 * Clear performance metrics and alerts
 * POST /api/monitoring/clear
 */
router.post('/clear',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    performanceMonitor.clear();
    
    logger.info('Performance metrics cleared', {
      userId: req.user._id,
      businessId: req.business._id
    });
    
    res.json({
      success: true,
      message: 'Performance metrics and alerts cleared'
    });
  })
);

/**
 * Get system health check
 * GET /api/monitoring/health
 */
router.get('/health',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const health = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      environment: process.env.NODE_ENV,
      version: require('../../package.json').version,
      
      // Database health
      database: {
        connected: require('mongoose').connection.readyState === 1,
        name: require('mongoose').connection.name,
        host: require('mongoose').connection.host,
        port: require('mongoose').connection.port
      },
      
      // External services health
      services: {
        twilio: {
          configured: !!(process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN),
          webhook_url: process.env.WEBHOOK_URL || 'not configured'
        },
        sentry: {
          configured: !!process.env.SENTRY_DSN,
          environment: process.env.NODE_ENV
        },
        backup: {
          enabled: process.env.BACKUP_ENABLED === 'true',
          s3_configured: !!(process.env.AWS_ACCESS_KEY_ID && process.env.BACKUP_S3_BUCKET)
        }
      }
    };
    
    res.json({
      success: true,
      data: health
    });
  })
);

/**
 * Test error reporting
 * POST /api/monitoring/test-error
 */
router.post('/test-error',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const { type = 'test' } = req.body;
    
    logger.info('Test error triggered', {
      type,
      userId: req.user._id,
      businessId: req.business._id
    });
    
    switch (type) {
      case 'error':
        throw new Error('Test error for monitoring system');
      
      case 'warning':
        logger.warn('Test warning for monitoring system', {
          test: true,
          userId: req.user._id
        });
        break;
      
      case 'performance':
        // Simulate slow operation
        await new Promise(resolve => setTimeout(resolve, 3000));
        break;
      
      default:
        logger.info('Test log message for monitoring system', {
          test: true,
          userId: req.user._id
        });
    }
    
    res.json({
      success: true,
      message: `Test ${type} completed`
    });
  })
);

/**
 * Get application logs (recent)
 * GET /api/monitoring/logs
 */
router.get('/logs',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const { level = 'info', limit = 100 } = req.query;
    
    // This is a simplified implementation
    // In production, you might want to use a proper log aggregation service
    const logs = {
      message: 'Log retrieval not implemented in this version',
      suggestion: 'Use external log aggregation service like ELK stack or Datadog',
      available_endpoints: [
        '/api/monitoring/status',
        '/api/monitoring/metrics',
        '/api/monitoring/alerts',
        '/api/monitoring/health'
      ]
    };
    
    res.json({
      success: true,
      data: logs
    });
  })
);

/**
 * Export performance report
 * GET /api/monitoring/report
 */
router.get('/report',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const { format = 'json' } = req.query;
    
    const report = {
      generated_at: new Date().toISOString(),
      period: '24h',
      system: getMonitoringStatus(),
      metrics: performanceMonitor.getMetrics(),
      alerts: performanceMonitor.getAlerts(),
      summary: {
        total_requests: Object.values(performanceMonitor.getMetrics())
          .filter(m => m.operation === 'api_request')
          .reduce((sum, m) => sum + m.count, 0),
        avg_response_time: Object.values(performanceMonitor.getMetrics())
          .filter(m => m.operation === 'api_request')
          .reduce((sum, m) => sum + m.avg, 0) / 
          Object.values(performanceMonitor.getMetrics())
            .filter(m => m.operation === 'api_request').length || 0,
        error_count: performanceMonitor.getAlerts().length,
        uptime: process.uptime()
      }
    };
    
    if (format === 'csv') {
      // Simple CSV export
      const csv = [
        'Metric,Count,Average,Min,Max',
        ...Object.entries(performanceMonitor.getMetrics()).map(([key, metric]) =>
          `${key},${metric.count},${metric.avg},${metric.min},${metric.max}`
        )
      ].join('\n');
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=performance-report.csv');
      res.send(csv);
    } else {
      res.json({
        success: true,
        data: report
      });
    }
  })
);

/**
 * Search application logs
 * GET /api/monitoring/logs/search
 */
router.get('/logs/search',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const criteria = {
      logType: req.query.logType || 'app',
      level: req.query.level,
      startDate: req.query.startDate,
      endDate: req.query.endDate,
      userId: req.query.userId,
      businessId: req.query.businessId,
      message: req.query.message,
      limit: parseInt(req.query.limit) || 100,
      offset: parseInt(req.query.offset) || 0
    };

    const results = await logAggregation.searchLogs(criteria);

    res.json({
      success: true,
      data: results
    });
  })
);

/**
 * Get log statistics
 * GET /api/monitoring/logs/stats
 */
router.get('/logs/stats',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const timeRange = req.query.timeRange || '24h';
    const stats = await logAggregation.getLogStatistics(timeRange);

    res.json({
      success: true,
      data: stats
    });
  })
);

/**
 * Get recent errors
 * GET /api/monitoring/logs/errors
 */
router.get('/logs/errors',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const limit = parseInt(req.query.limit) || 50;
    const errors = await logAggregation.getRecentErrors(limit);

    res.json({
      success: true,
      data: errors
    });
  })
);

/**
 * Get performance insights
 * GET /api/monitoring/logs/performance
 */
router.get('/logs/performance',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const timeRange = req.query.timeRange || '24h';
    const insights = await logAggregation.getPerformanceInsights(timeRange);

    res.json({
      success: true,
      data: insights
    });
  })
);

/**
 * Export logs
 * GET /api/monitoring/logs/export
 */
router.get('/logs/export',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const criteria = {
      logType: req.query.logType || 'app',
      level: req.query.level,
      startDate: req.query.startDate,
      endDate: req.query.endDate,
      userId: req.query.userId,
      businessId: req.query.businessId,
      message: req.query.message
    };

    const format = req.query.format || 'json';
    const exportData = await logAggregation.exportLogs(criteria, format);

    if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=logs-export.csv');
      res.send(exportData);
    } else {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=logs-export.json');
      res.send(exportData);
    }
  })
);

/**
 * Get cache statistics
 * GET /api/monitoring/cache/stats
 */
router.get('/cache/stats',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const stats = await cacheService.getStats();

    res.json({
      success: true,
      data: stats
    });
  })
);

/**
 * Cache health check
 * GET /api/monitoring/cache/health
 */
router.get('/cache/health',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const health = await cacheService.healthCheck();

    res.json({
      success: true,
      data: health
    });
  })
);

/**
 * Clear cache for business
 * POST /api/monitoring/cache/clear
 */
router.post('/cache/clear',
  authenticateToken,
  requirePermission('admin'),
  catchAsync(async (req, res) => {
    const businessId = req.user.business_id || req.business._id;
    const { pattern } = req.body;

    let cleared = 0;
    if (pattern) {
      cleared = await cacheService.deletePattern(businessId, pattern);
    } else {
      cleared = await cacheService.clearBusiness(businessId);
    }

    logger.info('Cache cleared', {
      businessId,
      pattern: pattern || 'all',
      cleared,
      userId: req.user._id
    });

    res.json({
      success: true,
      message: `Cleared ${cleared} cache entries`,
      data: { cleared, pattern: pattern || 'all' }
    });
  })
);

module.exports = router;
