require('dotenv').config();

console.log('Testing route imports...');

try {
  console.log('Testing auth routes...');
  const authRoutes = require('./backend/routes/auth');
  console.log('✓ Auth routes loaded');
} catch (error) {
  console.error('✗ Auth routes failed:', error.message);
}

try {
  console.log('Testing business routes...');
  const businessRoutes = require('./backend/routes/businesses');
  console.log('✓ Business routes loaded');
} catch (error) {
  console.error('✗ Business routes failed:', error.message);
}

try {
  console.log('Testing product routes...');
  const productRoutes = require('./backend/routes/products');
  console.log('✓ Product routes loaded');
} catch (error) {
  console.error('✗ Product routes failed:', error.message);
}

try {
  console.log('Testing sales routes...');
  const salesRoutes = require('./backend/routes/sales');
  console.log('✓ Sales routes loaded');
} catch (error) {
  console.error('✗ Sales routes failed:', error.message);
}

try {
  console.log('Testing customer routes...');
  const customerRoutes = require('./backend/routes/customers');
  console.log('✓ Customer routes loaded');
} catch (error) {
  console.error('✗ Customer routes failed:', error.message);
}

try {
  console.log('Testing report routes...');
  const reportRoutes = require('./backend/routes/reports');
  console.log('✓ Report routes loaded');
} catch (error) {
  console.error('✗ Report routes failed:', error.message);
}

try {
  console.log('Testing user routes...');
  const userRoutes = require('./backend/routes/users');
  console.log('✓ User routes loaded');
} catch (error) {
  console.error('✗ User routes failed:', error.message);
}

try {
  console.log('Testing webhook routes...');
  const webhookRoutes = require('./backend/routes/webhook');
  console.log('✓ Webhook routes loaded');
} catch (error) {
  console.error('✗ Webhook routes failed:', error.message);
}

console.log('Route testing completed');
